http:
  services:
    redi-microservice-listing:
      loadBalancer:
        passHostHeader: false
        servers:
          - url: 'https://DOCKER_REPO.internal.{{ env "CONTAINER_APP_ENV_DNS_SUFFIX" }}'
  middlewares:
    redirecthttps:
      redirectScheme:
        scheme: https
    listingapi-stripprefix:
      stripPrefix:
        prefixes:
          - "/listingapi"
        forceSlash: false
  routers:
    listingapisecure:
      rule: "PathPrefix(`/listingapi`)"
      middlewares:
        - "listingapi-stripprefix"
      service: redi-microservice-listing
