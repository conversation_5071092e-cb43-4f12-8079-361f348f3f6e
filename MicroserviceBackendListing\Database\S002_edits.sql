﻿/************************************************************************************************
Update to tables that are created in S001.
The changes must also be made to the original sql in s001. This file is to apply the changes to existing only.
**************************************************************************************************/
IF INDEXPROPERTY ( object_ID('listing.ListingAttribute'), 'IX_ListingAttribute_Filt_VGeography_4' , 'IndexID' ) IS NULL
BEGIN
    CREATE SPATIAL INDEX [IX_ListingAttribute_Filt_VGeography_4] ON [listing].[ListingAttribute] 
     (
      [ValueGeography]
     )
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes t WHERE t.name='IX_Listing_FI_StatusActive_Type0')
BEGIN

    CREATE NONCLUSTERED INDEX [IX_Listing_FI_StatusActive_Type0] ON [listing].[Listing] 
     (
      [Subject] ASC
     )
     INCLUDE (
      [ListingId]
     )
     WHERE StatusCode = 'Active' AND Deleted = 0 AND ListingTypeId = 0;

    CREATE NONCLUSTERED INDEX [IX_Listing_FI_StatusActive_Type1] ON [listing].[Listing] 
     (
      [Subject] ASC
     )
     INCLUDE (
      [ListingId]
     )
     WHERE StatusCode = 'Active' AND Deleted = 0 AND ListingTypeId = 1;

    CREATE NONCLUSTERED INDEX [IX_Listing_FI_StatusActive_Type2] ON [listing].[Listing] 
     (
      [Subject] ASC
     )
     INCLUDE (
      [ListingId]
     )
     WHERE StatusCode = 'Active' AND Deleted = 0 AND ListingTypeId = 2;

    CREATE NONCLUSTERED INDEX [IX_Listing_FI_StatusActive_Type3] ON [listing].[Listing] 
     (
      [Subject] ASC
     )
     INCLUDE (
      [ListingId]
     )
     WHERE StatusCode = 'Active' AND Deleted = 0 AND ListingTypeId = 3;

    CREATE NONCLUSTERED INDEX [IX_Listing_FI_StatusActive_type4] ON [listing].[Listing] 
     (
      [Subject] ASC
     )
     INCLUDE (
      [ListingId]
     )
     WHERE StatusCode = 'Active' AND Deleted = 0 AND ListingTypeId = 4;

 END
 GO



IF (OBJECT_ID('listing.FK_Listing_ListingStatus', 'F') IS NULL)
BEGIN

    ALTER TABLE [listing].[Listing]
        ADD CONSTRAINT [FK_Listing_ListingStatus] FOREIGN KEY ([StatusCode])  REFERENCES [listing].[ListingStatus]([StatusCode]);
END

IF COL_LENGTH('listing.Listing', 'ParentListingId') IS NULL
BEGIN
    ALTER TABLE [listing].[Listing]
        ADD [ParentListingId] bigint NULL;
    ALTER TABLE [listing].[Listing]
        ADD CONSTRAINT [FK_Listing_ParentListingId] FOREIGN KEY ([ParentListingId])  REFERENCES [listing].[Listing]([ListingId]);

    EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'The parent listing this listing belongs to. Optional.
    Allows listings to have child listings. The child listings could serve other purposes.',
    @level0type = N'SCHEMA', @level0name = N'listing',
    @level1type = N'TABLE', @level1name = N'Listing',
    @level2type=N'COLUMN', @level2name=N'ParentListingId';

    ALTER TABLE [listing].[Listing]
        ADD [SortOrder] int NULL;

    EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'Optional sort order so listing order can be explicitly set.',
    @level0type = N'SCHEMA', @level0name = N'listing',
    @level1type = N'TABLE', @level1name = N'Listing',
    @level2type=N'COLUMN', @level2name=N'SortOrder';
    CREATE NONCLUSTERED INDEX [IX_Listing_ParentListingIdListingTypeIdSortOrder] ON [listing].[Listing] 
     (
      [ParentListingId] ASC, 
      [ListingTypeId] ASC, 
      [SortOrder] ASC
     );
END

IF COL_LENGTH('listing.Attribute', 'IsRequired') IS NULL
BEGIN

    ALTER TABLE [listing].[Attribute]
        ADD [IsRequired] bit NULL,
         [NumericMinValue] decimal NULL,
         [NumericMaxValue] decimal NULL,
         [TextMinCharacters] int NULL,
         [TextMaxCharacters] int NULL,
         [MinDateDaysFromToday] int NULL,
         [MaxDateDaysFromToday] int NULL,
         [MinSelectionsRequired] int NULL,
         [MaxSelectionsAllowed] int NULL,
         [MaxStars] int NULL,
         [HelpText] nvarchar(1000) NULL;

    ALTER TABLE [listing].[DisplayContainerAttribute]
        ADD [DefaultTextValue] nvarchar(100) NULL,
         [DefaultNumericValue] decimal NULL,
         [DefaultDateAddDays] int NULL,
         [DefaultDateAddMonths] int NULL,
         [DefaultTimeAddMinutes] int NULL
END

IF COL_LENGTH('listing.listing', 'VisibilityClaimId') IS NULL
    ALTER TABLE [listing].[Listing]
        ADD VisibilityClaimId uniqueidentifier NULL;
GO

IF COL_LENGTH('listing.Listing', 'ParentEntityIntId') IS NULL
    BEGIN
    ALTER TABLE [listing].[Listing]
    ADD [ParentEntityIntId] bigint NULL;

     CREATE NONCLUSTERED INDEX [IX_Listing_Parent_2] ON [listing].[Listing] 
     (
      [ParentEntityIntId] ASC, 
      [ParentEntityType] ASC, 
      [ListingTypeId] ASC, 
      [ToDate] ASC, 
      [FromDate] ASC, 
      [StatusCode] ASC
     );

END
GO

IF COL_LENGTH('listing.ListingType', 'NonOwnerEditClaimId') IS NULL
BEGIN
    ALTER TABLE [listing].[ListingType]
    ADD [NonOwnerEditClaimId]     uniqueidentifier NULL;
END
GO

IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='listing' and t.name='TagSetGroup')
BEGIN
    EXEC sys.sp_updateextendedproperty
    @name=N'MS_Description', @value=N'A tag is a text string (frequently with no spaces) that can assign meaning or context to something. Useful for searching and filtering.', 
    @level0type=N'SCHEMA', @level0name=N'listing', 
    @level1type=N'TABLE', @level1name=N'Tag';

    ALTER TABLE [listing].[TagSet]
        ADD [SortOrder] int NULL;

    ALTER TABLE [listing].[TagSet]
        ADD [IsEnabled] bit NOT NULL CONSTRAINT [DF_TagSet_IsEnabled] DEFAULT 1;

    ALTER TABLE [listing].[TagSet]
        ADD [ParentTagSetId] int NULL;

    EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'Optional Parent TagSetId. Supports nesting tag sets',
    @level0type = N'SCHEMA', @level0name = N'listing',
    @level1type = N'TABLE', @level1name = N'TagSet',
    @level2type=N'COLUMN', @level2name=N'ParentTagSetId';

    ALTER TABLE [listing].[TagSet]
        ADD [Note] nvarchar(1000) NULL;

    EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'Additional information about a tag set that could be shown when a user hovers over a tag. set name',
    @level0type = N'SCHEMA', @level0name = N'listing',
    @level1type = N'TABLE', @level1name = N'TagSet',
    @level2type=N'COLUMN', @level2name=N'Note';

    ALTER TABLE [listing].[TagSet]
        ADD [Icon] varchar(400) NULL;

    EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'Specifies the icon to be displayed on the UI.
    Optional.',
    @level0type = N'SCHEMA', @level0name = N'listing',
    @level1type = N'TABLE', @level1name = N'TagSet',
    @level2type=N'COLUMN', @level2name=N'Icon';

    ALTER TABLE [listing].[Tag]
        ADD [Note] nvarchar(1000) NULL;

    EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'Additional information about a tag that could be shown when a user hovers over a tag.',
    @level0type = N'SCHEMA', @level0name = N'listing',
    @level1type = N'TABLE', @level1name = N'Tag',
    @level2type=N'COLUMN', @level2name=N'Note';

    ALTER TABLE [listing].[Tag]
        ADD [Icon] varchar(400) NULL;

    EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'Specifies the icon to be displayed on the UI.
    Optional.',
    @level0type = N'SCHEMA', @level0name = N'listing',
    @level1type = N'TABLE', @level1name = N'Tag',
    @level2type=N'COLUMN', @level2name=N'Icon';

    ALTER TABLE [listing].[Tag]
        ADD [Colour] varchar(20) NULL;

    EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'Optional colour code that can be used in the UI to colour a tag.',
    @level0type = N'SCHEMA', @level0name = N'listing',
    @level1type = N'TABLE', @level1name = N'Tag',
    @level2type=N'COLUMN', @level2name=N'Colour';

    ALTER TABLE [listing].[Tag]
        ADD [ImageUrl] varchar(2000) NULL;

    EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'Optional Image URL that can be used to show an image for a tag.',
    @level0type = N'SCHEMA', @level0name = N'listing',
    @level1type = N'TABLE', @level1name = N'Tag',
    @level2type=N'COLUMN', @level2name=N'ImageUrl';

    CREATE NONCLUSTERED INDEX [IX_TagSet_ParentTagSetIsEnabled] ON [listing].[TagSet] 
     (
      [ParentTagSetId] ASC, 
      [IsEnabled] ASC
     )
     INCLUDE (
      [TagSetId], 
      [Label], 
      [SortOrder]
     );

    CREATE NONCLUSTERED INDEX [IX_TagSet_TagSetGroupIsEnabled] ON [listing].[TagSet] 
     (
      [IsEnabled] ASC
     )
     INCLUDE (
      [TagSetId], 
      [Label], 
      [SortOrder], 
      [ParentTagSetId]
     );


    ALTER TABLE [listing].[TagSet]
        ADD CONSTRAINT [FK_TagSet_ParentTagSetId] FOREIGN KEY ([ParentTagSetId])  REFERENCES [listing].[TagSet]([TagSetId]);

    CREATE NONCLUSTERED INDEX [IX_ListingMedia_MediaUrlListingId] ON [listing].[ListingMedia] 
     (
      [MediaUrl] ASC,
      [ListingId] ASC
     )
END
