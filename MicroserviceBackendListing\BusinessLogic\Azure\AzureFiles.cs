using MicroserviceBackendListing.Interfaces;
using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.Blob;

namespace MicroserviceBackendListing.BusinessLogic.Azure
{
	internal class AzureFiles : ICloudStorage
	{
		private readonly IConfiguration configuration;

        public AzureFiles(IConfiguration configuration)
		{
			this.configuration = configuration;
        }

		public async Task<string> UploadFileAsync(string path, string cloudFolderName, string cloudFileName, bool isPublic, bool deleteOncomplete = true)
		{
			string fileUrl = "";
			using (FileStream stream = new FileStream(path, FileMode.Open, FileAccess.Read))
			{
                fileUrl = await UploadStreamAsync(stream, cloudFolderName, cloudFileName, isPublic);
			}
			if (deleteOncomplete)
			{
				System.IO.File.Delete(path);
			}
			return fileUrl;
		}

		public async Task<string> UploadStreamAsync(Stream dataStream, string cloudFolderName, string cloudFileName, bool isPublic)
		{
			var account = Authenticate();

			// Create the CloudBlobClient that represents the 
			// Blob storage endpoint for the storage account.
			CloudBlobClient cloudBlobClient = account.CreateCloudBlobClient();
			CloudBlobContainer cloudBlobContainer = cloudBlobClient.GetContainerReference(Config.AzureContainer);
			await cloudBlobContainer.CreateIfNotExistsAsync();

			BlobContainerPermissions permissions = new BlobContainerPermissions
			{
				PublicAccess = isPublic ? BlobContainerPublicAccessType.Blob : BlobContainerPublicAccessType.Off
			};

			await cloudBlobContainer.SetPermissionsAsync(permissions);

			// Get a reference to the blob address, then upload the file to the blob.
			// Use the value of localFileName for the blob name.
			CloudBlockBlob cloudBlockBlob = cloudBlobContainer.GetBlockBlobReference(cloudFolderName + "/" + cloudFileName);
			await cloudBlockBlob.UploadFromStreamAsync(dataStream);
			
			return cloudBlockBlob.Uri.AbsoluteUri;
        }


		public async Task<Uri> GetAuthenticatedUrl(string url, CloudItemPermission permissions)
        {
            var fileUri = new Uri(url);
			var cloudFileName = fileUri.AbsolutePath.Split('/')[fileUri.AbsolutePath.Split('/').Length - 1];
            var account = Authenticate();
			CloudBlobClient cloudBlobClient = account.CreateCloudBlobClient();
			CloudBlobContainer cloudBlobContainer = cloudBlobClient.GetContainerReference(Config.AzureContainer);
			await cloudBlobContainer.CreateIfNotExistsAsync();
			CloudBlockBlob cloudBlockBlob = cloudBlobContainer.GetBlockBlobReference(fileUri.AbsolutePath.Substring(fileUri.AbsolutePath.IndexOf('/',1) + 1));

			//Create an ad-hoc Shared Access Policy with read permissions which will expire in 1 week
			SharedAccessBlobPolicy policy = new SharedAccessBlobPolicy()
			{
				SharedAccessExpiryTime = DateTime.UtcNow.AddDays(7),
			};

			if (permissions == CloudItemPermission.None)
			{
				policy.Permissions = SharedAccessBlobPermissions.None;
			}
			else
			{
				// add permission flags
				if (permissions.HasFlag(CloudItemPermission.Read))
				{
					policy.Permissions |= SharedAccessBlobPermissions.Read;
				}
				if (permissions.HasFlag(CloudItemPermission.Write))
				{
					policy.Permissions |= SharedAccessBlobPermissions.Write | SharedAccessBlobPermissions.Create | SharedAccessBlobPermissions.Add;
				}
				if (permissions.HasFlag(CloudItemPermission.Delete))
				{
					policy.Permissions |= SharedAccessBlobPermissions.Delete;
				}
			}

			SharedAccessBlobHeaders headers = new SharedAccessBlobHeaders()
			{
				ContentDisposition = string.Format("attachment;filename=\"{0}\"", cloudFileName),
			};
			var sasToken = cloudBlockBlob.GetSharedAccessSignature(policy, headers);
			var blobUrl = cloudBlockBlob.Uri.AbsoluteUri + sasToken;

			return new Uri(blobUrl);
		}

		CloudStorageAccount Authenticate()
		{
			string storageConnectionString = configuration["AzureConnectionString"] ?? "";

			// Check whether the connection string can be parsed.
			CloudStorageAccount storageAccount;
			if (CloudStorageAccount.TryParse(storageConnectionString, out storageAccount))
			{
				// If the connection string is valid, proceed with operations against Blob
				// storage here.
				return storageAccount;
			}
			else
			{
				throw new Exception("Invalid azure blob connection string");
			}
		}

		public async Task Delete(string url)
		{
			var fileUri = new Uri(url);
            var account = Authenticate();

			// Create the CloudBlobClient that represents the 
			// Blob storage endpoint for the storage account.
			CloudBlobClient cloudBlobClient = account.CreateCloudBlobClient();
			CloudBlobContainer cloudBlobContainer = cloudBlobClient.GetContainerReference(Config.AzureContainer);
			await cloudBlobContainer.CreateIfNotExistsAsync();

			CloudBlockBlob cloudBlockBlob = cloudBlobContainer.GetBlockBlobReference(fileUri.AbsolutePath);
			await cloudBlockBlob.DeleteIfExistsAsync();
		}
	}
}