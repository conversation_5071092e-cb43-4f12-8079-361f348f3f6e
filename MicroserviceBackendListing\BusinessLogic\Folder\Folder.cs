﻿using MicroserviceBackendListing.BusinessLogic.Base;

using MicroserviceContract.Dtos.ListingManagement;
using Microsoft.Extensions.Caching.Memory;
using Redi.Prime3.MicroService.BaseLib;
using Sql;

namespace MicroserviceBackendListing.BusinessLogic
{
    /// <summary>
    /// 
    /// </summary>
    public class Folder : BusinessLogicBase
    {
        private UtilityFunctions _utils;
        private Func<ManageListing> _manageListingFactory;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="unitOfWork"></param>
        /// <param name="memoryCache"></param>
        /// <param name="utils"></param>
        /// <param name="manageListingFactory"></param>
        public Folder(IUnitOfWork unitOfWork, IMemoryCache memoryCache, UtilityFunctions utils, Func<ManageListing> manageListingFactory)
        {
            _unitOfWork = unitOfWork;
            _memoryCache = memoryCache;
            _manageListingFactory = manageListingFactory;
            _utils = utils;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="listingIds"></param>
        /// <param name="parentListingId"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        internal async Task BulkAddListingsAsync(List<long> listingIds, long parentListingId)
        {
            if (listingIds.Count < 0) throw new ArgumentException(nameof(listingIds));
            if (parentListingId < 0) throw new ArgumentException(nameof(parentListingId));
            var logic = _manageListingFactory();
            var folderListing = await logic.GetListingByListingId(parentListingId);
            
            foreach (var listingId in listingIds)
            {
                var listing = await logic.GetListingByListingId(listingId, includeAttributes: true, displayContainerCodesArray: new string[] { "ListingDetail" });
                listing.ParentListingId = parentListingId;

                var containers = listing.Attributes;
                if (containers != null) {
                    var detailContainer = containers.FirstOrDefault(x => x.DisplayContainerCode == "ListingDetail");
                    if (detailContainer != null) {
                        var attributes = detailContainer.Attributes;
                        var folderAttribute = attributes?.FirstOrDefault(x => x.AttributeCode == "FolderName");
                        if (folderAttribute != null)
                        {
                            folderAttribute.ValueString = folderListing.Subject;
                        }
                        else
                        {
                            folderAttribute = new ManageListingAttributeCDto();
                            folderAttribute.ValueString = folderListing.Subject;
                            folderAttribute.AttributeCode = "FolderName";
                            if (detailContainer.Attributes == null) { detailContainer.Attributes = []; }
                            detailContainer.Attributes.Add(folderAttribute);
                        }
                        await logic.UpdateListingWithDisplayContainers(listing);
                    }
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="listingIds"></param>
        /// <param name="parentListingId"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        internal async Task BulkRemoveListingsAsync(List<long> listingIds, long parentListingId)
        {
            if (listingIds.Count < 0) throw new ArgumentException(nameof(listingIds));
            if (parentListingId < 0) throw new ArgumentException(nameof(parentListingId));
            var logic = _manageListingFactory();
            var folderListing = await logic.GetListingByListingId(parentListingId);
            if (folderListing != null)
            {
                foreach (var listingId in listingIds)
                {
                    var listing = await logic.GetListingByListingId(listingId, includeAttributes: true, displayContainerCodesArray: new string[] { "ListingDetail" });
                    listing.ParentListingId = null;

                    var containers = listing.Attributes;
                    if (containers != null)
                    {
                        var detailContainer = containers.FirstOrDefault(x => x.DisplayContainerCode == "ListingDetail");
                        if (detailContainer != null)
                        {
                            var attributes = detailContainer.Attributes;
                            var folderAttribute = attributes?.FirstOrDefault(x => x.AttributeCode == "FolderName");
                            if (folderAttribute != null)
                            {
                                folderAttribute.ValueString = "";
                            }
                            else
                            {
                                folderAttribute = new ManageListingAttributeCDto();
                                folderAttribute.ValueString = "";
                                folderAttribute.AttributeCode = "FolderName";
                                if (detailContainer.Attributes == null) { detailContainer.Attributes = []; }
                                detailContainer.Attributes.Add(folderAttribute);
                            }
                            await logic.UpdateListingWithDisplayContainers(listing);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="listingId"></param>
        /// <param name="subject"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        internal async Task FolderRenameAsync(long listingId, string subject)
        {
            if (listingId < 0) throw new ArgumentException(nameof(listingId));
            if (string.IsNullOrEmpty(subject)) throw new ArgumentException(nameof(subject));
            var logic = _manageListingFactory();
            var folderListing = await logic.GetListingByListingId(listingId);
            if (folderListing != null)
            {
                folderListing.Subject = subject;
                await logic.UpdateListingWithDisplayContainers(folderListing);

                await UpdateFolderNameAttributeAsync(listingId, subject);
            }
        }

        private async Task UpdateFolderNameAttributeAsync(long listingId, string subject)
        {
            //Update all Listing with FolderName attribute to new folder name (for consistency).
            //This also includes the ListingAttribute (FolderName) against the Folder Listing record.
            //NOTE: The ListingAttribute (FolderName) being stored against the Folder Listing record is so that all the FolderNames can be retrieved in the GetFilters call.
            string sql = $@"
						DECLARE @ListingAuditTable TABLE (
							ListingId BIGINT,
							Description NVARCHAR(MAX),
							CreatedOn DATETIMEOFFSET(7),
							CreatedByName NVARCHAR(60)
						);

						UPDATE [lstAttr]
                        SET
                            [lstAttr].[ValueString] = @subject
                        OUTPUT {("'Listing updated attributes changed. " + (string.IsNullOrEmpty(subject) ? "Folder Name to' + @subject" : "Folder removed.'"))} AS [Description]
                               ,GETUTCDATE() AS [CreatedOn]
                               ,@createdByName AS [CreatedByName]
                               ,@listingId AS [ListingId]
                        INTO @ListingAuditTable ([Description], [CreatedOn], [CreatedByName], [ListingId])
						FROM [listing].[ListingAttribute] [lstAttr]
                        INNER JOIN [listing].[Listing] [lst] ON [lst].[ListingId] = [lstAttr].[ListingId] AND [lst].[Deleted] = 0
                        WHERE ([lst].[ParentListingId] = @listingId OR [lst].[ListingId] = @listingId) AND [lstAttr].[AttributeCode] = @attributeCode   

						-- Insert Audit record for Listings
						INSERT INTO [listing].[ListingAudit] ([Description], [CreatedOn], [CreatedByName], [ListingId])
						SELECT 
							[Description], 
							[CreatedOn], 
							[CreatedByName], 
							[ListingId]
						FROM @ListingAuditTable;
                ";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("listingId", listingId);
                command.AddArgument("subject", subject);
                command.AddArgument("attributeCode", "FolderName");
                command.AddArgument("createdByName", _utils.UserFullName);
                await command.Execute();
            }
        }

        internal async Task DeleteAsync(long listingId)
        {
            await _manageListingFactory().DeleteAsync(listingId);

            //Empty the folder names for the associated Listing Attribute records
            await UpdateFolderNameAttributeAsync(listingId, string.Empty);
        }
    }
}
