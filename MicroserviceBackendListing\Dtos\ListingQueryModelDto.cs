﻿using MicroserviceContract.Dtos.Common;
using Redi.Prime3.MicroService.BaseLib;
using Sql;

namespace MicroserviceBackendListing.Dtos
{
    public class ListingQueryModelDto
    {
        public Dictionary<string, string>? Filter { get; set; }
        public Dictionary<string, string>? OrFilter { get; set; }
        public Dictionary<string, string>? Exclude { get; set; }
        public string[]? DisplayContainerCodesArray { get; set; }
        public string? DisplayGroupCode { get; set; }
        public int[]? IncludeTagIdsArray { get; set; }
        public int[]? ExcludeTagIdsArray { get; set; }
        public Int64? ParentEntityIntId { get; set; }
        public Guid? ParentEntityId { get; set; }
        public string? ParentEntityType { get; set; }
        public Int64? ParentListingId { get; set; }
        public string? StatusCode { get; set; }
        public string? Subject { get; set; }
        public string? Description { get; set; }
        public GpslocationCDto? FromLocation { get; set; }
        public int? SearchDistanceKm { get; set; }
        public DateTimeOffset? BeforeDate { get; set; }
        public DateTimeOffset? AfterDate { get; set; }
        public short Visibility { get; set; }
        public short ListingTypeId { get; set; }
        /// <summary>
        /// Item available from date/time
        /// For bookings
        /// </summary>
        public DateTimeOffset? AvailableFromDateTime {  get; set; }
        /// <summary>
        /// Item available to date/time
        /// For bookings
        /// </summary>
        public DateTimeOffset? AvailableToDateTime {  get; set; }
        /// <summary>
        /// Item available from this location
        /// For bookings
        /// </summary>
        public int? PickupFromHoldingLocationId { get; set; }
        public StandardListParameters? StandardListParameters { get; set; }
    }
    
}
