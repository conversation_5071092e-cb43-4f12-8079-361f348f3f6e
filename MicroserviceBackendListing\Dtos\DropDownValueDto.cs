﻿using MicroserviceBackendListing.BusinessLogic;
using Sql;

namespace MicroserviceBackendListing.Dtos
{
    [Mappable(nameof(AttributeDropdownValueId))]
    public class DropDownValueDto
    {
        public string? Value { set; get; }

        public short SortOrder { set; get; }

        public string? DisplayValue { set; get; }

        public long AttributeDropdownValueId { set; get;}
        
        public string? AttributeCode { get; set; }
    }

    public class GetDropDownValueDto : DropDownValueDto
    {
    }
}
