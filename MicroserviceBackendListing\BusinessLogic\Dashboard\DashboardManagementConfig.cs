﻿using MicroserviceBackendListing.BusinessLogic.Base;
using MicroserviceBackendListing.Enums;
using MicroserviceContract.Dtos.ListingManagement;
using MicroserviceDashboardContract.Dtos;
using Microsoft.Extensions.Caching.Memory;
using Redi.Prime3.MicroService.BaseLib;
using Sql;

namespace MicroserviceBackendListing.BusinessLogic
{
    /// <summary>
    /// This class supports fetching the editor Display Containers.
    /// </summary>
    public class DashboardManagementConfig : BusinessLogicBase
    {
        private UtilityFunctions _utils;
        private Func<ListingAttribute> _listingAttributeFactory;
        public DashboardManagementConfig(IUnitOfWork u
            , IMemoryCache memoryCache
            , Func<ListingAttribute> listingAttributeFactory
            , UtilityFunctions utils)
        {
            _unitOfWork = u;
            _utils = utils;
            _memoryCache = memoryCache;
            _listingAttributeFactory = listingAttributeFactory;
        }

        /// <summary>
        /// Get Dashboard Config by listingId. Internal use.
        /// </summary>
        /// <param name="listingId"></param>
        /// <param name="attributes"></param>
        /// <returns></returns>
        private GetDashboardManagementConfigCDto MapToConfig(Int64 listingId, List<ManageListingAttributeCDto> attributes)
        {
            if (attributes == null || attributes.Count == 0)
            {
                throw new HttpRequestException($"Attributes do not exist for this listing '{listingId}'", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            var configAttr = attributes.Where(x => x.AttributeCode == DashboardAttributeCode.DashboardConfig.ToString()).FirstOrDefault();
            if (configAttr == null || string.IsNullOrEmpty(configAttr.ValueStringMax))
            {
                throw new HttpRequestException($"Attribute 'DashboardConfig' do not exist for this listing '{listingId}'", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            var currentVersionAttr = attributes.Where(x => x.AttributeCode == DashboardAttributeCode.DashboardConfigVersion.ToString()).FirstOrDefault();
            if (currentVersionAttr == null || !currentVersionAttr.ValueNumeric.HasValue)
            {
                throw new HttpRequestException($"Attribute 'DashboardConfigVersion' do not exist for this listing '{listingId}'", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }
            var helpTextAttr = attributes.Where(x => x.AttributeCode == DashboardAttributeCode.DashboardHelpText.ToString()).FirstOrDefault();

            GetDashboardManagementConfigCDto dto = new GetDashboardManagementConfigCDto();
            dto.ListingId = listingId;
            dto.CurrentVersionNo = (int)currentVersionAttr.ValueNumeric.Value;
            dto.HelpText = helpTextAttr?.ValueString ?? "";
            dto.ConfigJson = configAttr.ValueStringMax;

            return dto;
        }

        internal async Task CreateConfigAsync(Int64 listingId, List<ManageListingAttributeCDto> attributes)
        {
            if (listingId <= 0)
            {
                throw new ArgumentException($"ListingId '{listingId}' is invalid");
            }
            else
            {
                var dto = MapToConfig(listingId, attributes);
                foreach (var attr in attributes)
                {
                    if (attr.AttributeCode == DashboardAttributeCode.DashboardConfigVersion.ToString())
                    {
                        attr.ValueNumeric = attr.ValueNumeric + 1;
                    }
                    await _listingAttributeFactory().CreateAsync(attr, listingId);
                }

                DashboardManagementConfigHistoryCDto configHistoryDto = new DashboardManagementConfigHistoryCDto()
                {
                    ListingId = dto.ListingId,
                    ConfigJson = dto.ConfigJson,
                    CreatedOn = DateTime.UtcNow,
                    CreatedByName = _utils.UserFullName,
                    VersionNumber = dto.CurrentVersionNo + 1,
                    Note = dto.HelpText ?? ""
                };
                await AddDashboardConfigHistory(configHistoryDto);
            }
        }

        /// <summary>
        /// Update Dashboard Config.
        /// If any of the field changed, add record to DashboardConfigHistory table
        /// </summary>
        /// <param name="attributes"></param>
        /// <param name="listingId"></param>
        /// <returns></returns>
        internal async Task UpdateConfigAsync(Int64 listingId, List<ManageListingAttributeCDto> attributes)
        {
            if (listingId <= 0)
            {
                throw new ArgumentException($"ListingId '{listingId}' is invalid");
            }
            else
            {
                var dto = MapToConfig(listingId, attributes);
                var logic = _listingAttributeFactory();
                var currentAttributes = await logic.GetListingAttibutesbyId(listingId);
                foreach (var attr in attributes)
                {
                    if (attr.ListingAttributeId == 0)
                    {
                        //ID's for attributes aren't returned in Listing Get List call in Dropdown list on frontend.
                        var attribute = currentAttributes.FirstOrDefault(x => x.AttributeCode == attr.AttributeCode);
                        attr.ListingAttributeId = attribute?.ListingAttributeId ?? 0;
                    }
                    await logic.UpdateAsync(attr, listingId);
                }

                DashboardManagementConfigHistoryCDto configHistoryDto = new DashboardManagementConfigHistoryCDto()
                {
                    ListingId = dto.ListingId,
                    ConfigJson = dto.ConfigJson,
                    CreatedOn = DateTime.UtcNow,
                    CreatedByName = _utils.UserFullName,
                    VersionNumber = dto.CurrentVersionNo,
                    Note = dto.HelpText ?? ""
                };
                await AddDashboardConfigHistory(configHistoryDto);
            }
        }

        /// <summary>
        /// Add record to DashboardConfigHistory table. Internal use.
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        private async Task AddDashboardConfigHistory(DashboardManagementConfigHistoryCDto dto)
        {
            if (dto.ListingId <= 0)
            {
                throw new ArgumentException($"Invalid Dashboard '{dto.ListingId}'");
            }

            string sql = @"
            INSERT INTO [dashboard].[DashboardConfigHistory]
            (
                [ListingId],
                [ConfigJson],
                [CreatedOn],
                [CreatedByName],
                [VersionNumber],
                [Note]
            )
            VALUES
            (
                @ListingId,
                @ConfigJson,
                @CreatedOn,
                @CreatedByName,
                @VersionNumber,
                @Note
            )
            ";
            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArguments(dto);
                await command.Execute();
            }
        }
    }
}
