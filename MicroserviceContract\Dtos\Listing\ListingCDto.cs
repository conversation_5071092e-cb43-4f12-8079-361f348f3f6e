﻿using MicroserviceContract.Dtos.Common;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Text.Json.Serialization;

namespace MicroserviceContract.Dtos.Listing
{
    [Mappable(nameof(ListingId))]
    public class ListingListCDto
    {
        /// <summary>
        /// The Internal database ListingId.
        /// This is not returned to public facing systems, and cannot be queried on via the Listing Api.
        /// Use the ReferenceNo to fetch a listing
        /// </summary>
        [JsonIgnore]
        public Int64 ListingId { set; get; }
        /// <summary>
        /// Unique Id for Listing that is used by public facing front-ends to identify the listing.
        /// </summary>
        public string? ReferenceNo { get; set; }
        /// <summary>
        /// Short description of the listing, that should be shown in lists and page titles.
        /// </summary>
        public string? Subject { get; set; }
        /// <summary>
        /// Identifies the type of listing. 0 is default.
        /// </summary>
        public short? ListingTypeId { get; set; }

        /// <summary>
        /// The parent listing this listing belongs to. Optional.
        /// Allows listings to have child listings.The child listings could serve other purposes.
        /// </summary>
        public Int64? ParentListingId { get; set; }
        /// <summary>
        /// Optional sort order for adjusting order of small sets of listings
        /// </summary>
        public Int32? SortOrder { get; set; }

        /// <summary>
        /// Status of the listing: Draft, Pending, Active, Expired, Paused
        /// </summary>
        public string? StatusCode { get; set; }
        /// <summary>
        /// The Lat/Long of the listing
        /// </summary>
        public GpslocationCDto? GpsLocation { get; set; }
        /// <summary>
        /// When the listing is Active from
        /// </summary>
        public DateTimeOffset FromDate { get; set; }
        /// <summary>
        /// When the listing is active to. After this date it will be Expired (status) automatically.
        /// No value indicates no expiry
        /// </summary>
        public DateTimeOffset? ToDate { get; set; }
        /// <summary>
        /// Default media to be used when displaying listing
        /// </summary>
        public Int64? ProfileListingMediaId { get; set; }
        /// <summary>
        /// Default media url to be used when displaying listing
        /// </summary>
        public string? ProfileListingMediaUrl { get; set; }
        public Int64? ParentEntityIntId { set; get; }
        public Guid? ParentEntityId { set; get; }
        public string? ParentEntityType { get; set; }
        public Guid? ParentEntityId2 { set; get; }
        public string? ParentEntityType2 { get; set; }
        public DateTimeOffset CreatedOn { get; set; }
        public string? CreatedByName { get; set; }
        public DateTimeOffset? ModifiedOn { get; set; }
        public string? ModifiedByName { get; set; }
        /// <summary>
        /// Distance away from requested location. Calculated on listing call.
        /// </summary>
        public decimal? DistanceAwayInKm { get; set; }
        public string? Icon { get; set; }

        /// <summary>
        /// True when listing is a favourite of the logged in user.
        /// </summary>
        public bool? IsUserFavourite { get; set; }

    }

    public class ListingCDto : ListingListCDto
    {
        /// <summary>
        /// Detail description for the Listing
        /// </summary>
        public string? Description { get; set; }
    }

    public class ListingWithDataAndMediaCDto : ListingCDto
    {
        public int? BookingItemId { get; set; }
        /// <summary>
        /// List of media records associated with a listing
        /// </summary>
        [IgnoreDbMapping]
        public List<ListingMediaCDto>? Media { get; set; }
        /// <summary>
        /// List of attributes associated with a listing
        /// </summary>
        [IgnoreDbMapping]
        public List<ListingDisplayContainerCDto>? Attributes { get; set; }
        [IgnoreDbMapping]
        public Dictionary<string, object?>? Fields { get; set; }
        public DateTimeOffset? AvailableTill { set; get; }
        /// <summary>
        /// List of Tags linked to the Listing.
        /// </summary>
        public List<ListingTagCDto>? Tags { get; set; }
    }

    /// <summary>
    /// Defines a media record.\
    /// Media can include Image, Video's, Pdf's, and other files.
    /// </summary>
    [Mappable(nameof(ListingMediaId))]

    public class ListingMediaCDto : DtoBase
    {
        /// <summary>
        /// Id that uniquely identifies a media record
        /// </summary>
        public Int64 ListingMediaId { set; get; }
        /// <summary>
        /// The ListingId the media record belongs to.
        /// </summary>
        public Int64 ListingId { set; get; }
        /// <summary>
        /// Full URL that can be used to download / display media
        /// </summary>
        public string? MediaUrl { set; get; }
        /// <summary>
        /// Title to be displayed for media item
        /// </summary>
        public string? Title { set; get; }
        /// <summary>
        /// The type of media. Image, Video, Pdf
        /// </summary>
        public string? MediaTypeCode { set; get; }
        /// <summary>
        /// The media category code.
        /// This can be used to control display of media
        /// </summary>
        public string? MediaCategoryCode { set; get; }
        /// <summary>
        /// The media type display label
        /// </summary>
        public string? MediaTypeLabel { set; get; }
        /// <summary>
        /// The media category display label
        /// </summary>
        public string? MediaCategoryLabel { set; get; }

       

    }

    [Mappable(nameof(DisplayContainerCode))]
    public class ListingDisplayContainerCDto
    {
        public string? DisplayContainerCode { set; get; }
        /// <summary>
        /// The display container title that can be displayed to users
        /// </summary>
        public string? Title { set; get; }
        /// <summary>
        /// Show title to users when True
        /// </summary>
        public bool? IsShowTitle { set; get; }
        /// <summary>
        /// Icon to display next to container Title
        /// </summary>
        public string? Icon { set; get; }
        /// <summary>
        /// Is this Display Container enabled for use. \
        /// When False Display Container is not returned from Listing Api
        /// </summary>
        public bool? Enabled { set; get; }
        /// <summary>
        /// Help text that can be displayed to user (tooltip or help button)
        /// </summary>
        public string? HelpText { set; get; }

        /// <summary>
        /// List of Attributes that make up the Display Container
        /// </summary>
        [IgnoreDbMapping]
        public List<ListingDataCDto>? Attributes { set; get; }

    }

    [Mappable(nameof(ListingAttributeId))]
    public class ListingDataCDto
    {
        public Int64 ListingAttributeId { set; get; }
        public string? AttributeCode { set; get; }
        public string? ValueString { set; get; }
        public string? ValueStringMax { set; get; }
        public decimal? ValueNumeric { set; get; }
        public DateTimeOffset? ValueDateTime { set; get; }
        public GpslocationCDto? ValueGeography { set; get; }
        public short? NumericDecimalPlaces { get; set; }
        public int DisplayContainerAttributeId { set; get; }
        public string? DisplayContainerCode { set; get; }
        public bool IsReadOnly { set; get; }
        public string? Label { set; get; }
        public int? SortOrder { set; get; }
        public string? AttributeGroupCode { set; get; }
        public string? AttributeValueTypeCode { set; get; }
        public string? Icon { set; get; }
        public string? ReadAccessClaim { set; get; }
        public string? WriteAccessClaim { set; get; }
        public bool IsManyAllowed { set; get; }
        public string? InputTypeCode { set; get; }

    }
    /// <summary>
    /// The Sort Option that can be used for a container
    /// </summary>
    [Mappable(nameof(SortOptionId))]
    public class ListingSortOptionCDto
    {
        /// <summary>
        /// The Sort Option Id.
        /// Use this in Sortby on GetList.
        /// </summary>
        public int SortOptionId { set; get; }
        /// <summary>
        /// The sort label. This can be displayed to users.
        /// </summary>
        public string? Label { set; get; }
        /// <summary>
        /// The Display Container Code, this option is available for
        /// </summary>
        public string? DisplayContainerCode { set; get; }

    }
}
