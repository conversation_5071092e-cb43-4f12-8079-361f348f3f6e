﻿using Redi.Prime3.MicroService.BaseLib;
using Sql;

namespace MicroserviceContract.Dtos.CRM
{
    [Mappable(nameof(AddressId))]
    public class BaseAddressCDto : DtoBase
    {
        public Guid AddressId { get; set; }
        public string? Lines { get; set; }
        public string? City { get; set; }
        public string? StateOrProvince { get; set; }
        public string? PostalCode { get; set; }
        public string? Country { get; set; }
        public string? CountryCode { get; set; }
        public string? AddressName { get; set; }
        public string? AddressTypeCode { get; set; }
        public string? AddressTypeIsEnabled { get; set; }
        public string? AddressTypeDescription { get; set; }
        public string? FullAddress { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public bool IsPreferredAddress { get; set; }
        public Guid ParentEntityId { get; set; }
        public string? ParentEntityType { get; set; }
        public int SortOrder { get; set; }
    }

    public class GetAddressCDto : BaseAddressCDto { }

    public class GetListAddressCDto : BaseAddressCDto { }
}
