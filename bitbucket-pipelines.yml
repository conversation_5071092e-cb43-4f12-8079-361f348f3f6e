image: atlassian/default-image:4

pipelines:
  default:
    - parallel:
        - step:
            name: Build and Test
            script:
              - source azure-deploy/environment/.env.deploy.development
              - IMAGE_NAME="${DOCKER_REPO}"
              - docker build . --file MicroserviceBackendListing/Dockerfile --tag ${IMAGE_NAME}
            services:
              - docker
            caches:
              - docker
  branches:
    master:
      - step:
          name: Build and Test
          script:
            - source azure-deploy/environment/.env.deploy.development
            - IMAGE_NAME="${DOCKER_REPO}"
            - docker build . --file MicroserviceBackendListing/Dockerfile --tag ${IMAGE_NAME}
            - docker save ${IMAGE_NAME} --output "${IMAGE_NAME}.tar"
          services:
            - docker
          caches:
            - docker
          artifacts:
            - "*.tar"
      - step:
          name: Deploy Base Service to Docker
          script:
            - source azure-deploy/environment/.env.deploy.development
            - echo ${DOCKERHUB_PASSWORD} | docker login --username "$DOCKERHUB_USERNAME" --password-stdin
            - IMAGE_NAME="${DOCKER_REPO}"
            - docker load --input "${IMAGE_NAME}.tar"
            - VERSION="dev-0.1.${BITBUCKET_BUILD_NUMBER}"
            - IMAGE=${DOCKERHUB_NAMESPACE}/${IMAGE_NAME}
            - docker tag "${IMAGE_NAME}" "${IMAGE}:${VERSION}"
            - docker push "${IMAGE}:${VERSION}"
            - docker tag "${IMAGE_NAME}" "${IMAGE}:dev-latest"
            - docker push "${IMAGE}:dev-latest"
          services:
            - docker
      - step:
          name: Deploy to Development environment
          deployment: test
          script:
            - source azure-deploy/environment/.env.deploy.development
            - export $(cat azure-deploy/environment/.env.deploy.development | xargs)
            - curl -sL https://aka.ms/InstallAzureCLIDeb | bash
            - az login --service-principal -u $AZURE_SERVICE_PRINCIPAL_ID -p $AZURE_PASSWORD --tenant $AZURE_TENANT_ID
            - bash azure-deploy/deploy.sh
            - bash azure-deploy/traefik/traefik.deploy.sh
    production:
      - step:
          name: Build and Test Production
          script:
            - source azure-deploy/environment/.env.deploy.production
            - IMAGE_NAME="${DOCKER_REPO}"
            - docker build . --file MicroserviceBackendListing/Dockerfile --tag ${IMAGE_NAME}
            - docker save ${IMAGE_NAME} --output "${IMAGE_NAME}.tar"
          services:
            - docker
          caches:
            - docker
          artifacts:
            - "*.tar"
      - step:
          name: Deploy Prod Service to Docker
          script:
            - source azure-deploy/environment/.env.deploy.production
            - echo ${DOCKERHUB_PASSWORD} | docker login --username "$DOCKERHUB_USERNAME" --password-stdin
            - IMAGE_NAME="${DOCKER_REPO}"
            - docker load --input "${IMAGE_NAME}.tar"
            - VERSION="prod-0.1.${BITBUCKET_BUILD_NUMBER}"
            - IMAGE=${DOCKERHUB_NAMESPACE}/${IMAGE_NAME}
            - docker tag "${IMAGE_NAME}" "${IMAGE}:${VERSION}"
            - docker push "${IMAGE}:${VERSION}"
          services:
            - docker
      - step:
          name: Deploy to Production environment
          deployment: production
          trigger: manual
          script:
            - source azure-deploy/environment/.env.deploy.production
            - export $(cat azure-deploy/environment/.env.deploy.production | xargs)
            - curl -sL https://aka.ms/InstallAzureCLIDeb | bash
            - az login --service-principal -u $AZURE_SERVICE_PRINCIPAL_ID -p $AZURE_PASSWORD --tenant $AZURE_TENANT_ID
            - bash azure-deploy/deploy.sh
            - bash azure-deploy/traefik/traefik.deploy.sh