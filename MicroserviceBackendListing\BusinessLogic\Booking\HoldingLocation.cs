﻿using MicroserviceBackendListing.BusinessLogic.Base;
using MicroserviceContract.Dtos.Booking;
using Redi.Prime3.MicroService.BaseLib;
using Microsoft.Extensions.Caching.Memory;
using Sql;
using System.Collections.Specialized;

namespace MicroserviceBackendListing.BusinessLogic.Booking.Management
{
    public partial class HoldingLocation : BusinessLogicBase
    {
        private UtilityFunctions _utils;
#pragma warning disable CS0414 // The field 'HoldingLocation.ENTITY_TYPE' is assigned but its value is never used
        private readonly string ENTITY_TYPE = "HoldingLocation";
#pragma warning restore CS0414 // The field 'HoldingLocation.ENTITY_TYPE' is assigned but its value is never used
        private Dictionary<string, object>? _queryParameters;
        private Notifications _writeNotificationEvent;
        private readonly Func<ManageListing> _manageListing;
        public HoldingLocation(IUnitOfWork u, IMemoryCache memoryCache, UtilityFunctions utils,
            Notifications writeNotificationEvent, Func<ManageListing> manageListing
            )
        {
            _utils = utils;
            _unitOfWork = u;
            _memoryCache = memoryCache;
            _writeNotificationEvent = writeNotificationEvent;
            _manageListing = manageListing;
        }

        public async Task<HoldingLocationResponseCDto> GetHoldingLocationAsync(int holdingLocationId, bool forUpdate = false)
        {
            if (holdingLocationId <= 0) { return null; }
            string extraCols = "";
            string baseSqlJoins = "";
            string extraSqlJoin = "";
            string sql = "";
            _queryParameters = [];

            (sql, baseSqlJoins, extraSqlJoin, extraCols) = await BuildBaseHoldingLocationQuery(forUpdate: forUpdate);

            sql += "WHERE [HoldingLocation].[HoldingLocationId] = @holdingLocationId ";

            _queryParameters.Add("holdingLocationId", holdingLocationId);

            if (_utils.TenantId != null)
            {
                sql += " AND [HoldingLocation].[TenantId] = @TenantId ";
               _queryParameters.Add("TenantId", _utils.TenantId);
            }

            HoldingLocationResponseCDto? result = null;
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                if (_queryParameters.Count > 0)
                {
                    foreach (var qp in _queryParameters)
                    {
                        command.AddArgument(qp.Key, qp.Value);
                    }
                }
                using (var reader = await command.SelectRaw())
                {
                    if (reader.Read())
                    {
                        result = GetHoldingLocationRow(reader);
                    }
                }

            }

            return result;
        }

#pragma warning disable CS1998 // Async method lacks 'await' operators and will run synchronously
        internal async Task<(string, string, string, string)> BuildBaseHoldingLocationQuery(StringDictionary? canBeSortedBy = null, bool? forUpdate = false)
#pragma warning restore CS1998 // Async method lacks 'await' operators and will run synchronously
        {
            string extraCols = "";
            string extraSqlJoin = "";
            string baseSqlJoins = "";
            string sql = "";
            if (canBeSortedBy == null) { canBeSortedBy = []; }

            sql = @$"
            SELECT   
                    [HoldingLocation].[HoldingLocationId],
                    [HoldingLocation].[LocationParentEntityType],
                    [HoldingLocation].[LocationParentEntityId],
                    [HoldingLocation].[LocationParentEntityIntId],
                    [HoldingLocation].[TenantId],
                    CASE
                     {(ConfigBase.Schemas.ContainsKey("users") ? "WHEN [HoldingLocation].[LocationParentEntityType] = 'User' THEN [HoldingLocationUser].[FullName]" : "")}
                     {(ConfigBase.Schemas.ContainsKey("crm") ? "WHEN [HoldingLocation].[LocationParentEntityType] = 'Party' THEN [HoldingLocationParty].[DisplayName]" : "")}
                     {(ConfigBase.Schemas.ContainsKey("listing") ? "WHEN [HoldingLocation].[LocationParentEntityType] = 'Listing' THEN [HoldingLocationListing].[Subject]" : "")}
                     {(ConfigBase.Schemas.ContainsKey("order") ? "WHEN [HoldingLocation].[LocationParentEntityType] = 'Order' THEN [HoldingLocationOrder].[OrderReference]" : "")}
                     ELSE 'Unknown Location'
                    END AS [HoldingLocationName]
                    {extraCols} ";

            baseSqlJoins = $@"
            FROM    [booking].[HoldingLocation] {(forUpdate == true ? "WITH (UPDLOCK)" : "")}
            {(ConfigBase.Schemas.ContainsKey("crm") ? "LEFT JOIN [crm].[Party] [HoldingLocationParty] ON [HoldingLocationParty].[PartyId] = [HoldingLocation].[LocationParentEntityId] AND [HoldingLocation].[LocationParentEntityType] = 'Party'" : "")}
            {(ConfigBase.Schemas.ContainsKey("users") ? "LEFT JOIN [users].[User] [HoldingLocationUser] ON HoldingLocationUser].[UserId] = [HoldingLocation].[LocationParentEntityId] AND [HoldingLocation].[LocationParentEntityType] = 'User'" : "")} 
            {(ConfigBase.Schemas.ContainsKey("listing") ? "LEFT JOIN [listing].[Listing] [HoldingLocationListing] ON [HoldingLocationListing].[ListingId] = [HoldingLocation].[LocationParentEntityIntId] AND [HoldingLocation].[LocationParentEntityType] = 'Listing'" : "")}
            {(ConfigBase.Schemas.ContainsKey("order") ? "LEFT JOIN [order].[Order] [HoldingLocationOrder] ON [HoldingLocationOrder].[OrderId] = [HoldingLocation].[LocationParentEntityId] AND [HoldingLocation].[LocationParentEntityType] = 'Order'" : "")}
";

            sql += baseSqlJoins;
            sql += extraSqlJoin;

            return (sql, baseSqlJoins, extraSqlJoin, extraCols);
        }

        internal HoldingLocationResponseCDto GetHoldingLocationRow(Microsoft.Data.SqlClient.SqlDataReader reader)
        {
            HoldingLocationResponseCDto rec = new HoldingLocationResponseCDto();
            rec.HoldingLocationId = reader.GetInt32(reader.GetOrdinal("HoldingLocationId"));
            rec.HoldingLocationName = !reader.IsDBNull(reader.GetOrdinal("HoldingLocationName")) ? (reader.GetString(reader.GetOrdinal("HoldingLocationName"))) : null;
            rec.LocationParentEntityId = !reader.IsDBNull(reader.GetOrdinal("LocationParentEntityId")) ? (reader.GetGuid(reader.GetOrdinal("LocationParentEntityId"))) : null;
            rec.LocationParentEntityType = !reader.IsDBNull(reader.GetOrdinal("LocationParentEntityType")) ? (reader.GetString(reader.GetOrdinal("LocationParentEntityType"))) : null;
            rec.LocationParentEntityIntId = !reader.IsDBNull(reader.GetOrdinal("LocationParentEntityIntId")) ? (reader.GetInt64(reader.GetOrdinal("LocationParentEntityIntId"))) : null;

            return rec;

        }

        internal async Task<ListResponseDto<HoldingLocationResponseCDto>> ListHoldingLocationAsync(
            StandardListParameters standardListParameters, string? query, List<int>? holdingLocationIds)
        {
            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "HoldingLocationId", "[HoldingLocation].[HoldingLocationId]" },
                { "HoldingLocationName", "[HoldingLocationName]" },
            };

            string extraCols = "";
            string extraSqlJoin = "";
            string sql = "";
            string baseSqlJoins = "";
            _queryParameters = [];

            (sql, baseSqlJoins, extraSqlJoin, extraCols) = await BuildBaseHoldingLocationQuery(canBeSortedBy);

            #region WHERE clause
            string whereClause = @"
            WHERE [HoldingLocation].[Deleted] = @deleted ";
            _queryParameters.Add("deleted", false);

            if (_utils.TenantId != null)
            {
                whereClause += @" AND [HoldingLocation].[TenantId] = @tenantId ";
                _queryParameters.Add("tenantId", _utils.TenantId);
            }

            if (!string.IsNullOrEmpty(query))
            {
                whereClause += $@"
                AND (
                {(ConfigBase.Schemas.ContainsKey("users") ? "([HoldingLocation].[LocationParentEntityType] = 'User' AND [HoldingLocationUser].[FullName] LIKE '%' + @query + '%') OR " : "")}
                {(ConfigBase.Schemas.ContainsKey("crm") ? "([HoldingLocation].[LocationParentEntityType] = 'Party' THEN [HoldingLocationParty].[DisplayName] LIKE '%' + @query + '%') OR " : "")}
                {(ConfigBase.Schemas.ContainsKey("listing") ? "([HoldingLocation].[LocationParentEntityType] = 'Listing' THEN [HoldingLocationListing].[Subject] LIKE '%' + @query + '%') OR " : "")}
                {(ConfigBase.Schemas.ContainsKey("order") ? "([HoldingLocation].[LocationParentEntityType] = 'Order' THEN [HoldingLocationOrder].[OrderReference] LIKE '%' + @query + '%') OR " : "")}
                1 = 2 )";
                _queryParameters.Add("query", query);
            }

            if (holdingLocationIds != null && holdingLocationIds.Count > 0)
            {
                whereClause += @" AND [HoldingLocation].[HoldingLocationId] IN (@holdingLocationIds) ";
                _queryParameters.Add("holdingLocationIds", holdingLocationIds);
            }
            #endregion

            sql += whereClause;

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "HoldingLocationName");

            sql += $"OFFSET @offset ROWS";
            sql += $" FETCH NEXT @limit ROWS ONLY";

            _queryParameters.Add("offset", standardListParameters.Offset ?? 0);
            _queryParameters.Add("limit", standardListParameters.Limit ?? 100);

            List<HoldingLocationResponseCDto> result = new List<HoldingLocationResponseCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {

                if (_queryParameters.Count > 0)
                {
                    foreach (var qp in _queryParameters)
                    {
                        command.AddArgument(qp.Key, qp.Value);
                    }
                }

                using (var reader = await command.SelectRaw())
                {
                    while (reader.Read())
                    {
                        var rec = GetHoldingLocationRow(reader);
                        result.Add(rec);
                    }
                }
            }

            sql = @$"
            SELECT COUNT(*) AS totalNumOfRows
            
            {baseSqlJoins}
            {extraSqlJoin}
            ";

            sql += whereClause;
            var response = new ListResponseDto<HoldingLocationResponseCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                if (_queryParameters.Count > 0)
                {
                    foreach (var qp in _queryParameters)
                    {
                        command.AddArgument(qp.Key, qp.Value);
                    }
                }

                response.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }
            response.List = result;
            return response;
        }
    }
}
