﻿using MicroserviceBackendListing.BusinessLogic.Booking.Management;
using MicroserviceBackendListing.Dtos;
using MicroserviceBackendListing.Enums;
using MicroserviceContract.Dtos.Booking;
using Redi.Prime3.MicroService.BaseLib;
using Microsoft.Extensions.Caching.Memory;
using Sql;
using System.Collections.Specialized;
using Microsoft.IdentityModel.Tokens;

namespace MicroserviceBackendListing.BusinessLogic.Booking
{
    /// <summary>
    /// Booking Record manages all aspects of an actual booking or reservation
    /// </summary>
    public partial class BookingRecord : BusinessLogicBase
    {
        private UtilityFunctions _utils;
        private readonly string ENTITY_TYPE = "Booking";
        private Dictionary<string, object>? _queryParameters;
        private readonly Func<Statistic> _statistic;
        private Notifications _writeNotificationEvent;
        private Func<DaprCommonServiceClient> _daprCommonServiceClient;
        private HoldingLocation _holdingLocation;

        public BookingRecord(IUnitOfWork u, IMemoryCache memoryCache, UtilityFunctions utils, Func<Statistic> statistic, Notifications writeNotificationEvent, Func<DaprCommonServiceClient> daprCommonServiceClient, HoldingLocation holdingLocation)
        {
            _utils = utils;
            _unitOfWork = u;
            _statistic = statistic;
            _memoryCache = memoryCache;
            _writeNotificationEvent = writeNotificationEvent;
            _daprCommonServiceClient = daprCommonServiceClient;
            _holdingLocation = holdingLocation;
        }

        internal async Task<BookingRecordResponseCDto> GetSingleBookingRecord(long bookingRecordId, string? statisticFields = null, bool? returnNotesCount = false, bool? forUpdate = false)
        {
            if (bookingRecordId <= 0) { throw new HttpRequestException($"Invalid bookingRecordId {bookingRecordId}."); }
            string extraCols = "";
            string baseSqlJoins = "";
            string extraSqlJoin = "";
            string sql = "";
            List<AttributeDto> attrs = new List<AttributeDto>();
            _queryParameters = [];

            (sql, baseSqlJoins, extraSqlJoin, extraCols, attrs) = await BuildBaseBookingRecordQuery(statisticFields, returnNotesCount, forUpdate: forUpdate);

            sql += "WHERE [BookingRecord].[BookingRecordId] = @BookingRecordId ";

            _queryParameters.Add("BookingRecordId", bookingRecordId);

            // If user belongs to a Tenant then enusre they can only request there Tenant BookingRecordId's
            if (_utils.TenantId != null)
            {
                sql += " AND [BookingRecord].[TenantId] = @TenantId ";
                _queryParameters.Add("TenantId", _utils.TenantId);
            }

            BookingRecordResponseCDto? result = null;
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                if (_queryParameters.Count > 0)
                {
                    foreach (var qp in _queryParameters)
                    {
                        command.AddArgument(qp.Key, qp.Value);
                    }
                }
                using (var reader = await command.SelectRaw())
                {
                    if (reader.Read())
                    {
                        result = GetBookingRecordRow(reader, statisticFields, attrs);
                    }
                }

            }
            return result;
        }

        /// <summary>
        /// Get the next booking date and time for requesting user.
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        internal async Task<BookingCountsInNextBookingDateTimeCDto> GetNextBookingDateTimeAndCountsAsync()
        {
            string sql = @$"
WITH BookingRecords AS (
    SELECT 
        [FromDateTime]
    FROM 
        [booking].[BookingRecord]
    WHERE 
        [BookingRecordStatusId] != {(short)BookingRecordStatusEnum.Cancelled} 
        AND [BookingOwnerParentEntityType] = 'User'
)
SELECT 
    CAST(MIN([FromDateTime]) AS DATETIMEOFFSET) AS NextBookingDateTimeOffset,
    COUNT(*) AS Count        
FROM 
    [booking].[BookingRecord] 
WHERE 
    [BookingRecordStatusId] != {(short)BookingRecordStatusEnum.Cancelled} 
    AND [BookingOwnerParentEntityType] = 'User'
    {(_utils.UserId != null ? "AND [BookingOwnerParentEntityId] = @userId" : "")}
    AND [FromDateTime] >= (
        SELECT TOP 1 
            CAST(SWITCHOFFSET([FromDateTime], DATEPART(TZOFFSET, [FromDateTime])) AS DATETIMEOFFSET)
        FROM BookingRecords
        ORDER BY [FromDateTime] ASC
    )
    AND [FromDateTime] < (
        SELECT TOP 1 
            CAST(SWITCHOFFSET(DATEADD(DAY, 1, [FromDateTime]), DATEPART(TZOFFSET, [FromDateTime])) AS DATETIMEOFFSET)
        FROM BookingRecords
        ORDER BY [FromDateTime] ASC
    );
";
            try
            {
                using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
                {
                    if (_utils.UserId.HasValue)
                    {
                        command.AddArgument("userId", _utils.UserId);
                    }

                    BookingCountsInNextBookingDateTimeCDto rec = new BookingCountsInNextBookingDateTimeCDto();
                    using (var reader = await command.SelectRaw())
                    {
                        while (reader.Read())
                        {
                            rec.NextBookingDateTimeOffset = !reader.IsDBNull(reader.GetOrdinal("NextBookingDateTimeOffset"))
                                ? reader.GetDateTimeOffset(reader.GetOrdinal("NextBookingDateTimeOffset"))
                                : null;
                            rec.Count = !reader.IsDBNull(reader.GetOrdinal("Count"))
                                ? reader.GetInt32(reader.GetOrdinal("Count"))
                                : null;
                        }
                    }
                    return rec;
                }
            }
            catch (Exception ex)
            {
                throw new Exception(sql, ex);
            }
        }


        internal async Task<(string, string, string, string, List<AttributeDto>)> BuildBaseBookingRecordQuery(string? statisticFields = null, bool? returnNotesCount = false, StringDictionary? canBeSortedBy = null, bool? forUpdate = false, bool? fetchAttributes = true)
        {
            string extraCols = "";
            string extraSqlJoin = "";
            string baseSqlJoins = "";
            string sql = "";
            List<AttributeDto> attrs = new List<AttributeDto>();
            if (canBeSortedBy == null) { canBeSortedBy = []; }

            // Add Statistic Fields to query.
            if (!string.IsNullOrEmpty(statisticFields))
            {
                var statFields = await _statistic().GetList();
                int i = 0;
                foreach (var statFld in statisticFields.Split(','))
                {
                    if (statFields.ContainsKey(statFld))
                    {
                        canBeSortedBy.Add($"{statFld}", $"[Stat{statFld}Value]");
                        i++;
                        extraCols += $",[Stat{i}].[DValue] AS [Stat{statFld}Value] ";
                        extraSqlJoin += $" LEFT JOIN [common].[StatisticRecord] [Stat{i}] ON [Stat{i}].[ParentEntityIntId] = [BookingRecord].[BookingRecordId] AND [Stat{i}].[StatisticId] = @StatId{i} AND [Stat{i}].[SourceStatisticEntityTypeId] = {StatisticEntityTypeEnum.Booking} ";
#pragma warning disable CS8602 // Dereference of a possibly null reference.
                        _queryParameters.Add($"StatId{i}", statFields[statFld]);
#pragma warning restore CS8602 // Dereference of a possibly null reference.
                    }
                }
            }

            if (fetchAttributes == true)
            {
                attrs = await GetAttributes();
                int i = 0;
                foreach (var att in attrs)
                {
                    if (att != null && att.AttributeCode != null)
                    {
                        string attrCode = att.AttributeCode;
                        canBeSortedBy.Add($"{attrCode}", $"[Attr{attrCode}Value]");
                        i++;
                        if (att.IsManyAllowed == false)
                        {
                            extraCols += $",[Attr{i}].[{att.AttributeValueTypeCode}] AS [Attr{attrCode}Value] ";
                            extraSqlJoin += $" LEFT JOIN [listing].[ListingAttribute] [Attr{i}] ON [Attr{i}].[ListingId] = LI.[ListingId] AND [Attr{i}].[AttributeCode] = @AttributeCode{i} ";
                        }
                        else
                        {
                            extraCols += $",STUFF((Select ', ' + CAST(AA.[{att.AttributeValueTypeCode}] AS VARCHAR(MAX)) From [listing].[ListingAttribute] [AA] Where [LI].[ListingId] = AA.[ListingId] AND AA.[AttributeCode] = @AttributeCode{i} FOR XML PATH('')),1,1,'') AS [Attr{attrCode}Value] ";
                        }
#pragma warning disable CS8602 // Dereference of a possibly null reference.
                        _queryParameters.Add($"AttributeCode{i}", attrCode);
#pragma warning restore CS8602 // Dereference of a possibly null reference.
                    }
                }
            }

            if (returnNotesCount == true)
            {
                extraCols += " ,(Select  COUNT(*) From [common].[Note] Where [Note].[Deleted] = 0 AND [Note].[ParentEntityIntId] = [BookingRecord].[BookingRecordId] AND [Note].[ParentEntityType] = 'BookingRecord'  ) AS NotesCount ";
            }
            else
            {
                extraCols += " ,null AS NotesCount ";
            }

            sql = @$"
            SELECT   
                    [BookingRecord].[BookingRecordId],
                    [BookingRecord].[BookingRecordReference],
                    [BookingRecord].[BookingRecurringId],
                    [BookingRecord].[IsRecurringBookingCustomised],
                    [BookingRecord].[FromDateTime],
                    [BookingRecord].[ToDateTime],
                    [BookingRecord].[BookingRecordStatusId],
                    [BookingRecord].[BookingOwnerParentEntityId],
                    [BookingRecord].[BookingOwnerParentEntityIntId],
                    [BookingRecord].[BookingOwnerParentEntityType],
                    [BookingRecord].[PickupFromHoldingLocationId],
                    [BookingRecord].[ReturnToHoldingLocationId],
                    [BookingRecord].[TenantId],
                    [BookingRecord].[CreatedOn],
                    [BookingRecord].[CreatedByName],
                    [BookingRecord].[ModifiedOn],
                    [BookingRecord].[ModifiedByName],
                    [BookingRecord].[Note],
                    [Status].[Label] AS [BookingRecordStatusLabel],
                    [Status].[SortOrder] AS [BookingRecordStatusSortOrder],
                    [Status].[Colour] AS [BookingRecordStatusColour],
                    [Status].[Icon] AS [BookingRecordStatusIcon],
                    [BookingRecord].[Deleted],
                    -- Get the BookingOwner Name
                    CASE
                     {( ConfigBase.Schemas.ContainsKey("users") ? "WHEN [BookingRecord].[BookingOwnerParentEntityType] = 'User' THEN [BookingRecordOwnerUser].[FullName]" : "" )}
                     {(ConfigBase.Schemas.ContainsKey("crm") ? "WHEN [BookingRecord].[BookingOwnerParentEntityType] = 'Party' THEN [BookingRecordOwnerParty].[DisplayName]" : "" )}
                     {(ConfigBase.Schemas.ContainsKey("listing") ? "WHEN [BookingRecord].[BookingOwnerParentEntityType] = 'Listing' THEN [BookingRecordOwnerListing].[Subject]" : "" )}
                     {(ConfigBase.Schemas.ContainsKey("order") ? "WHEN [BookingRecord].[BookingOwnerParentEntityType] = 'Order' THEN [BookingRecordOwnerOrder].[OrderReference]" : "" )}
                     ELSE [BookingRecord].[CreatedByName]
                    END AS [BookingOwnerName],
                    -- Get PickupFromHoldingLocation Name
                    CASE
                     {(ConfigBase.Schemas.ContainsKey("users") ? "WHEN [PICKUPHOLD].[LocationParentEntityType] = 'User' THEN [PICKUPHOLDUser].[FullName]" : "")}
                     {(ConfigBase.Schemas.ContainsKey("crm") ? "WHEN [PICKUPHOLD].[LocationParentEntityType] = 'Party' THEN [PICKUPHOLDParty].[DisplayName]" : "")}
                     {(ConfigBase.Schemas.ContainsKey("listing") ? "WHEN [PICKUPHOLD].[LocationParentEntityType] = 'Listing' THEN [PICKUPHOLDListing].[Subject]" : "")}
                     {(ConfigBase.Schemas.ContainsKey("order") ? "WHEN [PICKUPHOLD].[LocationParentEntityType] = 'Order' THEN [PICKUPHOLDOrder].[OrderReference]" : "")}
                     ELSE 'Unknown Location'
                    END AS [PickupFromHoldingLocationName],
                    -- Get ReturnToHoldingLocation Name
                    CASE
                     {(ConfigBase.Schemas.ContainsKey("users") ? "WHEN [RETURNTOHOLD].[LocationParentEntityType] = 'User' THEN [RETURNTOHOLDUser].[FullName]" : "")}
                     {(ConfigBase.Schemas.ContainsKey("crm") ? "WHEN [RETURNTOHOLD].[LocationParentEntityType] = 'Party' THEN [RETURNTOHOLDParty].[DisplayName]" : "")}
                     {(ConfigBase.Schemas.ContainsKey("listing") ? "WHEN [RETURNTOHOLD].[LocationParentEntityType] = 'Listing' THEN [RETURNTOHOLDListing].[Subject]" : "")}
                     {(ConfigBase.Schemas.ContainsKey("order") ? "WHEN [RETURNTOHOLD].[LocationParentEntityType] = 'Order' THEN [RETURNTOHOLDOrder].[OrderReference]" : "")}
                     ELSE 'Unknown Location'
                    END AS [ReturnToHoldingLocationName],
                    STUFF((Select ',' + CAST(AC.[Subject] AS VARCHAR(MAX)) From [booking].[BookingRecordItem] [AA] INNER JOIN [booking].[BookingItem] [AB] ON [AB].[BookingItemId] = [AA].[BookingItemId] INNER JOIN [listing].[Listing] AC ON AB.[ParentEntityIntId] = AC.[ListingId] Where [BookingRecord].[BookingRecordId] = AA.[BookingRecordId] AND AA.[Deleted] = 0 FOR XML PATH('')),1,1,'') AS [BookingItemNames], 
                    STUFF((Select ',' + CAST(AA.[BookingItemId] AS VARCHAR(MAX)) From [booking].[BookingRecordItem] [AA] INNER JOIN [booking].[BookingItem] [AB] ON [AB].[BookingItemId] = [AA].[BookingItemId] Where [BookingRecord].[BookingRecordId] = AA.[BookingRecordId] AND AA.[Deleted] = 0 FOR XML PATH('')),1,1,'') AS [BookingItemIds],
                     STUFF((Select ',' + CAST(AM.[MediaUrl] AS VARCHAR(MAX)) From [booking].[BookingRecordItem] [AA] INNER JOIN [booking].[BookingItem] [AB] ON [AB].[BookingItemId] = [AA].[BookingItemId] INNER JOIN [listing].[ListingMedia] AM ON AB.[ParentEntityIntId] = AM.[ListingId] Where [BookingRecord].[BookingRecordId] = AA.[BookingRecordId] AND AA.[Deleted] = 0 FOR XML PATH('')),1,1,'') AS [ProfileListingMediaUrl]
                    {extraCols} ";

            baseSqlJoins = $@"
            FROM    [booking].[BookingRecord] {(forUpdate == true ? "WITH (UPDLOCK)" : "")}
            INNER JOIN [booking].[BookingRecordItem] [BRI] ON [BRI].[BookingRecordId] = [BookingRecord].[BookingRecordId] AND [BRI].[Deleted] = 0 
            INNER JOIN [booking].[BookingItem] [BI] ON [BRI].[BookingItemId] = [BI].[BookingItemId] 
            LEFT JOIN [listing].[Listing] [LI] ON BI.[ParentEntityIntId] = LI.[ListingId] AND BI.[ParentEntityType] = 'listing'
            LEFT JOIN [listing].[HoldingLocation] [PICKUPHOLD] ON [BookingRecord].[PickupFromHoldingLocationId] = PICKUPHOLD.[HoldingLocationId]
            {(ConfigBase.Schemas.ContainsKey("crm") ? "LEFT JOIN [crm].[Party] [PICKUPHOLDParty] ON [PICKUPHOLDParty].[PartyId] = [PICKUPHOLD].[LocationParentEntityId] AND [PICKUPHOLD].[LocationParentEntityType] = 'Party'" : "")}
            {(ConfigBase.Schemas.ContainsKey("users") ? "LEFT JOIN [users].[User] [PICKUPHOLDUser] ON [PICKUPHOLDUser].[UserId] = [PICKUPHOLD].[LocationParentEntityId] AND [PICKUPHOLD].[LocationParentEntityType] = 'User'" : "")} 
            {(ConfigBase.Schemas.ContainsKey("listing") ? "LEFT JOIN [listing].[Listing] [PICKUPHOLDListing] ON [PICKUPHOLDListing].[ListingId] = [PICKUPHOLD].[LocationParentEntityIntId] AND [PICKUPHOLD].[LocationParentEntityType] = 'Listing'" : "")}
            {(ConfigBase.Schemas.ContainsKey("order") ? "LEFT JOIN [order].[Order] [PICKUPHOLDOrder] ON [PICKUPHOLDOrder].[OrderId] = [PICKUPHOLD].[LocationParentEntityId] AND [PICKUPHOLD].[LocationParentEntityType] = 'Order'" : "")}

            LEFT JOIN [listing].[HoldingLocation] [RETURNTOHOLD] ON [BookingRecord].[ReturnToHoldingLocationId] = RETURNTOHOLD.[HoldingLocationId]
            {(ConfigBase.Schemas.ContainsKey("crm") ? "LEFT JOIN [crm].[Party] [RETURNTOHOLDParty] ON [RETURNTOHOLDParty].[PartyId] = [RETURNTOHOLD].[LocationParentEntityId] AND [RETURNTOHOLD].[LocationParentEntityType] = 'Party'" : "")}
            {(ConfigBase.Schemas.ContainsKey("users") ? "LEFT JOIN [users].[User] [RETURNTOHOLDUser] ON RETURNTOHOLDUser].[UserId] = [RETURNTOHOLD].[LocationParentEntityId] AND [RETURNTOHOLD].[LocationParentEntityType] = 'User'" : "")} 
            {(ConfigBase.Schemas.ContainsKey("listing") ? "LEFT JOIN [listing].[Listing] [RETURNTOHOLDListing] ON [RETURNTOHOLDListing].[ListingId] = [RETURNTOHOLD].[LocationParentEntityIntId] AND [RETURNTOHOLD].[LocationParentEntityType] = 'Listing'" : "")}
            {(ConfigBase.Schemas.ContainsKey("order") ? "LEFT JOIN [order].[Order] [RETURNTOHOLDOrder] ON [RETURNTOHOLDOrder].[OrderId] = [RETURNTOHOLD].[LocationParentEntityId] AND [RETURNTOHOLD].[LocationParentEntityType] = 'Order'" : "")}

            LEFT JOIN [booking].[BookingRecordStatus] [Status] ON [BookingRecord].[BookingRecordStatusId] = [Status].[BookingRecordStatusId]
            {( ConfigBase.Schemas.ContainsKey("crm") ? "LEFT JOIN [crm].[Party] [BookingRecordOwnerParty] ON [BookingRecordOwnerParty].[PartyId] = [BookingRecord].[BookingOwnerParentEntityId] AND [BookingRecord].[BookingOwnerParentEntityType] = 'Party'" : "" )}
            {( ConfigBase.Schemas.ContainsKey("users") ? "LEFT JOIN [users].[User] [BookingRecordOwnerUser] ON [BookingRecordOwnerUser].[UserId] = [BookingRecord].[BookingOwnerParentEntityId] AND [BookingRecord].[BookingOwnerParentEntityType] = 'User'" : "" )} 
            {(ConfigBase.Schemas.ContainsKey("listing") ? "LEFT JOIN [listing].[Listing] [BookingRecordOwnerListing] ON [BookingRecordOwnerListing].[ListingId] = [BookingRecord].[BookingOwnerParentEntityIntId] AND [BookingRecord].[BookingOwnerParentEntityType] = 'Listing'" : "")}
            {(ConfigBase.Schemas.ContainsKey("order") ? "LEFT JOIN [order].[Order] [BookingRecordOwnerOrder] ON [BookingRecordOwnerOrder].[OrderId] = [BookingRecord].[BookingOwnerParentEntityId] AND [BookingRecord].[BookingOwnerParentEntityType] = 'Order'" : "")}
";

            sql += baseSqlJoins;
            sql += extraSqlJoin;

            return (sql, baseSqlJoins, extraSqlJoin, extraCols, attrs);
        }


        /// <summary>
        /// Returns a list of BookingRecordResponseCDto matching the specified parameters.
        /// </summary>
        /// <param name="standardListParameters"></param>
        /// <param name="query"></param>
        /// <param name="bookingRecurringId"></param>
        /// <param name="isRecurringBookingCustomised"></param>
        /// <param name="bookingRecordStatusIds"></param>
        /// <param name="bookingItemIds"></param>
        /// <param name="bookingRecordReference"></param>
        /// <param name="bookingOwnerParentEntityIds"></param>
        /// <param name="bookingOwnerParentEntityIntIds"></param>
        /// <param name="bookingOwnerParentEntityType"></param>
        /// <param name="bookingRecordDateFrom"></param>
        /// <param name="bookingRecordDateTo"></param>
        /// <param name="filter"></param>
        /// <param name="exclude"></param>
        /// <param name="orFilter"></param>
        /// <param name="createdOnDateFrom"></param>
        /// <param name="createdOnDateTo"></param>
        /// <param name="modifiedOnDateFrom"></param>
        /// <param name="modifiedOnDateTo"></param>
        /// <param name="tenantId"></param>
        /// <param name="returnMyBookingsOnly"></param>
        /// <param name="statisticFields"></param>
        /// <param name="returnNotesCount"></param>
        /// <param name="createdByName"></param>
        /// <param name="modifiedByName"></param>
        /// <param name="pickupFromHoldingLocationIds"></param>
        /// <param name="returnToHoldingLocationIds"></param>
        /// <returns></returns>
        internal async Task<ListResponseDto<BookingRecordResponseCDto>> ListBookingRecords(StandardListParameters standardListParameters, string? query,
             int? bookingRecurringId, bool? isRecurringBookingCustomised, string? bookingRecordStatusIds,
             List<long>? bookingItemIds,
             string? bookingRecordReference, List<Guid>? bookingOwnerParentEntityIds, List<long>? bookingOwnerParentEntityIntIds, string? bookingOwnerParentEntityType,
             DateTimeOffset? bookingRecordDateFrom = null, DateTimeOffset? bookingRecordDateTo = null,
             Dictionary<string, string>? filter = null, Dictionary<string, string>? exclude = null, Dictionary<string, string>? orFilter = null,
             DateTimeOffset? createdOnDateFrom = null, DateTimeOffset? createdOnDateTo = null,
             DateTimeOffset? modifiedOnDateFrom = null, DateTimeOffset? modifiedOnDateTo = null, int? tenantId = null, bool? returnMyBookingsOnly = null,
             string? statisticFields = null, bool? returnNotesCount = false, string? createdByName = null, string? modifiedByName = null,
             List<int>? pickupFromHoldingLocationIds = null, List<int>? returnToHoldingLocationIds = null)
        {
            StringDictionary canBeSortedBy = new StringDictionary()
            {
                {"BookingRecordReference", "[BookingRecord].[BookingRecordReference]" },
                {"FromDateTime","[BookingRecord].[FromDateTime]" },
                {"ToDateTime", "[BookingRecord].[ToDateTime]" },
                {"BookingRecordStatusId", "[BookingRecord].[BookingRecordStatusId]" },
                {"BookingRecordStatusLabel", "[Status].[Label]" },
                {"BookingRecordStatusSortOrder", "[Status].[SortOrder]" },
                {"CreatedOn","[BookingRecord].[CreatedOn]" },
                {"CreatedByName","[CreatedByName]" },
                {"ModifiedOn","[BookingRecord].[ModifiedOn]"},
                {"ModifiedByName","[BookingRecord].[ModifiedByName]"},
                {"Note","[BookingRecord].[Note]"},
                {"BookingRecurringId", "[BookingRecord].[BookingRecurringId]" },
                {"BookingRecordId", "[BookingRecord].[BookingRecordId]" },
                {"IsRecurringBookingCustomised", "[BookingRecord].[IsRecurringBookingCustomised]" },
                {"PickupFromHoldingLocationName", "[PickupFromHoldingLocationName]" },
                {"ReturnToHoldingLocationName", "[ReturnToHoldingLocationName]" },
            };

            string extraCols = "";
            string extraSqlJoin = "";
            string sql = "";
            string baseSqlJoins = "";
            List<AttributeDto> attrs = new List<AttributeDto>();
            _queryParameters = [];

            (sql, baseSqlJoins, extraSqlJoin, extraCols, attrs) = await BuildBaseBookingRecordQuery(statisticFields, returnNotesCount, canBeSortedBy);

            string whereClause = @"
            WHERE [BookingRecord].[Deleted] = @deleted ";
            _queryParameters.Add("deleted", false);

            if (_utils.TenantId != null)
            {
                whereClause += @" AND [BookingRecord].[TenantId] = @tenantId ";
                _queryParameters.Add("tenantId", _utils.TenantId);
                if (tenantId != null && tenantId > 0) { throw new HttpRequestException($"tenantId is an invalid parameter for this tenant based user", null, System.Net.HttpStatusCode.BadRequest); }
            }
            else if (tenantId != null && tenantId > 0)
            {
                whereClause += @" AND [BookingRecord].[TenantId] = @tenantId ";
                _queryParameters.Add("tenantId", tenantId);
            }

            if (returnMyBookingsOnly == true)
            {
                string partA = "";
                string partB = "";
                // Only return bookings owned by the requesting user.
                if (_utils.UserId != null)
                {
                    partA = " ([BookingRecord].[BookingOwnerParentEntityType] = 'User' AND [BookingRecord].[BookingOwnerParentEntityId] = @bookingOwnerParentEntityId) ";
                    _queryParameters.Add("bookingOwnerParentEntityId", _utils.UserId);
                }
                if (ConfigBase.Schemas.ContainsKey("crm") && _utils.PartyId != null)
                {
                    partB = " ([BookingRecord].[BookingOwnerParentEntityType] = 'Party' AND [BookingRecord].[BookingOwnerParentEntityId] = @bookingOwnerParentEntityId) ";
                    _queryParameters.Add("bookingOwnerParentEntityId", _utils.PartyId);
                }

                if (!string.IsNullOrEmpty(partA) && !string.IsNullOrEmpty(partB))
                {
                    whereClause += $@"AND ({partA} OR {partB})";
                }
                else
                {
                    whereClause += $@"AND {partA} {partB}";
                    if (string.IsNullOrEmpty(partA) && string.IsNullOrEmpty(partB))
                    {
                        throw new HttpRequestException($"error building query for parameter returnMyBookingsOnly. No UserId or PartyId is available in context", null, System.Net.HttpStatusCode.BadRequest);
                    }
                }
            }

            if (!String.IsNullOrEmpty(bookingRecordReference))
            {
                _queryParameters.Add("bookingRecordReference", bookingRecordReference);
                whereClause += $@" AND [BookingRecordTo].[BookingRecordReference] LIKE '%' + @bookingRecordReference + '%' ";
            }
            if (bookingRecurringId != null && bookingRecurringId > 0)
            {
                _queryParameters.Add("bookingRecurringId", bookingRecurringId);
                whereClause += $@" AND [BookingRecord].[BookingRecurringId] = @bookingRecurringId ";
            }

            if (isRecurringBookingCustomised != null)
            {
                _queryParameters.Add("isRecurringBookingCustomised", isRecurringBookingCustomised);
                whereClause += $@" AND [BookingRecord].[IsRecurringBookingCustomised] = @isRecurringBookingCustomised ";
            }

            if (!String.IsNullOrEmpty(query))
            {
                _queryParameters.Add("query", query);
                whereClause += $@" AND ([BookingRecord].[BookingRecordReference] LIKE '%' + @query + '%'
                                        OR [BookingRecord].[CreatedByName] LIKE '%' + @query + '%') ";
            }

            if (!bookingRecordReference.IsNullOrEmpty() && bookingRecordReference != null)
            {
                whereClause += @" AND [BookingRecord].[BookingRecordReference] LIKE '%' + @bookingRecordReference ";
                _queryParameters.Add("bookingRecordReference", bookingRecordReference);
            }

            if (!bookingRecordStatusIds.IsNullOrEmpty() && bookingRecordStatusIds != null)
            {
                if (bookingRecordStatusIds.Contains(','))
                {
                    // Allow search on many status ids
                    whereClause += @" AND [BookingRecord].[BookingRecordStatusId] IN (@BookingRecordStatusIds) ";
                    _queryParameters.Add("BookingRecordStatusIds", bookingRecordStatusIds.Split(',').Select(x => Int16.Parse(x)).ToList());
                }
                else
                {
                    whereClause += @" AND [BookingRecord].[BookingRecordStatusId] = @BookingRecordStatusId ";
                    _queryParameters.Add("BookingRecordStatusId", Int16.Parse(bookingRecordStatusIds));
                }
            }
            else
            {
                // Exclude Cancelled by default.
                whereClause += @" AND [BookingRecord].[BookingRecordStatusId] != @BookingRecordStatusId ";
                _queryParameters.Add("BookingRecordStatusId", (short)BookingRecordStatusEnum.Cancelled);
            }

            if (bookingItemIds?.Count > 0)
            {
                //sql += " INNER JOIN [booking].[BookingRecordItem] BRI ON BRI.[BookingRecordId] = [BookingRecord].[BookingRecordId] AND BRI.[Deleted] = 0 ";
                _queryParameters.Add("bookingItemIds", bookingItemIds);
                whereClause += $@" AND BRI.[BookingItemId] IN (@bookingItemIds) ";
            }

            if (pickupFromHoldingLocationIds?.Count > 0)
            {
                _queryParameters.Add("pickupFromHoldingLocationIds", pickupFromHoldingLocationIds);
                whereClause += $@" AND [BookingRecord].[PickupFromHoldingLocationId] IN (@pickupFromHoldingLocationIds) ";
            }
            if (returnToHoldingLocationIds?.Count > 0)
            {
                _queryParameters.Add("returnToHoldingLocationIds", returnToHoldingLocationIds);
                whereClause += $@" AND [BookingRecord].[ReturnToHoldingLocationId] IN (@returnToHoldingLocationIds) ";
            }

            if (bookingOwnerParentEntityIds?.Count > 0)
            {
                _queryParameters.Add("bookingOwnerParentEntityIds", bookingOwnerParentEntityIds);
                whereClause += $@" AND [BookingRecord].[BookingOwnerParentEntityId] IN (@bookingOwnerParentEntityIds) ";
            }
            if (bookingOwnerParentEntityIntIds?.Count > 0)
            {
                _queryParameters.Add("bookingOwnerParentEntityIntIds", bookingOwnerParentEntityIntIds);
                whereClause += $@" AND [BookingRecord].[BookingOwnerParentEntityIntId] IN (@bookingOwnerParentEntityIntIds) ";
            }
            if (!string.IsNullOrEmpty(bookingOwnerParentEntityType))
            {
                _queryParameters.Add("bookingOwnerParentEntityType", bookingOwnerParentEntityType);
                whereClause += $@" AND [BookingRecord].[BookingOwnerParentEntityType] = @bookingOwnerParentEntityType ";
            }

            if (bookingRecordDateFrom != null)
            {
                whereClause += @" AND ([BookingRecord].[FromDateTime] >= @bookingRecordDateFrom OR [BookingRecord].[ToDateTime] >= @bookingRecordDateFrom ) ";
                _queryParameters.Add("bookingRecordDateFrom", bookingRecordDateFrom);
            }
            if (bookingRecordDateTo != null)
            {
                whereClause += @" AND ([BookingRecord].[FromDateTime] <= @bookingRecordDateTo OR [BookingRecord].[ToDateTime] <= @bookingRecordDateTo ) ";
                _queryParameters.Add("bookingRecordDateTo", bookingRecordDateTo);
            }

            if (createdOnDateFrom != null)
            {
                whereClause += @" AND [BookingRecord].[CreatedOn] >= @createdOnDateFrom ";
                _queryParameters.Add("createdOnDateFrom", createdOnDateFrom);
            }
            if (createdOnDateTo != null)
            {
                whereClause += @" AND [BookingRecord].[CreatedOn] <= @createdOnDateTo ";
                _queryParameters.Add("createdOnDateTo", createdOnDateTo);
            }
            if (createdByName != null && !string.IsNullOrEmpty(createdByName))
            {
                whereClause += @" AND [BookingRecord].[CreatedByName] = @createdByName ";
                _queryParameters.Add("createdByName", createdByName);
            }

            if (modifiedOnDateFrom != null)
            {
                whereClause += @" AND [BookingRecord].[ModifiedOn] >= @modifiedOnDateFrom ";
                _queryParameters.Add("modifiedOnDateFrom", modifiedOnDateFrom);
            }
            if (modifiedOnDateTo != null)
            {
                whereClause += @" AND [BookingRecord].[ModifiedOn] <= @modifiedOnDateTo ";
                _queryParameters.Add("modifiedOnDateTo", modifiedOnDateTo);
            }
            if (modifiedByName != null && !string.IsNullOrEmpty(modifiedByName))
            {
                whereClause += @" AND [BookingRecord].[ModifiedByName] = @modifiedByName ";
                _queryParameters.Add("modifiedByName", modifiedByName);
            }

            sql += whereClause;

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "BookingRecordId");

            sql += $"OFFSET @offset ROWS";
            sql += $" FETCH NEXT @limit ROWS ONLY";

            _queryParameters.Add("offset", standardListParameters.Offset ?? 0);
            _queryParameters.Add("limit", standardListParameters.Limit ?? 100);

            List<BookingRecordResponseCDto> result = new List<BookingRecordResponseCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {

                if (_queryParameters.Count > 0)
                {
                    foreach (var qp in _queryParameters)
                    {
                        command.AddArgument(qp.Key, qp.Value);
                    }
                }

                using (var reader = await command.SelectRaw())
                {
                    while (reader.Read())
                    {
                        var rec = GetBookingRecordRow(reader, statisticFields, attrs);
                        result.Add(rec);
                    }
                }
            }

            sql = @$"
            SELECT COUNT(*) AS totalNumOfRows
            
            {baseSqlJoins}
            {extraSqlJoin}
            ";

            sql += whereClause;
            var response = new ListResponseDto<BookingRecordResponseCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                if (_queryParameters.Count > 0)
                {
                    foreach (var qp in _queryParameters)
                    {
                        command.AddArgument(qp.Key, qp.Value);
                    }
                }

                response.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }
            response.List = result;
            return response;
        }

        internal BookingRecordResponseCDto GetBookingRecordRow(Microsoft.Data.SqlClient.SqlDataReader reader, string? statisticFields, List<AttributeDto> attrs)
        {
            BookingRecordResponseCDto rec = new BookingRecordResponseCDto();
            rec.BookingRecordStatusColour = !reader.IsDBNull(reader.GetOrdinal("BookingRecordStatusColour")) ? (reader.GetString(reader.GetOrdinal("BookingRecordStatusColour"))) : null;
            rec.BookingRecordStatusIcon = !reader.IsDBNull(reader.GetOrdinal("BookingRecordStatusIcon")) ? (reader.GetString(reader.GetOrdinal("BookingRecordStatusIcon"))) : null;
            rec.BookingRecordStatusLabel = !reader.IsDBNull(reader.GetOrdinal("BookingRecordStatusLabel")) ? (reader.GetString(reader.GetOrdinal("BookingRecordStatusLabel"))) : null;
            rec.BookingRecordStatusSortOrder = !reader.IsDBNull(reader.GetOrdinal("BookingRecordStatusSortOrder")) ? (reader.GetByte(reader.GetOrdinal("BookingRecordStatusSortOrder"))) : null;
            
            rec.BookingRecordReference = !reader.IsDBNull(reader.GetOrdinal("BookingRecordReference")) ? (reader.GetString(reader.GetOrdinal("BookingRecordReference"))) : null;
            rec.BookingRecurringId = !reader.IsDBNull(reader.GetOrdinal("BookingRecurringId")) ? (reader.GetInt32(reader.GetOrdinal("BookingRecurringId"))) : null;
            rec.IsRecurringBookingCustomised = !reader.IsDBNull(reader.GetOrdinal("IsRecurringBookingCustomised")) ? (reader.GetBoolean(reader.GetOrdinal("IsRecurringBookingCustomised"))) : null;
            rec.BookingRecordStatusId = !reader.IsDBNull(reader.GetOrdinal("BookingRecordStatusId")) ? (reader.GetByte(reader.GetOrdinal("BookingRecordStatusId"))) : null;
            rec.FromDateTime = !reader.IsDBNull(reader.GetOrdinal("FromDateTime")) ? (reader.GetDateTimeOffset(reader.GetOrdinal("FromDateTime"))) : null;
            rec.ToDateTime = !reader.IsDBNull(reader.GetOrdinal("ToDateTime")) ? (reader.GetDateTimeOffset(reader.GetOrdinal("ToDateTime"))) : null;
            rec.BookingOwnerParentEntityType = !reader.IsDBNull(reader.GetOrdinal("BookingOwnerParentEntityType")) ? (reader.GetString(reader.GetOrdinal("BookingOwnerParentEntityType"))) : null;
            rec.BookingOwnerParentEntityId = !reader.IsDBNull(reader.GetOrdinal("BookingOwnerParentEntityId")) ? (reader.GetGuid(reader.GetOrdinal("BookingOwnerParentEntityId"))) : null;
            rec.BookingOwnerParentEntityIntId = !reader.IsDBNull(reader.GetOrdinal("BookingOwnerParentEntityIntId")) ? (reader.GetInt64(reader.GetOrdinal("BookingOwnerParentEntityIntId"))) : null;
            rec.BookingOwnerName = !reader.IsDBNull(reader.GetOrdinal("BookingOwnerName")) ? (reader.GetString(reader.GetOrdinal("BookingOwnerName"))) : null;
            rec.Note = !reader.IsDBNull(reader.GetOrdinal("Note")) ? (reader.GetString(reader.GetOrdinal("Note"))) : null;
            rec.BookingItemIds = !reader.IsDBNull(reader.GetOrdinal("BookingItemIds")) ? (reader.GetString(reader.GetOrdinal("BookingItemIds"))) : null;
            rec.BookingItemNames = !reader.IsDBNull(reader.GetOrdinal("BookingItemNames")) ? (reader.GetString(reader.GetOrdinal("BookingItemNames"))) : null;
            rec.NotesCount = !reader.IsDBNull(reader.GetOrdinal("NotesCount")) ? (reader.GetInt32(reader.GetOrdinal("NotesCount"))) : null;
            rec.ModifiedByName = !reader.IsDBNull(reader.GetOrdinal("ModifiedByName")) ? (reader.GetString(reader.GetOrdinal("ModifiedByName"))) : null;
            rec.ModifiedOn = !reader.IsDBNull(reader.GetOrdinal("ModifiedOn")) ? (reader.GetDateTimeOffset(reader.GetOrdinal("ModifiedOn"))) : null;
            rec.CreatedByName = !reader.IsDBNull(reader.GetOrdinal("CreatedByName")) ? (reader.GetString(reader.GetOrdinal("CreatedByName"))) : null;
            rec.CreatedOn = !reader.IsDBNull(reader.GetOrdinal("CreatedOn")) ? (reader.GetDateTimeOffset(reader.GetOrdinal("CreatedOn"))) : DateTime.UtcNow;
            rec.TenantId = !reader.IsDBNull(reader.GetOrdinal("TenantId")) ? (reader.GetInt32(reader.GetOrdinal("TenantId"))) : null;
            rec.Deleted = !reader.IsDBNull(reader.GetOrdinal("Deleted")) ? (reader.GetBoolean(reader.GetOrdinal("Deleted"))) : false;
            rec.ProfileListingMediaUrl = !reader.IsDBNull(reader.GetOrdinal("ProfileListingMediaUrl")) ? (reader.GetString(reader.GetOrdinal("ProfileListingMediaUrl"))): null;
            rec.PickupFromHoldingLocationId = !reader.IsDBNull(reader.GetOrdinal("PickupFromHoldingLocationId")) ? (reader.GetInt32(reader.GetOrdinal("PickupFromHoldingLocationId"))) : null;
            rec.ReturnToHoldingLocationId = !reader.IsDBNull(reader.GetOrdinal("ReturnToHoldingLocationId")) ? (reader.GetInt32(reader.GetOrdinal("ReturnToHoldingLocationId"))) : null;
            rec.PickupFromHoldingLocationName = !reader.IsDBNull(reader.GetOrdinal("PickupFromHoldingLocationName")) ? (reader.GetString(reader.GetOrdinal("PickupFromHoldingLocationName"))) : null;
            rec.ReturnToHoldingLocationName = !reader.IsDBNull(reader.GetOrdinal("ReturnToHoldingLocationName")) ? (reader.GetString(reader.GetOrdinal("ReturnToHoldingLocationName"))) : null;

            rec.BookingRecordId = reader.GetInt64(reader.GetOrdinal("BookingRecordId"));

            rec.Fields = new Dictionary<string, object?>();
            foreach (var att in attrs)
            {
                if (att != null && att.AttributeCode != null && !reader.IsDBNull(reader.GetOrdinal($"Attr{att.AttributeCode}Value")))
                {
                    switch (att.AttributeValueTypeCode)
                    {
                        case "ValueString":
                            rec.Fields.Add($"{Char.ToLowerInvariant(att.AttributeCode[0]) + att.AttributeCode.Substring(1)}", !reader.IsDBNull(reader.GetOrdinal($"Attr{att.AttributeCode}Value")) ? (reader.GetString(reader.GetOrdinal($"Attr{att.AttributeCode}Value"))) : null);
                            break;
                        case "ValueStringMax":
                            rec.Fields.Add($"{Char.ToLowerInvariant(att.AttributeCode[0]) + att.AttributeCode.Substring(1)}", !reader.IsDBNull(reader.GetOrdinal($"Attr{att.AttributeCode}Value")) ? (reader.GetString(reader.GetOrdinal($"Attr{att.AttributeCode}Value"))) : null);
                            break;
                        case "ValueNumeric":
                            rec.Fields.Add($"{Char.ToLowerInvariant(att.AttributeCode[0]) + att.AttributeCode.Substring(1)}", !reader.IsDBNull(reader.GetOrdinal($"Attr{att.AttributeCode}Value")) ? (reader.GetDecimal(reader.GetOrdinal($"Attr{att.AttributeCode}Value"))).ToString() : null);
                            break;
                        case "ValueDateTime":
                            rec.Fields.Add($"{Char.ToLowerInvariant(att.AttributeCode[0]) + att.AttributeCode.Substring(1)}", !reader.IsDBNull(reader.GetOrdinal($"Attr{att.AttributeCode}Value")) ? (reader.GetDateTimeOffset(reader.GetOrdinal($"Attr{att.AttributeCode}Value"))).ToString() : null);
                            break;
                        default:
                            break;
                    }
                }
            }

            if (!string.IsNullOrEmpty(statisticFields))
            {
                rec.Statistics = [];
                foreach (var statFld in statisticFields.Split(','))
                {
                    if (!reader.IsDBNull(reader.GetOrdinal($"Stat{statFld}Value")))
                    {
                        rec.Statistics.Add(_utils.LowercaseFirstLetter(statFld), !reader.IsDBNull(reader.GetOrdinal($"Stat{statFld}Value")) ? (reader.GetDecimal(reader.GetOrdinal($"Stat{statFld}Value"))) : null);
                    }
                }
            }

            return rec;

        }

        private async Task<List<AttributeDto>> GetAttributes()
        {
            string cacheKey = "AllAttributesForBooking";
            List<AttributeDto>? cacheValue;

            if (_memoryCache.TryGetValue(cacheKey, out cacheValue) && cacheValue != null)
            {
                return cacheValue;
            }

            string sql = @$"
                        SELECT *
                        FROM [listing].[Attribute]";
            var result = new List<AttributeDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                result = await command.SelectMany<AttributeDto>();
            }

            if (result != null && result.Count > 0)
            {
                cacheValue = result;
                _memoryCache.Set(cacheKey, cacheValue, CacheOptions());

                return result;
            }

            return null;
        }

    }
}
