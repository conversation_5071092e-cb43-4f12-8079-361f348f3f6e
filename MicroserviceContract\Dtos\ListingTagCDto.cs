﻿using Sql;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MicroserviceContract.Dtos
{
    [Mappable(nameof(ListingTagId))]
    public class ListingTagCDto
    {
        public long ListingTagId { get; set; }
        /// <summary>
        /// Unique internal database tagId that identifies a tag
        /// </summary>
        public int TagId { get; set; }
        /// <summary>
        /// The Tags Label - this is what is displayed
        /// </summary>
        public string? TagLabel { get; set; }
        /// <summary>
        /// Optional Tag Set Id the Tag belongs to
        /// A Tag can belong to 1 set at most
        /// </summary>
        public int? TagSetId { get; set; }
        /// <summary>
        /// The Tag Sets Label (display Name)
        /// </summary>
        public string? TagSetLabel { get; set; }
        public string? TagSetIcon { get; set; }
        public string? TagSetNote { get; set; }
        /// <summary>
        /// The Listing the Tag has been associated with
        /// </summary>
        public long ListingId { get; set; }
        /// <summary>
        /// Optional sort order to control the order tags are displayed for a listing
        /// </summary>
        public int? SortOrder { get; set; }
        /// <summary>
        /// Optional Icon that can be displayed with or instead of the tag label
        /// </summary>
        public string? TagIcon { get; set; }
        /// <summary>
        /// Optional colour that the Label or Icon can be displayed with (could be text or background depending on system)
        /// </summary>
        public string? TagColour { get; set; }
        /// <summary>
        /// Optional image url that can be displayed with or instead of the tag label or icon
        /// </summary>
        public string? TagImageUrl { get; set; }
        /// <summary>
        /// Optional note for a tag. This could be displayed when a user hovers over or clicks on a tag
        /// </summary>
        public string? TagNote { get; set; }
        /// <summary>
        /// When the tag was added to the listing
        /// </summary>
        public DateTimeOffset CreatedOn { get; set; }
        /// <summary>
        /// The name of the user who added the tag
        /// </summary>
        public string? CreatedByName { get; set; }
        /// <summary>
        /// When the tag was modified against the listing (ie. change sort order)
        /// </summary>
        public DateTimeOffset? ModifiedOn { get; set; }
        public string? ModifiedByName { get; set; }
    }

    public class ListingTagListCDto : ListingTagCDto
    {

    }

    [Mappable(nameof(TagId))]
    public class TagCDto
    {
        /// <summary>
        /// Unique internal database tagId
        /// </summary>
        public int? TagId { get; set; }
        /// <summary>
        /// The Tags Label - this is what is displayed
        /// </summary>
        public string? Label { get; set; }
        /// <summary>
        /// Optional Tag Set Id the Tag belongs to
        /// A Tag can belong to 1 set at most
        /// </summary>
        public int? TagSetId { get; set; }
        /// <summary>
        /// The Tag Sets Label (display Name)
        /// </summary>
        public string? TagSetLabel { get; set; }
        public string? TagSetIcon { get; set; }
        public string? TagSetNote { get; set; }
        /// <summary>
        /// Optional order the tags should be displayed in
        /// </summary>
        public int? SortOrder { get; set; }
        /// <summary>
        /// Tag can be displayed/used when true
        /// </summary>
        public bool IsEnabled { get; set; }
        /// <summary>
        /// Optional Icon that can be displayed with or instead of the tag label
        /// </summary>
        public string? Icon { get; set; }
        /// <summary>
        /// Optional colour that the Label or Icon can be displayed with (could be text or background depending on system)
        /// </summary>
        public string? Colour { get; set; }
        /// <summary>
        /// Optional image url that can be displayed with or instead of the tag label or icon
        /// </summary>
        public string? ImageUrl { get; set; }
        /// <summary>
        /// Optional note for a tag. This could be displayed when a user hovers over or clicks on a tag
        /// </summary>
        public string? Note { get; set; }
    }

    [Mappable(nameof(TagSetId))]
    public class TagSetCDto
    {
        /// <summary>
        /// The unique internal TagSetId
        /// </summary>
        public int? TagSetId { get; set; }
        /// <summary>
        /// Label that describes the tag set and may be displayed on a front-end
        /// </summary>
        public string? Label { get; set; }
        /// <summary>
        /// Optional sort order to control the order Tag Sets are displayed in
        /// </summary>
        public int? SortOrder { get; set; }
        /// <summary>
        /// When True a Tag Set is enabled for use or display
        /// </summary>
        public bool IsEnabled { get; set; }
        /// <summary>
        /// Optional Icon that can be displayed with or instead of the tag set label
        /// </summary>
        public string? Icon { get; set; }
        /// <summary>
        /// Optional note for a tag set. This could be displayed when a user hovers over or clicks on a tag set
        /// </summary>
        public string? Note { get; set; }
        public int? ParentTagSetId { get; set; }

    }
}
