﻿using MicroserviceBackendListing.BusinessLogic.Base;
using MicroserviceBackendListing.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using MicroserviceContract.Dtos.Booking;
using Microsoft.AspNetCore.Mvc;
using Sql;
using MicroserviceBackendListing.BusinessLogic.Booking.Management;
using MicroserviceBackendListing.Policies;
using Microsoft.AspNetCore.Authorization;

namespace MicroserviceBackendListing.Services
{
    [Route("api/BookingHoldingLocation")]
    public class HoldingLocationController : AppController
    {
        private readonly HoldingLocation _holdingLocation;
        public HoldingLocationController(HoldingLocation holdingLocation, IUnitOfWork unitOfWork)
        {
            _holdingLocation = holdingLocation;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a Single Holding Location by HoldingLocationId
        /// </summary>
        /// <param name="holdingLocationId">The id of the holding location to be returned</param>
        /// <returns></returns>
        [HttpGet]
        [Route("Get")]
        [ProducesResponseType(typeof(HoldingLocationResponseCDto), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetAsync(int holdingLocationId)
        {
            if (holdingLocationId <= 0) { throw new HttpRequestException("parameter holdingLocationId is required", null, System.Net.HttpStatusCode.BadRequest); }

            var result = await _holdingLocation.GetHoldingLocationAsync(holdingLocationId);
            return Ok(result);
        }

        /// <summary>
        /// Return a list of Holding Locations
        /// </summary>
        /// <param name="standardListParameters"></param>
        /// <param name="query"></param>
        /// <param name="holdingLocationIds"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("ListHoldingLocations")]
        public async Task<IActionResult> ListAsync(
            [FromQuery] StandardListParameters standardListParameters,
            [FromQuery] string? query = null,
            [FromQuery] string? holdingLocationIds = null
           )
        {
            var logic = _holdingLocation;
            List<int>? holdingLocationIdsList = null;
            if (!string.IsNullOrEmpty(holdingLocationIds))
            {
                holdingLocationIdsList = holdingLocationIds.Split(',').Select(x => int.Parse(x)).ToList();
            }

            var result = await logic.ListHoldingLocationAsync(standardListParameters: standardListParameters,
                                                  query: query,
                                                  holdingLocationIds: holdingLocationIdsList);

            return Ok(result);
        }

        /// <summary>
        /// When Listing is been used as the Parent Entity for Holding Locations.
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("CreateListingAndHoldingLocation")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        [ProducesResponseType(typeof(long), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> CreateListingAndHoldingLocation([FromBody] RequestCDto<HoldingLocationRequestCDto> dto)
        {
            if (dto == null || dto.Data == null)
            {
                throw new HttpRequestException("Invalid dto request. Missing Data", null, System.Net.HttpStatusCode.BadRequest);
            }
            var logic = _holdingLocation;

            long holdingLocationId = await logic.CreateListingAndHoldingLocationPair(dto);

            _unitOfWork.Commit();
            return Ok(holdingLocationId);
        }

        [HttpPost]
        [Route("UpdateListingAndHoldingLocation")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        public async Task<IActionResult> UpdateListingAndHoldingLocationPair(int? holdingLocationId, [FromBody] RequestCDto<HoldingLocationRequestCDto> dto)
        {
            if (holdingLocationId == null || holdingLocationId <= 0) { throw new HttpRequestException("parameter holdingLocationId is required", null, System.Net.HttpStatusCode.BadRequest); }

            if (dto == null || dto.Data == null)
            {
                throw new HttpRequestException("Invalid dto request. Missing Data", null, System.Net.HttpStatusCode.BadRequest);
            }

            var logic = _holdingLocation;
            await logic.UpdateListingAndHoldingLocationPair((int)holdingLocationId, dto);
            _unitOfWork.Commit();

            return Ok();
        }

        [HttpPost]
        [Route("DeleteHoldingLocation")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        public async Task<IActionResult> DeleteHoldingLocation(int? holdingLocationId)
        {
            if (holdingLocationId == null || holdingLocationId <= 0) { throw new HttpRequestException("parameter holdingLocationId is required", null, System.Net.HttpStatusCode.BadRequest); }

            var logic = _holdingLocation;
            await logic.DeleteHoldingLocation((int)holdingLocationId);
            _unitOfWork.Commit();

            return Ok();
        }

    }
}
