﻿//using System.Drawing;

using Microsoft.AspNetCore.Mvc;
using MicroserviceBackendListing.Interfaces;
using MicroserviceBackendListing.BusinessLogic;
using MicroserviceContract.Dtos;
using Sql;
using System;
using System.Data.SqlTypes;
using System.Text.RegularExpressions;
using MicroserviceContract;
using MicroserviceBackendListing.BusinessLogic.Base;
using MicroserviceContract.Dtos.ListingManagement;
using Amazon.S3.Model;
using Amazon.Runtime.CredentialManagement.Internal;
using MicroserviceBackendListing.Policies;
using Microsoft.AspNetCore.Authorization;

namespace MicroserviceBackendListing.Services
{

	[Route("api/FileManager")]
	public class FileManagerController : AppController
	{
		ILogger logger;
		ICloudStorage cloud;
		ManageListing _manageListing;
		ListingMedia _listingMedia;

		public FileManagerController(IUnitOfWork unitOfWork, ILogger logg, ICloudStorage a, ManageListing ManageListing, ListingMedia listingMedia)
        {
            _unitOfWork = unitOfWork;
            logger = logg;
            cloud = a;
            _manageListing = ManageListing;
            _listingMedia = listingMedia;
        }

        [HttpGet]
		// [ClaimAuthorize(AppClaims.Permissions.AccessFiles)]
		[Route("GetAuthenticatedUrl")]
		public async Task<IActionResult> GetAuthenticatedUrl([FromQuery] string baseUrl)
        {
            var uri = await cloud.GetAuthenticatedUrl(baseUrl, CloudItemPermission.Read);
            return Ok(uri);
		}

        //[HttpPost]
        //[Route("Delete")]
        //public async Task<IActionResult> Delete([FromQuery] Guid mediaListingId)
        //{
        //	var file = await _manageListing.getMediaListingAsync;
        //	if (file == null)
        //	{
        //		return BadRequest(new Exception("File not found"));
        //	}
        //	else
        //          {
        //              await cloud.Delete(file.PathOrUrl);
        //          }

        //          await _fileFactory().Delete(fileId, deleteAllVersions);
        //	_unitOfWork.Commit();
        //	return Ok();
        //}

        /// <summary>
        /// Used to upload a file to Azure and create all related tables
        /// </summary>
        /// <param name="file">The file to upload</param>
        /// <param name="cloudFilename">The name to display (eg. Photo.png)</param>
        /// <param name="fileCategoryCode">The type of file (from the FileCategory table)</param>
        /// <param name="isPublic">Set to true to create a permanent url, set to false to have urls expire after 7 days</param>
        /// <param name="listingId"></param>
        /// <param name="sortOrder">Sort order</param>
        /// <returns></returns>
        [HttpPost]
		[Route("UploadFile")]
        [Authorize(Policy = PolicyType.UploadFilePolicy)]
        public async Task<ManageListingMediaCDto> UploadFile(IFormFile file, [FromQuery] string cloudFilename, [FromQuery] string fileCategoryCode, [FromQuery] bool isPublic, long? listingId, [FromQuery] int? sortOrder = null)
		{
            try
            {
                // Save file locally
                string fileName = file.FileName;
				string folderName = Path.Join(Directory.GetCurrentDirectory(), "Uploads");
				
				string uniqueFileName = cloudFilename.Split("/")[0];
				uniqueFileName = EnsureUniqueDestination(folderName, fileName);

                string path = Path.Join(folderName, uniqueFileName);

				if (!Directory.Exists(folderName))
				{
					Directory.CreateDirectory(folderName);
				}

				using (FileStream fileStream = System.IO.File.Create(path))
				{
					file.CopyTo(fileStream);
                }
				string fileType = file.ContentType;
				string mediaType = "";
				if (fileType.Contains("image")) {
					mediaType = "image";
				} 
				if (fileType.Contains("pdf"))
				{
                    mediaType = "pdf";
                }
                if (fileType.Contains("video"))
                {
                    mediaType = "video";
                }

				var fileGuid = Guid.NewGuid();
                var cloudFilePath = Path.GetFileNameWithoutExtension(cloudFilename) + fileGuid + Path.GetExtension(cloudFilename);
                var mediaUrl = await cloud.UploadFileAsync(path, "test-base", cloudFilePath, isPublic, true);
                ManageListingMediaCDto mediaDto = new ManageListingMediaCDto()
                {
                    ListingMediaId = 0,
                    Title = cloudFilename,
                    ListingId = listingId != null ? (long)listingId : 0,
                    MediaUrl = mediaUrl,
                    MediaCategoryCode = fileCategoryCode,
                    MediaTypeCode = mediaType,
                    Deleted = false,
                };
                var listingMedia = mediaDto;
                if (listingId.HasValue)
				{
                    listingMedia = await _manageListing.AddMedia(mediaDto.ListingId, mediaDto);
				}
				_unitOfWork.Commit();
				return (listingMedia);
            }
			catch (Exception ex)
			{
				logger.LogError(ex, "UploadToAmazon");
				throw;
			}
		}

		//simply adds a digit on to the filname if it already exists. I.e if myfile.jpg exists then this will return myfile #2.jpg
		private string EnsureUniqueDestination(string folder, string fileName)
		{
			string path = Path.Join(folder, fileName);
			if (!System.IO.File.Exists(path))
			{
				return fileName;
			}
			else
			{
				int count = 2;
				string[] splitName = fileName.Split('.');
				//make sure file extension is tehre
				if (splitName.Length > 1)
				{
					string newName = "";
					for (int ii = 0; ii < splitName.Length - 2; ii++)
					{
						newName += splitName[ii];
						newName += ".";
					}
					newName += splitName[splitName.Length - 2];
					newName += " #" + count + "." + splitName[splitName.Length - 1];
					while (System.IO.File.Exists(folder + newName))
					{
						count++;
						newName = "";
						for (int ii = 0; ii < splitName.Length - 2; ii++)
						{
							newName += splitName[ii];
							newName += ".";
						}
						newName += splitName[splitName.Length - 2];
						newName += " #" + count + "." + splitName[splitName.Length - 1];
					}
					return newName;
				}
				else
				{
					//in case no file extension
					while (System.IO.File.Exists(folder + fileName + " #" + count))
					{
						count++;
					}
					return fileName + " #" + count;
				}
			}
		}
	}
}