﻿using MicroserviceBackendListing.BusinessLogic.Base;

using MicroserviceContract.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Collections.Specialized;

namespace MicroserviceBackendListing.BusinessLogic
{
    public class Favourite : BusinessLogicBase
    {
        private UtilityFunctions _utils;
        private ManageListing _manageListingFactory;

        public Favourite(IUnitOfWork u, UtilityFunctions utils, ManageListing manageListingFacory)
        {
            _unitOfWork = u;
            _utils = utils;
            _manageListingFactory = manageListingFacory;
        }

        public async Task<GetFavouriteCDto> GetAsync(int favouriteId)
        {
            string sql = @"
                 SELECT 
                         [Favourite].[FavouriteId]
                        ,[Favourite].[FavouriteSetId]
                        ,[Favourite].[ListingId]
                        ,[Listing].[ReferenceNo]
                        ,[Favourite].[ParentEntityId]
                        ,[Favourite].[ParentEntityType]
                        ,[Favourite].[Note]
                        ,[Favourite].[SortOrder]
                  FROM   [listing].[Favourite]
             LEFT JOIN   [listing].[Listing] ON [Favourite].[ListingId] = [Listing].[ListingId]
                 WHERE   [FavouriteId] = @FavouriteId";

            var result = new GetFavouriteCDto();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("FavouriteId", favouriteId);
                result = await command.SelectSingle<GetFavouriteCDto>();
            }

            return result;
        }

        public async Task<ListResponseDto<GetFavouriteCDto>> GetListAsync(StandardListParameters standardListParameters, Guid parentEntityId, int? setId)
        {
            if (setId != null && await GetSetAsync(setId.Value) == null)
            {
                throw new ApiErrorException($"Cannot find a match for this '{setId}' FavouriteSetId");
            }

            if (standardListParameters == null)
            {
                standardListParameters = new StandardListParameters();
            }

            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "SortOrder", "[Favourite].[SortOrder]" },
                { "ParentEntityId", "[Favourite].[ParentEntityId]" },
                { "ParentEntityType", "[Favourite].[ParentEntityType]" },
                { "ListingId", "[Favourite].[ListingId]" },
                { "FavouriteSetId", "[Favourite].[FavouriteSetId]" }
            };

            string sql = @"
            SELECT 
                         [Favourite].[FavouriteId]
                        ,[Favourite].[FavouriteSetId]
                        ,[Favourite].[ParentEntityId]
                        ,[Favourite].[ParentEntityType]
                        ,[Favourite].[Note]
                        ,[Favourite].[SortOrder]
            FROM         [listing].[Favourite] ";

            string whereCondition = " WHERE [Favourite].[ParentEntityId] = @ParentEntityId ";

            if (setId != null)
            {
                whereCondition += " AND [Favourite].[FavouriteSetId] = @FavouriteSetId ";
            }

            sql += whereCondition;
            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "ParentEntityId, FavouriteSetId, SortOrder");
            sql += $" OFFSET @Offset ROWS";
            sql += $" FETCH NEXT @Limit ROWS ONLY";

            var response = new ListResponseDto<GetFavouriteCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ParentEntityId", parentEntityId);
                command.AddArgument("FavouriteSetId", setId);
                command.AddArgument("Offset", standardListParameters.Offset);
                command.AddArgument("Limit", standardListParameters.Limit);

                response.List = await command.SelectMany<GetFavouriteCDto>();
            }

            sql = @"SELECT COUNT(*) AS totalNumOfRows
                    FROM [listing].[Favourite] ";
            sql += whereCondition;

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ParentEntityId", parentEntityId);
                command.AddArgument("FavouriteSetId", setId);
                response.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return response;
        }

        public async Task<GetFavouriteCDto> AddAsync(string referenceNo, Guid parentEntityId, string parentEntityType, int sortOrder, int? setId = null)
        {
            // Check if listing exists
            var listingId = await _manageListingFactory.GetListingId(referenceNo);
            if (listingId == null)
            {
                throw new ApiErrorException($"Cannot find a match for this '{referenceNo}' ReferenceNo");
            }

            // Check if FavouriteSet exists
            if (setId != null && await GetSetAsync(setId.Value) == null)
            {
                throw new ApiErrorException($"Cannot find a match for this '{setId}' FavouriteSetId");
            }

            string sql = @"
            INSERT INTO  [listing].[Favourite]
                        (
                         [FavouriteSetId]
                        ,[ListingId]
                        ,[ParentEntityId]
                        ,[ParentEntityType]
                        ,[Note]
                        ,[SortOrder]
                        ,[CreatedOn]
                        ,[CreatedByName]
                        ,[ModifiedOn]
                        ,[ModifiedByName]
                        ,[Deleted]
                        )
                VALUES
                        (
                         @FavouriteSetId
                        ,@ListingId
                        ,@ParentEntityId
                        ,@ParentEntityType
                        ,NULL
                        ,@SortOrder
                        ,@CreatedOn
                        ,@CreatedByName
                        ,NULL
                        ,NULL
                        ,0
                        )";

            int favouriteId = 0;
            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArgument("ListingId", listingId);
                command.AddArgument("ParentEntityId", parentEntityId);
                command.AddArgument("ParentEntityType", parentEntityType);
                command.AddArgument("FavouriteSetId", setId);
                command.AddArgument("SortOrder", sortOrder);
                command.AddArgument("CreatedOn", DateTimeOffset.UtcNow);
                command.AddArgument("CreatedByName", _utils.UserFullName);

                favouriteId = await command.ExecuteAndReturnIdentity();
            }
            var result = await GetAsync(favouriteId);

            return result;
        }

        public async Task RemoveAsync(string referenceNo, Guid parentEntityId)
        {
            // Check if listing exists
            var listingId = await _manageListingFactory.GetListingId(referenceNo);
            if (listingId == null)
            {
                throw new ApiErrorException($"Cannot find a match for this '{referenceNo}' ReferenceNo");
            }

            string sql = @"
            UPDATE   [listing].[Favourite]
            SET
                     [Deleted] = 1
                    ,[ModifiedOn] = @ModifiedOn
                    ,[ModifiedByName] = @ModifiedByName
            WHERE    [ListingId] = @ListingId AND [ParentEntityId] = @ParentEntityId ";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Delete, sql))
            {
                command.AddArgument("ModifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("ModifiedByName", _utils.UserFullName);
                command.AddArgument("ListingId", listingId);
                command.AddArgument("ParentEntityId", parentEntityId);

                await command.Execute();
            }
        }

        public async Task<ListResponseDto<GetFavouriteCDto>> ListAsync(StandardListParameters standardListParameters, int? favouriteSetId = null, string? referenceNo = null, Guid? parentEntityId = null)
        {
            if (standardListParameters == null)
            {
                standardListParameters = new StandardListParameters();
            }

            var listingId = await _manageListingFactory.GetListingId(referenceNo);
            if (listingId == null)
            {
                throw new ApiErrorException($"Cannot find a match for this '{listingId}' ListingId");
            }

            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "SortOrder", "[Favourite].[SortOrder]" },
                { "ParentEntityId", "[Favourite].[ParentEntityId]" },
                { "ParentEntityType", "[Favourite].[ParentEntityType]" },
                { "ListingId", "[Favourite].[ListingId]" },
                { "FavouriteSetId", "[Favourite].[FavouriteSetId]" }
            };

            string sql = @" 
            SELECT 
                         [Favourite].[FavouriteId]
                        ,[Favourite].[FavouriteSetId]
                        ,[Favourite].[ParentEntityId]
                        ,[Favourite].[ParentEntityType]
                        ,[Favourite].[Note]
                        ,[Favourite].[SortOrder]
            FROM         [listing].[Favourite] ";

            string whereCondition = "";
            if (favouriteSetId != null || referenceNo != null || parentEntityId != null)
            {
                var whereClauses = new List<string>();
                if (favouriteSetId != null) whereClauses.Add("[FavouriteSetId] = @FavouriteSetId");
                if (referenceNo != null) whereClauses.Add("[ListingId] = @ListingId");
                if (parentEntityId != null) whereClauses.Add("[ParentEntityId] = @ParentEntityId");
                whereCondition += " WHERE " + string.Join(" AND ", whereClauses) + " ";
            }

            sql += whereCondition;
            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "ParentEntityId, SortOrder, ParentEntityType");
            sql += $" OFFSET @offset ROWS";
            sql += $" FETCH NEXT @limit ROWS ONLY";

            var response = new ListResponseDto<GetFavouriteCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ListingId", listingId);
                command.AddArgument("FavouriteSetId", favouriteSetId);
                command.AddArgument("ParentEntityId", parentEntityId);
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);

                response.List = await command.SelectMany<GetFavouriteCDto>();
            }

            sql = @"
            SELECT COUNT(*) AS totalNumOfRows
            FROM [listing].[Favourite] ";
            sql += whereCondition;

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ListingId", listingId);
                command.AddArgument("FavouriteSetId", favouriteSetId);
                command.AddArgument("ParentEntityId", parentEntityId);

                response.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return response;
        }

        public async Task<GetFavouriteSetCDto> GetSetAsync(int setId)
        {
            string sql = @"
            SELECT 
                     [FavouriteSetId]
                    ,[Name]
                    ,[ParentEntityId]
                    ,[ParentEntityType]
                    ,[SortOrder]
            FROM     [listing].[FavouriteSet]
            WHERE    [FavouriteSetId] = @FavouriteSetId";

            var result = new GetFavouriteSetCDto();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("FavouriteSetId", setId);
                result = await command.SelectSingle<GetFavouriteSetCDto>();
            }

            return result;
        }

        public async Task<GetFavouriteSetCDto> AddSetAsync(GetFavouriteSetCDto dto)
        {
            string sql = @"
            INSERT INTO  [listing].[FavouriteSet]
                        (
                         [Name]
                        ,[ParentEntityId]
                        ,[ParentEntityType]
                        ,[SortOrder]
                        ,[CreatedOn]
                        ,[CreatedByName]
                        ,[ModifiedOn]
                        ,[ModifiedByName]
                        ,[Deleted]
                        )
                VALUES
                        (
                         @Name
                        ,@ParentEntityId
                        ,@ParentEntityType
                        ,@SortOrder
                        ,@CreatedOn
                        ,@CreatedByName
                        ,NULL
                        ,NULL
                        ,0
                        )";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArguments(dto);
                command.AddArgument("CreatedOn", DateTimeOffset.UtcNow);
                command.AddArgument("CreatedByName", _utils.UserFullName);

                await command.Execute();
            }

            var result = await GetSetAsync(dto.FavouriteSetId);

            return result;
        }

        public async Task DeleteSetAsync(int setId)
        {
            var exists = await GetSetAsync(setId);
            if (exists == null)
            {
                throw new ApiErrorException($"Cannot find a match for this '{setId}' FavouriteSetId");
            }

            string sql = @"
            UPDATE   [listing].[FavouriteSet]
            SET
                     [Deleted] = 1
                    ,[ModifiedOn] = @ModifiedOn
                    ,[ModifiedByName] = @ModifiedByName
            WHERE    [FavouriteSetId] = @FavouriteSetId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Delete, sql))
            {
                command.AddArgument("ModifiedOn", DateTime.UtcNow);
                command.AddArgument("ModifiedByName", _utils.UserFullName);
                command.AddArgument("FavouriteSetId", setId);

                await command.Execute();
            }
        }

        public async Task UpdateSetAsync(GetFavouriteSetCDto dto)
        {
            var exists = await GetSetAsync(dto.FavouriteSetId);
            if (exists == null)
            {
                throw new ApiErrorException($"Cannot find a match for this '{dto.FavouriteSetId}' FavouriteSetId");
            }

            string sql = @"
            UPDATE  [listing].[FavouriteSet]
            SET
                     [Name] = @Name
                    ,[ModifiedOn] = @ModifiedOn
                    ,[ModifiedByName] = @ModifiedByName
            WHERE    [FavouriteSetId] = @FavouriteSetId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArguments(dto);
                command.AddArgument("ModifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("ModifiedByName", _utils.UserFullName);
                await command.Execute();
            }
        }

        public async Task<List<GetFavouriteSetCDto>> GetSetListsAsync(Guid parentEntityId)
        {
            string sql = @"
            SELECT 
                     [FavouriteSetId]
                    ,[Name]
                    ,[ParentEntityId]
                    ,[ParentEntityType]
                    ,[SortOrder]
            FROM     [listing].[FavouriteSet]
            WHERE    [ParentEntityId] = @ParentEntityId";

            var result = new List<GetFavouriteSetCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ParentEntityId", parentEntityId);
                result = await command.SelectMany<GetFavouriteSetCDto>();
            }

            return result;
        }

        public async Task AddListingNoteAsync(int favouriteId, string note)
        {
            var exists = await GetAsync(favouriteId);
            if (exists == null)
            {
                throw new ApiErrorException($"Cannot find a match for this '{favouriteId}' FavouriteId");
            }

            string sql = @"
            UPDATE  [listing].[Favourite]
            SET
                     [Note] = @Note
                    ,[ModifiedOn] = @ModifiedOn
                    ,[ModifiedByName] = @ModifiedByName
            WHERE    [FavouriteId] = @FavouriteId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("Note", note);
                command.AddArgument("FavouriteId", favouriteId);
                command.AddArgument("ModifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("ModifiedByName", _utils.UserFullName);
                await command.Execute();
            }
        }

        public async Task UpdateListingNoteAsync(int favouriteId, string note)
        {
            var exists = await GetAsync(favouriteId);
            if (exists == null)
            {
                throw new ApiErrorException($"Cannot find a match for this '{favouriteId}' FavouriteId");
            }

            string sql = @"
            UPDATE  [listing].[Favourite]
            SET
                     [Note] = @Note
                    ,[ModifiedOn] = @ModifiedOn
                    ,[ModifiedByName] = @ModifiedByName
            WHERE    [FavouriteId] = @FavouriteId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("Note", note);
                command.AddArgument("FavouriteId", favouriteId);
                command.AddArgument("ModifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("ModifiedByName", _utils.UserFullName);
                await command.Execute();
            }
        }

        public async Task DeleteListingNoteAsync(int favouriteId)
        {
            var exists = await GetAsync(favouriteId);
            if (exists == null)
            {
                throw new ApiErrorException($"Cannot find a match for this '{favouriteId}' FavouriteId");
            }

            string sql = @"
            UPDATE  [listing].[Favourite]
            SET
                     [Note] = NULL
                    ,[ModifiedOn] = @ModifiedOn
                    ,[ModifiedByName] = @ModifiedByName
            WHERE    [FavouriteId] = @FavouriteId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("FavouriteId", favouriteId);
                command.AddArgument("ModifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("ModifiedByName", _utils.UserFullName);
                await command.Execute();
            }
        }

        private async Task<List<Int64>> GetFavouriteListingIds(Guid parentEntityId)
        {
            string cacheKey = "GetFavouriteListingIds" + parentEntityId.ToString();
            //todo fix caching as its causing a error
            //if (_memoryCache.TryGetValue(cacheKey, out List<Int64> cacheValue))
            //{
            //    return cacheValue;
            //}

            string sql = @"
                 SELECT [Favourite].[ListingId]
                  FROM   [listing].[Favourite]
                 WHERE   [Deleted] = 0 AND [Favourite].[ParentEntityId] = @ParentEntityId";

            var result = new List<long>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ParentEntityId", parentEntityId);
                using (var reader = await command.SelectRaw())
                {
                    while (reader.Read())
                    {
                        result.Add(reader.GetInt64(reader.GetOrdinal("ListingId")));
                    }
                }

            }
            //_memoryCache.Set(cacheKey, result, CacheOptions());

            return result;
        }

        public async Task<bool> IsListingFavouriteForLoggedInUser(Int64 listingId)
        {
            if (_utils.PartyId == null) { return false; }
            var userFavs = await GetFavouriteListingIds((Guid)_utils.PartyId);
            if (userFavs != null && userFavs.Exists(x => x == listingId)) { return true; }

            return false;
        }
    }
}
