/************************************************************************************************
All standard Workflow Tables, Indexes and Views created here.
Feature based tables are in feature specific sql files.
**************************************************************************************************/

-- ******************** SqlDBM: Microsoft SQL Server ********************
-- ** Generated by SqlDBM: Redi Base <NAME_EMAIL> *


IF NOT EXISTS (SELECT * FROM sys.schemas s WHERE s.name='workflow')
EXEC ('CREATE SCHEMA [workflow]');
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Workflow and scheduled or demand job tables.', @level0type = N'SCHEMA', @level0name = N'workflow';
GO
-- ************************************** [workflow].[WorkflowStepType]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='workflow' and t.name='WorkflowStepType')
CREATE TABLE [workflow].[WorkflowStepType]
(
 [WorkflowStepTypeId] smallint NOT NULL ,
 [Label]              nvarchar(100) NOT NULL ,
 [IsEnabled]          bit NOT NULL CONSTRAINT [DF_WorkflowStepType_IsEnabled] DEFAULT 1 ,
 [SortOrder]          smallint NOT NULL ,
 [Description]        nvarchar(1000) NULL ,

 CONSTRAINT [PK_1] PRIMARY KEY CLUSTERED ([WorkflowStepTypeId] ASC)
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Identifies the behaviour of each Workflow Step.
1 - Execute Via Queue
A message will be placed into the steps QueueName in order to execute the step. The function associated with the queue must be workflow enabled

2 - Notification Event
An event will be sent to the Notification Event Queue in order to trigger any required notification processing - email/sms/app, etc

3 - Condition
Evaluate conditions in order to determine next path to take - first true condition is the path taken

4 - Wait for Dependencies
Pause processing until one or more named dependencies are met. This could be input from an external system, end user input, etc

5 - Branch
Branch down multiple paths. If child path has a condition it will only be executed if it evaluates to true. All child paths with no condition will always be executed. Each path will result in a SUB WorkflowJob been created', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WorkflowStepType';
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Defines the type of each step (how each step behaves):
1 - Execute Via Queue
2 - Notification Event
3 - Condition
4 - Wait for Dependencies
5 - Branch', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WorkflowStepType', @level2type=N'COLUMN', @level2name=N'WorkflowStepTypeId';
GO

-- ************************************** PostScript

INSERT INTO [workflow].[WorkflowStepType]
(WorkflowStepTypeId, [Label], [IsEnabled], [SortOrder], [Description])
VALUES
(1, 'Execute Via Queue', 1, 1, 'A message will be placed into the steps QueueName in order to execute the step. The function associated with the queue must be workflow enabled'),
(2, 'Notification Event', 1, 2, 'An event will be sent to the Notification Event Queue in order to trigger any required notification processing - email/sms/app, etc'),
(3, 'Condition', 1, 3, 'Evaluate conditions in order to determine next path to take - first true condition is the path taken'),
(4, 'Wait for Dependencies', 1, 4, 'Pause processing until one or more named dependencies are met. This could be input from an external system, end user input, etc')
(5, 'Branch', 1, 5, 'Branch down multiple paths. If child path has a condition it will only be executed if it evaluates to true. All child paths with no condition will always be executed. Each path will result in a SUB WorkflowJob been created.')

GO
-- ************************************** PostScript End [workflow].[WorkflowStepType]
-- ************************************** [workflow].[WorkflowCondition]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='workflow' and t.name='WorkflowCondition')
CREATE TABLE [workflow].[WorkflowCondition]
(
 [WorkflowConditionId] int IDENTITY (1, 1) NOT NULL ,
 [TenantId]            int NULL ,
 [Expression]          nvarchar(max) NOT NULL ,
 [FieldIds]            varchar(4000) NULL ,
 [VariableIds]         varchar(4000) NULL ,

 CONSTRAINT [PK_1] PRIMARY KEY CLUSTERED ([WorkflowConditionId] ASC)
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'This is a generic Condition table. 
This can be used for determining if a next step within a workflow should be executed. It can also control if a scheduled job meets conditions to be run.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WorkflowCondition';
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The Workflow Condition Id identifies each unique condition that can be applied to a workflow or a schedule.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WorkflowCondition', @level2type=N'COLUMN', @level2name=N'WorkflowConditionId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'When set indicates a condition belongs to a single tenant.
When empty condition is applicable to any tenant.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WorkflowCondition', @level2type=N'COLUMN', @level2name=N'TenantId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The C# expression that will be evaluated to determine if the condition is true', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WorkflowCondition', @level2type=N'COLUMN', @level2name=N'Expression';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Comma separated list of FieldId''s that are used in the expression.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WorkflowCondition', @level2type=N'COLUMN', @level2name=N'FieldIds';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Comma Separated list of VariableId''s that are used in the expression.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WorkflowCondition', @level2type=N'COLUMN', @level2name=N'VariableIds';
GO
-- ************************************** [workflow].[WfJobStatus]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='workflow' and t.name='WfJobStatus')
CREATE TABLE [workflow].[WfJobStatus]
(
 [WfJobStatusId] tinyint NOT NULL ,
 [Label]         nvarchar(100) NOT NULL ,
 [IsEnabled]     bit NOT NULL CONSTRAINT [DF_WfJobCancelledReason_IsEnabled] DEFAULT 1 ,
 [SortOrder]     tinyint NOT NULL ,

 CONSTRAINT [PK_1] PRIMARY KEY CLUSTERED ([WfJobStatusId] ASC)
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'1 - Scheduled,
2 - Delayed
3 - Check Conditions
4 - Executing
5 - Waiting
6 - Completed
7 - Cancelled
8 - Error', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJobStatus';
GO

-- ************************************** PostScript

INSERT INTO [worflow].[WfJobStatus]
([WfJobStatusId], [Label], [IsEnabled], [SortOrder])
VALUES
(1, 'Scheduled', 1, 1),
(2, 'Delayed', 1, 2),
(3, 'Check Conditions', 1, 3),
(4, 'Executing', 1, 4),
(5, 'Waiting', 1, 5),
(6, 'Completed', 1, 6),
(7, 'Cancelled', 1, 7),
(8, 'Error', 1, 8)

GO
-- ************************************** PostScript End [workflow].[WfJobStatus]
-- ************************************** [workflow].[WfJobCancelledReason]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='workflow' and t.name='WfJobCancelledReason')
CREATE TABLE [workflow].[WfJobCancelledReason]
(
 [WfJobCancelledReasonId] tinyint NOT NULL ,
 [Label]                  nvarchar(100) NOT NULL ,
 [IsEnabled]              bit NOT NULL CONSTRAINT [DF_WfJobCancelledReason_IsEnabled] DEFAULT 1 ,
 [SortOrder]              tinyint NOT NULL ,

 CONSTRAINT [PK_1] PRIMARY KEY CLUSTERED ([WfJobCancelledReasonId] ASC)
);
GO
-- ************************************** [workflow].[ScheduleType]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='workflow' and t.name='ScheduleType')
CREATE TABLE [workflow].[ScheduleType]
(
 [ScheduleTypeId] smallint NOT NULL ,
 [Label]          nvarchar(100) NOT NULL ,
 [IsEnabled]      bit NOT NULL CONSTRAINT [DF_ScheduleType_IsEnabled] DEFAULT 1 ,

 CONSTRAINT [PK_1] PRIMARY KEY CLUSTERED ([ScheduleTypeId] ASC)
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Determines if a Schedule is of type Simple or Workflow', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'ScheduleType';
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Determines if a Schedule is of type 1 - Simple or 2 - Workflow', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'ScheduleType', @level2type=N'COLUMN', @level2name=N'ScheduleTypeId';
GO

-- ************************************** PostScript

INSERT INTO [workflow].[ScheduleType]
(ScheduleTypeId, [Label], [IsEnabled])
VALUES
(1, 'Simple', 1),
(2, 'Workflow', 1)

GO
-- ************************************** PostScript End [workflow].[ScheduleType]
-- ************************************** [workflow].[SchedulePurpose]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='workflow' and t.name='SchedulePurpose')
CREATE TABLE [workflow].[SchedulePurpose]
(
 [SchedulePurposeId] smallint NOT NULL ,
 [Label]             nvarchar(100) NOT NULL ,
 [IsEnabled]         bit NOT NULL ,

 CONSTRAINT [PK_1] PRIMARY KEY CLUSTERED ([SchedulePurposeId] ASC)
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Schedule purpose allows us to clearly identify schedules for different purposes.
For example a schedule may be a reporting group that is used to pull together many reports into a single report delivery', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'SchedulePurpose';
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Schedule purpose allows us to clearly identify schedules for different purposes.
For example a schedule may be a reporting group that is used to pull together many reports into a single report delivery', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'SchedulePurpose', @level2type=N'COLUMN', @level2name=N'SchedulePurposeId';
GO
-- ************************************** [workflow].[ScheduleMode]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='workflow' and t.name='ScheduleMode')
CREATE TABLE [workflow].[ScheduleMode]
(
 [ScheduleModeId] smallint NOT NULL ,
 [Label]          nvarchar(100) NOT NULL ,
 [IsEnabled]      bit NOT NULL CONSTRAINT [DF_ScheduleMode_IsEnabled] DEFAULT 1 ,

 CONSTRAINT [PK_1] PRIMARY KEY CLUSTERED ([ScheduleModeId] ASC)
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Determines the mode in which a schedule operates: 1 - once, 2 - recurring, 3 - on demand', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'ScheduleMode';
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Determines the mode in which a schedule operates: 1 - once, 2 - recurring, 3 - on demand', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'ScheduleMode', @level2type=N'COLUMN', @level2name=N'ScheduleModeId';
GO

-- ************************************** PostScript

INSERT INTO [workflow].[ScheduleMode]
(ScheduleModeId, [Label], [IsEnabled])
VALUES
(1, 'Once Only', 1),
(2, 'Recurring', 1),
(3, 'On Demand', 1)

GO
-- ************************************** PostScript End [workflow].[ScheduleMode]
-- ************************************** [workflow].[ReportType]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='workflow' and t.name='ReportType')
CREATE TABLE [workflow].[ReportType]
(
 [ReportTypeId] smallint NOT NULL ,
 [Label]        nvarchar(100) NOT NULL ,
 [IsEnabled]    bit NOT NULL CONSTRAINT [DF_ReportType_IsEnabled] DEFAULT 1 ,

 CONSTRAINT [PK_1] PRIMARY KEY CLUSTERED ([ReportTypeId] ASC)
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Identifies the type of report.
1 - Report
2 - Alert', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'ReportType';
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The ReportTypeId. 1 - Report, 2 - Alert', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'ReportType', @level2type=N'COLUMN', @level2name=N'ReportTypeId';
GO

-- ************************************** PostScript

INSERT INTO [workflow].[ReportType]
(ReportTypeId, [Label], [IsEnabled])
VALUES
(1, 'Report', 1),
(2, 'Alert', 2)

GO
-- ************************************** PostScript End [workflow].[ReportType]
-- ************************************** [workflow].[RecurrenceFrequency]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='workflow' and t.name='RecurrenceFrequency')
CREATE TABLE [workflow].[RecurrenceFrequency]
(
 [RecurrenceFrequencyId] smallint NOT NULL ,
 [Label]                 nvarchar(100) NULL ,
 [IsEnabled]             bit NOT NULL CONSTRAINT [DF_RecurrenceFrequency_IsEnabled] DEFAULT 1 ,
 [SortOrder]             smallint NOT NULL ,

 CONSTRAINT [PK_1] PRIMARY KEY CLUSTERED ([RecurrenceFrequencyId] ASC)
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The frequency something repeats or recurs at.
1 - Daily,
2 - Weekly,
3 - Monthly
4 - Yearly
5 - Every X Hours
6 - Every X Minutes', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'RecurrenceFrequency';
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The frequency something repeats or recurs at.
1 - Daily,
2 - Weekly,
3 - Monthly
4 - Yearly
5 - Every X Hours
6 - Every X Minutes', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'RecurrenceFrequency', @level2type=N'COLUMN', @level2name=N'RecurrenceFrequencyId';
GO

-- ************************************** PostScript

INSERT INTO [workflow].[RecurranceFrequency]
([RecurranceFrequencyId], [Label], [SortOrder], [IsEnabled])
VALUES
(1, 'Daily', 1, 1),
(2, 'Weekly', 2, 1),
(3, 'Monthly', 3, 1),
(4, 'Yearly', 4, 1),
(5, 'Every X Hours', 5, 1),
(6, 'Every X Minutes', 6, 1)

GO
-- ************************************** PostScript End [workflow].[RecurrenceFrequency]
-- ************************************** [workflow].[RecurOnPosition]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='workflow' and t.name='RecurOnPosition')
CREATE TABLE [workflow].[RecurOnPosition]
(
 [RecurOnPositionId] tinyint NOT NULL ,
 [Label]             nvarchar(60) NOT NULL ,
 [SortOrder]         tinyint NULL ,
 [IsEnabled]         bit NOT NULL CONSTRAINT [DF_BookingRecurOnPosition_IsEnabled] DEFAULT 1 ,

 CONSTRAINT [PK_BookingRecurOnPosition] PRIMARY KEY CLUSTERED ([RecurOnPositionId] ASC)
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'When a schedule needs to occur on the 1st, 2nd, etc Tuesday (weekday) of a month.
1 - First, 2 - Second, 3 - Third, 4 - Fourth, 9 - Last', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'RecurOnPosition';
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'When a schedule needs to occur on the 1st, 2nd, etc Tuesday (weekday) of a month.
1 - First, 2 - Second, 3 - Third, 4 - Fourth, 9 - Last', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'RecurOnPosition', @level2type=N'COLUMN', @level2name=N'RecurOnPositionId';
GO

-- ************************************** PostScript

INSERT INTO [workflow].[RecurOnPosition]
([RecurOnPositionId], [Label], [SortOrder], [IsEnabled])
VALUES
(1, 'First', 1, 1),
(2, 'Second', 2, 1),
(3, 'Third', 3, 1),
(4, 'Fourth', 4, 1),
(9, 'Last', 5, 1),
(10, 'Second Last', 6, 1),
(11, 'Third Last', 7, 1)

GO
-- ************************************** PostScript End [workflow].[RecurOnPosition]
-- ************************************** [workflow].[RecurOn]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='workflow' and t.name='RecurOn')
CREATE TABLE [workflow].[RecurOn]
(
 [RecurOnId] tinyint NOT NULL ,
 [Label]     nvarchar(60) NOT NULL ,
 [SortOrder] tinyint NULL ,
 [IsEnabled] bit NOT NULL CONSTRAINT [DF_BookingRecurOn_IsEnabled] DEFAULT 1 ,

 CONSTRAINT [PK_BookingRecurOn] PRIMARY KEY CLUSTERED ([RecurOnId] ASC)
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'When a schedule will recur on.
1 - Day of Month, 2 -  Week Day, 3 - Weekend Day, 11 - Monday, 12 - Tuesday, 13 - Wednesday, 14 - Thursday, 15 - Friday, 16 - Saturday, 17 - Sunday.

Requires RecurOnPosition to also be set', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'RecurOn';
GO

-- ************************************** PostScript

INSERT INTO [workflow].[RecurOn]
([RecurOnId], [Label], [SortOrder], [IsEnabled])
VALUES
(1, 'Day of month', 1, 1),
(2, 'Week day', 2, 1),
(3, 'Weekend day', 3, 1),
(11, 'Monday', 11, 1),
(12, 'Tuesday', 12, 1),
(13, 'Wednesday', 13, 1),
(14, 'Thursday', 14, 1),
(15, 'Friday', 15, 1),
(16, 'Saturday', 16, 1),
(17, 'Sunday', 17, 1)

GO
-- ************************************** PostScript End [workflow].[RecurOn]
-- ************************************** [workflow].[DependencyComplete]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='workflow' and t.name='DependencyComplete')
CREATE TABLE [workflow].[DependencyComplete]
(
 [DependencyCompleteId] bigint IDENTITY (1, 1) NOT NULL ,
 [DependencyKey]        varchar(100) NOT NULL ,
 [DependencyDate]       datetimeoffset(7) NULL ,
 [CreatedOn]            datetimeoffset(7) NOT NULL ,

 CONSTRAINT [PK_DependencyComplete] PRIMARY KEY NONCLUSTERED ([DependencyCompleteId] ASC)
);
GO

CREATE NONCLUSTERED INDEX [IX_DependencyComplete_KeyDate] ON [workflow].[DependencyComplete] 
 (
  [DependencyKey] ASC, 
  [DependencyDate] ASC
 )

GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Every time something that is a potential dependancy to workflow is completed a row is inserted in here.
Scheduled jobs can then check if a dependancy is met before they are run or continued with.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'DependencyComplete';
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The dependency key is used to uniquely identify a dependency. Format:
T{TenantId}{Tech}{Item}

TenantId - only included for Tenant Specific items. Otherwise 0
Tech - identifies the applicable technology that is a dependency. eg. adx, sql, api, file
Item - Clearly identifies the item such as a table name.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'DependencyComplete', @level2type=N'COLUMN', @level2name=N'DependencyKey';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The Date the dependency is relative to', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'DependencyComplete', @level2type=N'COLUMN', @level2name=N'DependencyDate';
GO
-- ************************************** [workflow].[Attribute]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='workflow' and t.name='Attribute')
CREATE TABLE [workflow].[Attribute]
(
 [AttributeCode]          varchar(80) NOT NULL ,
 [Label]                  nvarchar(100) NOT NULL ,
 [AttributeValueTypeCode] varchar(40) NOT NULL ,

 CONSTRAINT [PK_1] PRIMARY KEY CLUSTERED ([AttributeCode] ASC),
 CONSTRAINT [FK_1] FOREIGN KEY ([AttributeValueTypeCode])  REFERENCES [AttributeValueType]([AttributeValueTypeCode])
);
GO
-- ************************************** [workflow].[Workflow]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='workflow' and t.name='Workflow')
CREATE TABLE [workflow].[Workflow]
(
 [WorkflowId]          int IDENTITY (1, 1) NOT NULL ,
 [Name]                nvarchar(500) NOT NULL ,
 [LastExecutedOn]      datetimeoffset(7) NULL ,
 [IsEnabled]           bit NOT NULL CONSTRAINT [DF_Workflow_IsEnabled] DEFAULT 1 ,
 [FirstWorkflowStepId] int NULL ,
 [TenantId]            int NULL ,
 [CreatedOn]           datetimeoffset(7) NOT NULL ,
 [CreatedByName]       nvarchar(60) NULL ,
 [ModifiedOn]          datetimeoffset(7) NULL ,
 [ModifiedByName]      nvarchar(60) NULL ,
 [Deleted]             bit NOT NULL CONSTRAINT [DF_Workflow_Deleted] DEFAULT 0 ,

 CONSTRAINT [PK_1] PRIMARY KEY CLUSTERED ([WorkflowId] ASC),
 CONSTRAINT [FK_1] FOREIGN KEY ([FirstWorkflowStepId])  REFERENCES [workflow].[WorkflowStep]([WorkflowStepId])
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Defines a Workflow. Will contain one or more steps that are required to execute the workflow.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Workflow';
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The workFlow Id. Uniquely identifies a workflow (a series of steps).', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Workflow', @level2type=N'COLUMN', @level2name=N'WorkflowId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The first step in a Workflow.
A workflow cannot be run if this is not set.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Workflow', @level2type=N'COLUMN', @level2name=N'FirstWorkflowStepId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'When set indicates a workflow belongs to a single tenant.
When empty workflow is applicable to any tenant.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Workflow', @level2type=N'COLUMN', @level2name=N'TenantId';
GO
-- ************************************** [workflow].[WorkflowStepConnector]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='workflow' and t.name='WorkflowStepConnector')
CREATE TABLE [workflow].[WorkflowStepConnector]
(
 [WorkflowStepConnectorId] int IDENTITY (1, 1) NOT NULL ,
 [ParentWorkflowStepId]    int NOT NULL ,
 [ChildWorkflowStepId]     int NOT NULL ,
 [WorkflowId]              int NOT NULL ,
 [SortOrder]               int NULL ,
 [WorkflowConditionId]     int NULL ,

 CONSTRAINT [PK_1] PRIMARY KEY CLUSTERED ([WorkflowStepConnectorId] ASC),
 CONSTRAINT [FK_1] FOREIGN KEY ([WorkflowId])  REFERENCES [workflow].[Workflow]([WorkflowId]),
 CONSTRAINT [FK_2] FOREIGN KEY ([ParentWorkflowStepId])  REFERENCES [workflow].[WorkflowStep]([WorkflowStepId]),
 CONSTRAINT [FK_3] FOREIGN KEY ([ChildWorkflowStepId])  REFERENCES [workflow].[WorkflowStep]([WorkflowStepId]),
 CONSTRAINT [FK_4] FOREIGN KEY ([WorkflowConditionId])  REFERENCES [workflow].[WorkflowCondition]([WorkflowConditionId])
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Connect steps together in a Complex Workflow that has 1 or more conditional paths.
A parent step may have 1 or more child steps with conditions that control which path is taken.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WorkflowStepConnector';
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The Workflow Step Connector Id to uniquely identify a path from a parent step to the next step. This may be conditional.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WorkflowStepConnector', @level2type=N'COLUMN', @level2name=N'WorkflowStepConnectorId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Id that identifies a step within a workflow.
The parent step in this relationship (parent is the From Step)', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WorkflowStepConnector', @level2type=N'COLUMN', @level2name=N'ParentWorkflowStepId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Id that identifies a step within a workflow.
The child step. Child is the To step.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WorkflowStepConnector', @level2type=N'COLUMN', @level2name=N'ChildWorkflowStepId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The workFlow Id. Uniquely identifies a workflow (a series of steps).', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WorkflowStepConnector', @level2type=N'COLUMN', @level2name=N'WorkflowId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'For a Parent with Multiple Conditional Child steps the sort order controls which path is taken. The first condition that evaluates to true will have its path taken (child).
Note! a Branch parent step will take every path that is true (not just the first).', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WorkflowStepConnector', @level2type=N'COLUMN', @level2name=N'SortOrder';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The Workflow Condition Id identifies each unique condition that can be applied to a workflow.
For this Step Connector the path to the Child will only be taken if the condition evaluates to true (or there is no condition)', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WorkflowStepConnector', @level2type=N'COLUMN', @level2name=N'WorkflowConditionId';
GO
-- ************************************** [workflow].[WorkflowStep]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='workflow' and t.name='WorkflowStep')
CREATE TABLE [workflow].[WorkflowStep]
(
 [WorkflowStepId]            int IDENTITY (1, 1) NOT NULL ,
 [WorkflowId]                int NOT NULL ,
 [WorkflowStepTypeId]        smallint NOT NULL ,
 [SortOrder]                 smallint NULL ,
 [Name]                      nvarchar(500) NOT NULL ,
 [QueueName]                 varchar(63) NULL ,
 [NotificationEventTypeCode] varchar(60) NULL ,

 CONSTRAINT [PK_1] PRIMARY KEY CLUSTERED ([WorkflowStepId] ASC),
 CONSTRAINT [FK_1] FOREIGN KEY ([WorkflowId])  REFERENCES [workflow].[Workflow]([WorkflowId]),
 CONSTRAINT [FK_2] FOREIGN KEY ([WorkflowStepTypeId])  REFERENCES [workflow].[WorkflowStepType]([WorkflowStepTypeId])
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Id that identifies a step within a workflow.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WorkflowStep', @level2type=N'COLUMN', @level2name=N'WorkflowStepId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The workFlow Id. Uniquely identifies a workflow (a series of steps).', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WorkflowStep', @level2type=N'COLUMN', @level2name=N'WorkflowId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Defines the type of each step (how each step behaves):
1 - Execute Via Queue
2 - Notification Event
3 - Condition
4 - Wait for Dependencies', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WorkflowStep', @level2type=N'COLUMN', @level2name=N'WorkflowStepTypeId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'For simple sequential workflows this controls the order steps are executed sequentially in.
Steps are process in ascending order.
For complex workflows with conditional paths the WorkflowStepConnector table controls the order steps are executed in,', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WorkflowStep', @level2type=N'COLUMN', @level2name=N'SortOrder';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The Azure Storage Queue Name. In order to execute the step a message will be placed in this queue.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WorkflowStep', @level2type=N'COLUMN', @level2name=N'QueueName';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'To pass a message off to the Notification Engine this Event Type Code must be set.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WorkflowStep', @level2type=N'COLUMN', @level2name=N'NotificationEventTypeCode';
GO
-- ************************************** [workflow].[Schedule]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='workflow' and t.name='Schedule')
CREATE TABLE [workflow].[Schedule]
(
 [ScheduleId]                int IDENTITY (1, 1) NOT NULL ,
 [Name]                      nvarchar(200) NULL ,
 [WorkflowId]                int NULL ,
 [ScheduleTypeId]            smallint NOT NULL CONSTRAINT [DF_Schedule_ScheduleTypeId] DEFAULT 1 ,
 [WorkflowConditionId]       int NULL ,
 [ScheduleModeId]            smallint NOT NULL CONSTRAINT [DF_Schedule_ScheduleModeId] DEFAULT 2 ,
 [SchedulePurposeId]         smallint NULL ,
 [TenantId]                  int NULL ,
 [SimpleChainedQueueNames]   varchar(4000) NULL ,
 [ParentEntityId]            uniqueidentifier NULL ,
 [ParentEntityIntId]         bigint NULL ,
 [ParentEntityType]          varchar(100) NULL ,
 [ScheduledNextRunAt]        datetimeoffset(7) NULL ,
 [IsEnabled]                 bit NOT NULL CONSTRAINT [DF_Schedule_IsEnabled] DEFAULT 1 ,
 [Note]                      nvarchar(3000) NULL ,
 [ScheduleStartsOn]          datetimeoffset(7) NULL ,
 [ScheduleEndsOn]            datetimeoffset(7) NULL ,
 [TimezoneIanaId]            varchar(40) NULL ,
 [FromTime]                  time NULL ,
 [ToTime]                    time NULL ,
 [ScheduledWhenDescription]  nvarchar(400) NULL ,
 [RecurrenceFrequencyId]     smallint NULL ,
 [RecurOnId]                 tinyint NULL ,
 [RecurOnPositionId]         tinyint NULL ,
 [RecurEveryX]               smallint NULL ,
 [RecurOnDayOfMonth]         tinyint NULL ,
 [RecurOnMonth]              tinyint NULL ,
 [IncludeMonday]             bit NOT NULL CONSTRAINT [DF_Schedule_IncludeMonday] DEFAULT 0 ,
 [IncludeTuesday]            bit NOT NULL CONSTRAINT [DF_Schedule_IncludeTuesday] DEFAULT 0 ,
 [IncludeWednesday]          bit NOT NULL CONSTRAINT [DF_Schedule_IncludeWednesday] DEFAULT 0 ,
 [IncludeThursday]           bit NOT NULL CONSTRAINT [DF_Schedule_IncludeThursday] DEFAULT 0 ,
 [IncludeFriday]             bit NOT NULL CONSTRAINT [DF_Schedule_IncludeFriday] DEFAULT 0 ,
 [IncludeSaturday]           bit NOT NULL CONSTRAINT [DF_Schedule_IncludeSaturday] DEFAULT 0 ,
 [IncludeSunday]             bit NOT NULL CONSTRAINT [DF_Schedule_IncludeSunday] DEFAULT 0 ,
 [NotificationEventTypeCode] varchar(60) NULL ,
 [CreatedOn]                 datetimeoffset(7) NOT NULL ,
 [CreatedByName]             nvarchar(60) NULL ,
 [ModifiedOn]                datetimeoffset(7) NULL ,
 [ModifiedByName]            nvarchar(60) NULL ,
 [Deleted]                   bit NOT NULL CONSTRAINT [DF_Schedule_Deleted] DEFAULT 0 ,

 CONSTRAINT [PK_1] PRIMARY KEY CLUSTERED ([ScheduleId] ASC),
 CONSTRAINT [FK_1] FOREIGN KEY ([WorkflowId])  REFERENCES [workflow].[Workflow]([WorkflowId]),
 CONSTRAINT [FK_2] FOREIGN KEY ([ScheduleTypeId])  REFERENCES [workflow].[ScheduleType]([ScheduleTypeId]),
 CONSTRAINT [FK_3] FOREIGN KEY ([WorkflowConditionId])  REFERENCES [workflow].[WorkflowCondition]([WorkflowConditionId]),
 CONSTRAINT [FK_4] FOREIGN KEY ([ScheduleModeId])  REFERENCES [workflow].[ScheduleMode]([ScheduleModeId]),
 CONSTRAINT [FK_5] FOREIGN KEY ([RecurrenceFrequencyId])  REFERENCES [workflow].[RecurrenceFrequency]([RecurrenceFrequencyId]),
 CONSTRAINT [FK_6] FOREIGN KEY ([RecurOnPositionId])  REFERENCES [workflow].[RecurOnPosition]([RecurOnPositionId]),
 CONSTRAINT [FK_7] FOREIGN KEY ([RecurOnId])  REFERENCES [workflow].[RecurOn]([RecurOnId]),
 CONSTRAINT [FK_8] FOREIGN KEY ([SchedulePurposeId])  REFERENCES [workflow].[SchedulePurpose]([SchedulePurposeId])
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'This is a generic Schedule table that will support schedule reports, as well as other things like processing, tasks, etc. It is responsible for determining WHEN something should happen.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Schedule';
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Identifies a pre-defined schedule for processing, tasks, reporting, jobs, etc.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Schedule', @level2type=N'COLUMN', @level2name=N'ScheduleId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Optional name for a Schedule', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Schedule', @level2type=N'COLUMN', @level2name=N'Name';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The workFlow Id. Uniquely identifies a workflow (a series of steps).
Optional - a schedule may be defined with no workflow.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Schedule', @level2type=N'COLUMN', @level2name=N'WorkflowId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Determines if a Schedule is of type 
1 - Simple (Chained) or 
2 - Workflow (must have a WorkflowId)', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Schedule', @level2type=N'COLUMN', @level2name=N'ScheduleTypeId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Optional. If set, this is the condition that must be met for the schedule to be run at its scheduled time. If the condition evaluates to false then the schedule will not run (job will be set to Cancelled).', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Schedule', @level2type=N'COLUMN', @level2name=N'WorkflowConditionId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Determines the mode in which a schedule operates: 1 - once, 2 - recurring, 3 - on demand', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Schedule', @level2type=N'COLUMN', @level2name=N'ScheduleModeId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Schedule purpose allows us to clearly identify schedules for different purposes.
For example a schedule may be a reporting group that is used to pull together many reports into a single report delivery', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Schedule', @level2type=N'COLUMN', @level2name=N'SchedulePurposeId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The Tenant this Schedule belongs to.
If empty then the schedule is not Tenant specific', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Schedule', @level2type=N'COLUMN', @level2name=N'TenantId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'A comma separated list of queue names that will be sequentially processed. Only used if Simple schedule type (1).
After each queue step is executed a message will be written to the next queue (effectively chaining together queue functions).', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Schedule', @level2type=N'COLUMN', @level2name=N'SimpleChainedQueueNames';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The next time this schedule must run.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Schedule', @level2type=N'COLUMN', @level2name=N'ScheduledNextRunAt';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Must be true for the schedule to run a new job.
Once a job has started this setting is ignored.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Schedule', @level2type=N'COLUMN', @level2name=N'IsEnabled';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Date and time from when a schedule will first start running.
If empty is valid immediately', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Schedule', @level2type=N'COLUMN', @level2name=N'ScheduleStartsOn';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Date and time when a schedule will end.
If not set, then the schedule is infinite.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Schedule', @level2type=N'COLUMN', @level2name=N'ScheduleEndsOn';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The Time Zone Iana Identifier.
See https://en.wikipedia.org/wiki/List_of_tz_database_time_zones

The timezone FromTime and ToTime relate to.
Or the timezone used for scheduling (based on day of week etc)', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Schedule', @level2type=N'COLUMN', @level2name=N'TimezoneIanaId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'If set specifies the time of day that a schedule can run at.
If ToTime is also set then the pair specify a window of time during which the schedule can occur.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Schedule', @level2type=N'COLUMN', @level2name=N'FromTime';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'If FromTime is also set then the pair specify a window of time during which the schedule can occur.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Schedule', @level2type=N'COLUMN', @level2name=N'ToTime';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'A system generated description of when the schedule is run. 
This can be displayed to users.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Schedule', @level2type=N'COLUMN', @level2name=N'ScheduledWhenDescription';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The frequency something repeats or recurs at.
1 - Daily,
2 - Weekly,
3 - Monthly
4 - Yearly
5 - Every X Hours
6 - Every X Minutes', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Schedule', @level2type=N'COLUMN', @level2name=N'RecurrenceFrequencyId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'When a Schedule will Recur On.
Works with RecurOnPositionId to support 3rd Tuesday of Month, 1st Weekday of Month, Last Friday of Month, Last Day of Month.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Schedule', @level2type=N'COLUMN', @level2name=N'RecurOnId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'When a schedule needs to occur on the 1st, 2nd, etc Tuesday (weekday) of a month.
1 - First, 2 - Second, 3 - Third, 4 - Fourth, 9 - Last

Works with RecurOnId', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Schedule', @level2type=N'COLUMN', @level2name=N'RecurOnPositionId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Identifies the notification event to be optionally triggered by the schedule.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Schedule', @level2type=N'COLUMN', @level2name=N'NotificationEventTypeCode';
GO
-- ************************************** [workflow].[WfJob]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='workflow' and t.name='WfJob')
CREATE TABLE [workflow].[WfJob]
(
 [WfJobId]                 bigint IDENTITY (1, 1) NOT NULL ,
 [Name]                    nvarchar(400) NULL ,
 [ScheduleId]              int NULL ,
 [WorkflowId]              int NULL ,
 [WorkflowConditionId]     int NULL ,
 [ScheduledAt]             datetimeoffset(7) NULL ,
 [IsScheduled]             bit NOT NULL CONSTRAINT [DF_WfJob_IsScheduled] DEFAULT 0 ,
 [WfJobStatusId]           tinyint NOT NULL CONSTRAINT [DF_WfJob_WfJobStatusId] DEFAULT 1 ,
 [SchedulePurposeId]       smallint NOT NULL ,
 [TenantId]                int NULL ,
 [OwningPartyId]           uniqueidentifier NULL ,
 [CompletedAt]             datetimeoffset(7) NULL ,
 [NextCheckDependenciesAt] datetimeoffset(7) NULL ,
 [TimeoutDependencyWaitAt] datetimeoffset(7) NULL ,
 [WorkflowStepId]          int NULL ,
 [WfJobCancelledReasonId]  tinyint NOT NULL ,
 [CreatedOn]               datetimeoffset(7) NOT NULL ,
 [CreatedByName]           nvarchar(60) NULL ,
 [ModifiedOn]              datetimeoffset(7) NULL ,
 [ModifiedByName]          nvarchar(60) NULL ,
 [Deleted]                 bit NOT NULL CONSTRAINT [DF_WfJob_Deleted] DEFAULT 0 ,

 CONSTRAINT [PK_1] PRIMARY KEY CLUSTERED ([WfJobId] ASC),
 CONSTRAINT [FK_1] FOREIGN KEY ([ScheduleId])  REFERENCES [workflow].[Schedule]([ScheduleId]),
 CONSTRAINT [FK_2] FOREIGN KEY ([WorkflowId])  REFERENCES [workflow].[Workflow]([WorkflowId]),
 CONSTRAINT [FK_3] FOREIGN KEY ([WorkflowConditionId])  REFERENCES [workflow].[WorkflowCondition]([WorkflowConditionId]),
 CONSTRAINT [FK_4] FOREIGN KEY ([WorkflowStepId])  REFERENCES [workflow].[WorkflowStep]([WorkflowStepId]),
 CONSTRAINT [FK_5] FOREIGN KEY ([WfJobCancelledReasonId])  REFERENCES [workflow].[WfJobCancelledReason]([WfJobCancelledReasonId]),
 CONSTRAINT [FK_6] FOREIGN KEY ([WfJobStatusId])  REFERENCES [workflow].[WfJobStatus]([WfJobStatusId])
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'This is a generic workflow job table to track each occurrence of a schedule (ie. when it is to run) or manually run job.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJob';
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Identifies a pre-defined schedule for processing, tasks, reporting, jobs, etc.
Optional', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJob', @level2type=N'COLUMN', @level2name=N'ScheduleId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The workFlow Id. Uniquely identifies a workflow (a series of steps).
Optional - a job may not have an associated workflow', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJob', @level2type=N'COLUMN', @level2name=N'WorkflowId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The Workflow Condition Id identifies each unique condition that can be applied to a workflow or a schedule.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJob', @level2type=N'COLUMN', @level2name=N'WorkflowConditionId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'When this was scheduled to run', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJob', @level2type=N'COLUMN', @level2name=N'ScheduledAt';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'1 - Scheduled,
2 - Delayed
3 - Check Conditions
4 - Executing
5 - Waiting
6 - Completed
7 - Cancelled
8 - Error', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJob', @level2type=N'COLUMN', @level2name=N'WfJobStatusId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Schedule purpose allows us to clearly identify schedules for different purposes.
For example a schedule may be a reporting group that is used to pull together many reports into a single report delivery', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJob', @level2type=N'COLUMN', @level2name=N'SchedulePurposeId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The Tenant who the job is related or belongs to', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJob', @level2type=N'COLUMN', @level2name=N'TenantId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Optionally identifies a PartyId that owns this job. Could be an individual, group, team, or organisation.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJob', @level2type=N'COLUMN', @level2name=N'OwningPartyId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The datetime when the job completed', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJob', @level2type=N'COLUMN', @level2name=N'CompletedAt';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'if a schedule has dependancies such as data having arrived then this is the next time to check the dependancy is meet', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJob', @level2type=N'COLUMN', @level2name=N'NextCheckDependenciesAt';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'if waiting on dependancies after this time then auto Cancel job.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJob', @level2type=N'COLUMN', @level2name=N'TimeoutDependencyWaitAt';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Identifies the current step within a workflow.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJob', @level2type=N'COLUMN', @level2name=N'WorkflowStepId';
GO
-- ************************************** [workflow].[ScheduleDependency]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='workflow' and t.name='ScheduleDependency')
CREATE TABLE [workflow].[ScheduleDependency]
(
 [ScheduleDependencyId] int IDENTITY (1, 1) NOT NULL ,
 [ScheduleId]           int NOT NULL ,
 [DependencyKey]        varchar(100) NOT NULL ,
 [DependencyDateRule]   varchar(200) NULL ,
 [IsDateMustMatch]      bit NOT NULL CONSTRAINT [DF_ScheduleDependency_IsDateMustMatch] DEFAULT 0 ,

 CONSTRAINT [PK_1] PRIMARY KEY CLUSTERED ([ScheduleDependencyId] ASC),
 CONSTRAINT [FK_1] FOREIGN KEY ([ScheduleId])  REFERENCES [workflow].[Schedule]([ScheduleId])
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'A row for each dependency a schedule has.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'ScheduleDependency';
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Identifies a pre-defined schedule for processing, tasks, reporting, jobs, etc.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'ScheduleDependency', @level2type=N'COLUMN', @level2name=N'ScheduleId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Identifies how to validate the DependencyDate for a completed Dependency.
{Column}{+/-}{number}{period}

Column: ScheduledAt, Now
Period: d for days; h for hours; m for minutes, mt for months

ScheduledAt-1d
To be true DependencyDate >= ScheduledAt minus 1 day.

ScheduledAt
To be true DependencyDate >= ScheduledAt

ScheduledAt-2mt
To be true DependencyDate >= ScheduledAt minus 2 months.

The required date will be stored in the wfJobDependency.RequiredDependencyDate column.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'ScheduleDependency', @level2type=N'COLUMN', @level2name=N'DependencyDateRule';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Indicates how the RequiredDependencyDate is compare to the Completed DependencyDate.
Default is false and will allow DependencyDate to be greater than or equal to RequiredDependencyDate. 
When true the dates must match.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'ScheduleDependency', @level2type=N'COLUMN', @level2name=N'IsDateMustMatch';
GO
-- ************************************** [workflow].[Report]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='workflow' and t.name='Report')
CREATE TABLE [workflow].[Report]
(
 [ReportId]       int IDENTITY (1, 1) NOT NULL ,
 [Title]          nvarchar(400) NOT NULL ,
 [ReportTypeId]   smallint NOT NULL CONSTRAINT [DF_Report_ReportTypeId] DEFAULT 1 ,
 [IsEnabled]      bit NOT NULL CONSTRAINT [DF_Report_IsEnabled] DEFAULT 1 ,
 [TenantId]       int NULL ,
 [ScheduleId]     int NOT NULL ,
 [CreatedOn]      datetimeoffset(7) NOT NULL ,
 [CreatedByName]  nvarchar(60) NULL ,
 [ModifiedOn]     datetimeoffset(7) NULL ,
 [ModifiedByName] nvarchar(60) NULL ,
 [Deleted]        bit NOT NULL ,

 CONSTRAINT [PK_1] PRIMARY KEY CLUSTERED ([ReportId] ASC),
 CONSTRAINT [FK_1] FOREIGN KEY ([ReportTypeId])  REFERENCES [workflow].[ReportType]([ReportTypeId]),
 CONSTRAINT [FK_2] FOREIGN KEY ([ScheduleId])  REFERENCES [workflow].[Schedule]([ScheduleId])
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'This is a generic Report table. 
This can be used for scheduled reports or on-demand reports, alerts, etc. It is responsible for determining what is reported.
The workflow.Schedule table defines when a report runs, Notification Event/Rules defines any delivery requirements (email, sms) for the report.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Report';
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The title of the report', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Report', @level2type=N'COLUMN', @level2name=N'Title';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The ReportTypeId. 1 - Report, 2 - Alert.
Defaults to 1', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Report', @level2type=N'COLUMN', @level2name=N'ReportTypeId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Identifies a pre-defined schedule for reporting.
The schedule may also  just indicate the report only runs on-demand', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'Report', @level2type=N'COLUMN', @level2name=N'ScheduleId';
GO
-- ************************************** [workflow].[WfJobDependency]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='workflow' and t.name='WfJobDependency')
CREATE TABLE [workflow].[WfJobDependency]
(
 [WfJobDependencyId]      bigint IDENTITY (1, 1) NOT NULL ,
 [WfJobId]                bigint NOT NULL ,
 [DependencyKey]          varchar(100) NOT NULL ,
 [RequiredDependencyDate] datetimeoffset(7) NULL ,

 CONSTRAINT [PK_WfJobDependency] PRIMARY KEY NONCLUSTERED ([WfJobDependencyId] ASC),
 CONSTRAINT [FK_WfJobDependency_WfJobId] FOREIGN KEY ([WfJobId])  REFERENCES [workflow].[WfJob]([WfJobId])
);
GO

CREATE NONCLUSTERED INDEX [IX_WfJobDependency_KeyDate] ON [workflow].[WfJobDependency] 
 (
  [DependencyKey] ASC, 
  [RequiredDependencyDate] ASC
 )
 INCLUDE (
  [WfJobId]
 )

GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'A row will exist for each pending dependency for a job. Rows are removed as the dependencies are met (this is to ensure the table remains relatively small).
A dependency is met when the DependencyKey matches and the RequiredDependencyDate is <= Completed DependencyDate', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJobDependency';
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The job the dependency if related to.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJobDependency', @level2type=N'COLUMN', @level2name=N'WfJobId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The dependency key is used to uniquely identify a dependency. Format:
T{TenantId}{Tech}{Item}

TenantId - only included for Tenant Specific items. Otherwise 0
Tech - identifies the applicable technology that is a dependency. eg. adx, sql, api, file
Item - Clearly identifies the item such as a table name.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJobDependency', @level2type=N'COLUMN', @level2name=N'DependencyKey';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'When the row is created the DependencyDate will be set to the required datetime for matching completed dependencies.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJobDependency', @level2type=N'COLUMN', @level2name=N'RequiredDependencyDate';
GO
-- ************************************** [workflow].[WfJobData]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='workflow' and t.name='WfJobData')
CREATE TABLE [workflow].[WfJobData]
(
 [WfJobId]        bigint NOT NULL ,
 [Data]           nvarchar(max) NULL ,
 [CreatedOn]      datetimeoffset(7) NOT NULL ,
 [CreatedByName]  nvarchar(60) NULL ,
 [ModifiedOn]     datetimeoffset(7) NULL ,
 [ModifiedByName] nvarchar(60) NULL ,

 CONSTRAINT [PK_1] PRIMARY KEY CLUSTERED ([WfJobId] ASC),
 CONSTRAINT [FK_1] FOREIGN KEY ([WfJobId])  REFERENCES [workflow].[WfJob]([WfJobId])
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Data field contains a Dictionary of data for a job. This will be updated as a job progresses.
Max one record for a job. Not all jobs will have a record.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJobData';
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The WfJob this Data record is for.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJobData', @level2type=N'COLUMN', @level2name=N'WfJobId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Dictionary style storage of data', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJobData', @level2type=N'COLUMN', @level2name=N'Data';
GO
-- ************************************** [workflow].[WfJobAudit]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='workflow' and t.name='WfJobAudit')
CREATE TABLE [workflow].[WfJobAudit]
(
 [WfJobAuditId]   bigint IDENTITY (1, 1) NOT NULL ,
 [WfJobId]        bigint NOT NULL ,
 [WorkflowStepId] int NULL ,
 [QueueName]      varchar(64) NULL ,
 [TenantId]       int NULL ,
 [QueuedTime]     datetimeoffset(7) NULL ,
 [StartTime]      datetimeoffset(7) NULL ,
 [EndTime]        datetimeoffset(7) NULL ,
 [Information]    nvarchar(max) NULL ,

 CONSTRAINT [PK_1] PRIMARY KEY CLUSTERED ([WfJobAuditId] ASC),
 CONSTRAINT [FK_1] FOREIGN KEY ([WfJobId])  REFERENCES [workflow].[WfJob]([WfJobId]),
 CONSTRAINT [FK_2] FOREIGN KEY ([WorkflowStepId])  REFERENCES [workflow].[WorkflowStep]([WorkflowStepId])
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The WfJobAudit tracks each step that is executed for a job. The StartTime and EndTime can be used to determine how long a step took to execute. The QueueedTime can be used to determine how long it was queued waiting processing.
The Information field is user readable text about the step', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJobAudit';
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The WfJobId this Audit record is for.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJobAudit', @level2type=N'COLUMN', @level2name=N'WfJobId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Id that identifies a step within a workflow.
Optional - only populated if job is related to a workflow with steps', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJobAudit', @level2type=N'COLUMN', @level2name=N'WorkflowStepId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The QueueName associated with a WfJob step that has no associated workFLow (is just a series of chained queue''s).', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJobAudit', @level2type=N'COLUMN', @level2name=N'QueueName';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Optional Tenant associated with the WfJob.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJobAudit', @level2type=N'COLUMN', @level2name=N'TenantId';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The time when this job step was queued for processing.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJobAudit', @level2type=N'COLUMN', @level2name=N'QueuedTime';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'When a job step started executing. If the step has not stated executing then this will be null.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJobAudit', @level2type=N'COLUMN', @level2name=N'StartTime';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'When a job step ended. If the job step has not completed then this will be null.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJobAudit', @level2type=N'COLUMN', @level2name=N'EndTime';
GO
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Human readable text about the step. This can be anything that may be useful for a user to read.', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'WfJobAudit', @level2type=N'COLUMN', @level2name=N'Information';
GO
-- ************************************** [workflow].[ReportAttribute]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='workflow' and t.name='ReportAttribute')
CREATE TABLE [workflow].[ReportAttribute]
(
 [ReportAttributeId] int IDENTITY (1, 1) NOT NULL ,
 [ReportId]          int NOT NULL ,
 [AttributeCode]     varchar(80) NOT NULL ,
 [ValueString]       nvarchar(500) NULL ,
 [ValueStringMax]    nvarchar(max) NULL ,
 [ValueNumeric]      decimal(18,7) NULL ,
 [ValueDateTime]     datetimeoffset(7) NULL ,

 CONSTRAINT [PK_1] PRIMARY KEY CLUSTERED ([ReportAttributeId] ASC),
 CONSTRAINT [FK_1] FOREIGN KEY ([ReportId])  REFERENCES [workflow].[Report]([ReportId]),
 CONSTRAINT [FK_2] FOREIGN KEY ([AttributeCode])  REFERENCES [workflow].[Attribute]([AttributeCode])
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Supports having different attributes against a report', @level0type = N'SCHEMA', @level0name = N'workflow', @level1type = N'TABLE', @level1name = N'ReportAttribute';
GO
