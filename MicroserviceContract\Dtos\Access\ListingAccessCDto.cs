﻿using Redi.Prime3.MicroService.BaseLib;
using Sql;

namespace MicroserviceContract.Dtos.Listing
{
    [Mappable(nameof(ListingAccessId))]
    public class BaseListingAccessCDto : DtoBase
    {
        public int ListingAccessId { get; set; }
        public long ListingId { get; set; }
        public Guid PartyId { get; set; }
        public int ListingAccessRoleId { get; set; }
        public int TenantId { get; set; }
        public string DisplayName { get; set; }
        public string? PartyType { get; set; }
    }

    public class GetListingAccessCDto : BaseListingAccessCDto { }

    [Mappable(nameof(ListingAccessId))]
    public class PartyListingAccessCDto
    {
        public int ListingAccessId { get; set; }
        public long ListingId { get; set; }
        public Guid PartyId { get; set; }
        public string DisplayName { get; set; }
        public int ListingAccessRoleId { get; set; }
        public string? PartyType { get; set; }
    }

}
