﻿using MicroserviceBackendListing.BusinessLogic.Base;

using Redi.Prime3.MicroService.BaseLib;
using MicroserviceDashboardContract.Dtos;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using Sql;
using System.Collections.Specialized;

namespace MicroserviceBackendListing.BusinessLogic
{
    /// <summary>
    /// This class supports fetching, updating and creating Dashboard Presentation
    /// </summary>
    public class DashboardPresentation : BusinessLogicBase
    {
        private UtilityFunctions _utils;
        private Func<Listing> _listingFactory;
        public DashboardPresentation(IUnitOfWork u, IMemoryCache memoryCache, UtilityFunctions utils, Func<Listing> listingFactory)
        {
            _unitOfWork = u;
            _utils = utils;
            _memoryCache = memoryCache;
            _listingFactory = listingFactory;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="standardListParameters"></param>
        /// <param name="name"></param>
        /// <returns></returns>
        internal async Task<ListResponseDto<DashboardPresentationCDto>> GetListAsync(StandardListParameters standardListParameters, string? name = null)
        {
            if (standardListParameters == null)
            {
                standardListParameters = new StandardListParameters();
            }

            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "SortOrder", "[DashboardPresentation].[SortOrder]" },
                { "Name", "[DashboardPresentation].[Name]" },
                { "CreatedOn", "[DashboardPresentation].[CreatedOn]" }
            };

            string sql = @"
            SELECT 
                        [DashboardPresentation].[DashboardPresentationId], 
                        [DashboardPresentation].[Name], 
                        [DashboardPresentation].[ConfigJson],
                        [DashboardPresentation].[PartyId],
                        [DashboardPresentation].[DashboardCount],
                        [DashboardPresentation].[SortOrder],
                        [DashboardPresentation].[CreatedOn],
                        [DashboardPresentation].[CreatedByName],
                        [DashboardPresentation].[ModifiedOn], 
                        [DashboardPresentation].[ModifiedByName],
                        [DashboardPresentation].[Deleted]
            FROM        [dashboard].[DashboardPresentation] [DashboardPresentation]";

            string whereCondition = @" WHERE [DashboardPresentation].[TenantId] = @tenantId 
AND [DashboardPresentation].[Deleted] = 0 
AND ([DashboardPresentation].[PartyId] IS NULL OR [DashboardPresentation].[PartyId] = @partyId)
";

            if (!string.IsNullOrEmpty(name))
            {
                whereCondition += " AND [DashboardPresentation].[Name] = @name";
            }
            sql += whereCondition;
            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "SortOrder, CreatedOn");
            sql += $" OFFSET @Offset ROWS";
            sql += $" FETCH NEXT @Limit ROWS ONLY";

            var response = new ListResponseDto<DashboardPresentationCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("name", name);
                command.AddArgument("partyId", _utils.PartyId);
                command.AddArgument("tenantId", _utils.TenantId);
                command.AddArgument("Offset", standardListParameters.Offset);
                command.AddArgument("Limit", standardListParameters.Limit);

                response.List = await command.SelectMany<DashboardPresentationCDto>();
            }

            sql = @"SELECT COUNT(*) AS totalNumOfRows
                    FROM [dashboard].[DashboardPresentation] ";
            sql += whereCondition;

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("name", name);
                command.AddArgument("partyId", _utils.PartyId);
                command.AddArgument("tenantId", _utils.TenantId);
                response.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return response;
        }

        internal async Task<DashboardPresentationWithConfigCDto> GetAsync(int dashboardPresentationId)
        {
            string sql = @"
                        SELECT 
                        [DashboardPresentation].[DashboardPresentationId], 
                        [DashboardPresentation].[Name], 
                        [DashboardPresentation].[ConfigJson],
                        [DashboardPresentation].[PartyId],
                        [DashboardPresentation].[DashboardCount],
                        [DashboardPresentation].[SortOrder],
                        [DashboardPresentation].[CreatedOn],
                        [DashboardPresentation].[CreatedByName],
                        [DashboardPresentation].[ModifiedOn], 
                        [DashboardPresentation].[ModifiedByName],
                        [DashboardPresentation].[Deleted]
                        FROM [dashboard].[DashboardPresentation] [DashboardPresentation]
                        WHERE [DashboardPresentation].[DashboardPresentationId] = @dashboardPresentationId 
AND [DashboardPresentation].[Deleted] = 0 
AND ([DashboardPresentation].[PartyId] IS NULL OR [DashboardPresentation].[PartyId] = @partyId)
";
            var result = new DashboardPresentationWithConfigCDto();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("dashboardPresentationId", dashboardPresentationId);
                command.AddArgument("partyId", _utils.PartyId);
                result = await command.SelectSingle<DashboardPresentationWithConfigCDto>();
            }
            if (string.IsNullOrEmpty(result.ConfigJson))
            {
                throw new HttpRequestException($"Dashboard Presentation 'ConfigJson' do not exist for '{dashboardPresentationId}'", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }
            var config = JsonConvert.DeserializeObject<DashboardPresentationConfigCDto>(result.ConfigJson);
            if (config == null)
            {
                throw new HttpRequestException($"Dashboard Presentation 'Config' do not exist for '{dashboardPresentationId}'", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }
            var listing = _listingFactory();
            result.Config = new DashboardPresentationConfigCDto()
            {
                Dashboards = new List<DashboardPresentationListItemConfigDto>()
            };

            //Exclude dashboards that are deleted
            if (config.Dashboards != null)
            {
                foreach (var dashboard in config.Dashboards)
                {
                    //Check if the dashboard is deleted
                    var item = await listing.GetSingleListing(dashboard.ListingId, false, excludeDeleted: true);
                    if (item != null)
                    {
                        result.Config.Dashboards.Add(dashboard);
                    }
                }
            }
            return result;
        }

        internal async Task<int> CreateAsync(DashboardPresentationCDto dto)
        {
            if (string.IsNullOrEmpty(dto.Name))
            {
                throw new ArgumentException($"Dashboard Presentation Name does not exist.");
            }
            if (string.IsNullOrEmpty(dto.ConfigJson))
            {
                throw new ArgumentException($"Dashboard Presentation Config does not exist.");
            }

            if (dto.DashboardPresentationId > 0)
            {
                var exists = await GetAsync(dto.DashboardPresentationId);
                if (exists == null)
                {
                    await UpdateAsync(dto);
                }
            }

            dto.PartyId = _utils.PartyId;
            dto.CreatedOn = DateTimeOffset.UtcNow;
            dto.CreatedByName = _utils.UserFullName;

            string sql = @"
                INSERT INTO [dashboard].[DashboardPresentation]
                (
                    [Name],
                    [PartyId],
                    [TenantId],
                    [DashboardCount],
                    [ConfigJson],
                    [SortOrder],
                    [CreatedOn],
                    [CreatedByName],
                    [Deleted]
                )
                VALUES
                (
                    @Name,
                    @PartyId,
                    @TenantId,
                    @DashboardCount,
                    @ConfigJson,
                    @SortOrder,
                    @CreatedOn,
                    @CreatedByName,
                    0
                )
                ";
            int dashboardPresentationId = 0;
            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArguments(dto);
                command.AddArgument("TenantId", _utils.TenantId);
                command.AddArgument("SortOrder", 1);
                dashboardPresentationId = await command.ExecuteAndReturnIdentity();
            }
            return dashboardPresentationId;
        }

        internal async Task UpdateAsync(DashboardPresentationCDto dto)
        {
            if (string.IsNullOrEmpty(dto.Name))
            {
                throw new ArgumentException($"Dashboard Presentation Name does not exist.");
            }
            if (string.IsNullOrEmpty(dto.ConfigJson))
            {
                throw new ArgumentException($"Dashboard Presentation Config does not exist.");
            }
            if (dto.DashboardPresentationId == 0)
            {
                await CreateAsync(dto);
            }
            else
            {
                dto.PartyId = _utils.PartyId;
                dto.ModifiedOn = DateTimeOffset.UtcNow;
                dto.ModifiedByName = _utils.UserFullName;

                string sql = @"
            UPDATE   [dashboard].[DashboardPresentation]
            SET
                     [Name] = @Name
                    ,[DashboardCount] = @DashboardCount,
                    ,[ConfigJson] = @ConfigJson
                    ,[ModifiedOn] = @ModifiedOn
                    ,[ModifiedByName] = @ModifiedByName
            WHERE    [DashboardPresentation] = @DashboardPresentationId AND [ParentEntityId] = @ParentEntityId ";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
                {
                    command.AddArguments(dto);
                    await command.Execute();
                }
            }
        }

        internal async Task DeleteAsync(int dashboardPresentationId)
        {
            var exists = await GetAsync(dashboardPresentationId);
            if (exists == null)
            {
                throw new ArgumentException($"Dashboard Presentation Name does not exist.");
            }

            string sql = @"
            UPDATE   [dashboard].[DashboardPresentation]
            SET
                    [Deleted] = 1
                    ,[ModifiedOn] = @modifiedOn
                    ,[ModifiedByName] = @modifiedByName
            WHERE    [dashboardPresentationId] = @dashboardPresentationId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArgument("dashboardPresentationId", dashboardPresentationId);
                command.AddArgument("modifiedOn", DateTimeOffset.Now);
                command.AddArgument("modifiedByName", "System");
                await command.Execute();
            }    
        }
    }
}
