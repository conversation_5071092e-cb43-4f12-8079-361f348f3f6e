﻿using Redi.Prime3.MicroService.BaseLib;
using Sql;

namespace MicroserviceDashboardContract.Dtos
{
    [Mappable(nameof(UserId))]
    public class BaseUserCDto : DtoBase
    {
        public Guid UserId { get; set; }
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? FullName { get; set; }
        /// <summary>
        /// This is the users primary role. Eg. Accounts Manager, Sales Rep, etc
        /// </summary>
        public Guid? RoleId { get; set; }
        public string? RoleName { get; set; }
        public string? Position { get; set; }
        public bool IsPrimaryContact { get; set; }
        public Guid? ManagerUserId { get; set; }
        public string? ManagerUserName { get; set; }
    }

    public class GetListUserCDto : BaseUserCDto
    {

    }

    public class UserWithAccessCDto : BaseUserCDto
    {
        public int DashboardAccessRoleId { get; set; }
    }
}
