﻿using MicroserviceBackendListing.Dtos;
using MicroserviceContract.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Collections.Specialized;
using Microsoft.Data.SqlClient;

namespace MicroserviceBackendListing.BusinessLogic
{
    public class Attribute : BusinessLogicBase
    {
        private readonly Func<DropdownValue> _drop;
        private readonly Func<AttributeGroup> _AttributeGroupFactory;
        private readonly Func<AttributeInputType> _AttributeInputTypeFactory;
      
        public Attribute(IUnitOfWork u, Func<DropdownValue> drop, Func<AttributeGroup> AttributeGroupFactory, Func<AttributeInputType> AttributeInputTypeFactory)
        {
            _unitOfWork = u;
            _drop = drop;
            _AttributeGroupFactory = AttributeGroupFactory;
            _AttributeInputTypeFactory = AttributeInputTypeFactory;
        }

        /// <summary>
        /// Get a Single Attribute with an AttributeCode
        /// </summary>
        /// <param name="AttributeCode"></param>
        /// <returns></returns>
        internal async Task<GetAttributeDto> GetAsync(string? AttributeCode)
        {
            if (AttributeCode == null)
            {
                throw new HttpRequestException("AttributeCode cannot be null", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }
            string sql = @"
                        SELECT [Attribute].[AttributeCode], 
                        [Attribute].[Label],
                        [Attribute].[AttributeGroupCode],  
                        [Attribute].[InputTypeCode],
                        [AttributeInputType].[Label] AS DataTypeLabel,
                        [Attribute].[Description],
                        [Attribute].[SortOrder], 
                        [Attribute].[IsEnabled],
                        [Attribute].[NumericDecimalPlaces] 
                        FROM [listing].[Attribute] [Attribute]
                        LEFT JOIN [listing].[AttributeInputType] ON [Attribute].[InputTypeCode] = [listing].[AttributeInputType].[InputTypeCode]
                        WHERE [AttributeCode] = @AttributeCode";
            GetAttributeDto query;
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("AttributeCode", AttributeCode);
                query = await command.SelectSingle<GetAttributeDto>();
            }
            if (query == null || query.AttributeCode == null)
            {
                return query;
            }
            string code = query.AttributeCode;
            List<DropDownValueDto> list = await _drop().GetListDropDownByAttributeCode(code);
            query.DropdownValues = list;
            return query;
        }

        /// <summary>
        /// Create an Attribute
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        /// <exception cref="HttpRequestException"></exception>
        internal async Task CreateAsync(GetAttributeCDto dto)
        {
            if (dto.AttributeCode == null || dto.AttributeGroupCode == null)
            {
                throw new HttpRequestException("Attribute and Attribute Group Code cannot be null", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (dto.AttributeValueTypeCode == null)
            {
                throw new HttpRequestException("AttributeValueTypeCode cannot be null", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (dto.IsEnabled == false)
            {
                throw new HttpRequestException("Attempting to Create a disabled Attribute", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }
            
            bool isValidGroupCode = dto.AttributeGroupCode == null || await _AttributeGroupFactory().GroupIsEnable(dto.AttributeGroupCode);
            if (!isValidGroupCode)
            {
                throw new HttpRequestException($"Invalid Group Code '{dto.AttributeGroupCode}'", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            bool isValidInputTypeCode = dto.InputTypeCode == null || await _AttributeInputTypeFactory().TypeIsEnable(dto.InputTypeCode);
            if (!isValidInputTypeCode)
            {
                throw new HttpRequestException($"Invalid Data Type Code '{dto.InputTypeCode}'", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            var exists = await GetAsync(dto.AttributeCode);
                if (exists != null)
                {
                    await UpdateAsync(dto);
                    return;
                }      
            string sql1 = @"
                        INSERT INTO [listing].[Attribute]( 
                        [AttributeCode], 
                        [Label],
                        [AttributeGroupCode], 
                        [AttributeValueTypeCode],
                        [InputTypeCode], 
                        [NumericDecimalPlaces],
                        [Description],                         
                        [SortOrder], 
                        [Icon],
                        [ReadAccessClaim],
                        [WriteAccessClaim],
                        [IsManyAllowed],
                        [IsEnabled])
                        VALUES(
                        @AttributeCode, 
                        @Label,
                        @AttributeGroupCode, 
                        @AttributeValueTypeCode,
                        @InputTypeCode, 
                        @NumericDecimalPlaces,
                        @Description,                         
                        @SortOrder, 
                        @Icon,
                        @ReadAccessClaim,
                        @WriteAccessClaim,
                        @IsManyAllowed,
                        @IsEnabled)";
            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql1))
            {
                command.AddArguments(dto);
                await command.Execute();
            }
            if(dto.DropdownValues != null)
            {
                foreach (var dropDto in dto.DropdownValues)
                {
                    dropDto.AttributeCode = dto.AttributeCode;
                    await _drop().CreateAsync(dropDto);
                }
            }
        }

        /// <summary>
        /// Update an Attribute
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        /// <exception cref="HttpRequestException"></exception>
        internal async Task UpdateAsync(GetAttributeCDto dto)
        {
            if (dto.AttributeCode == null || dto.AttributeGroupCode == null)
            {
                throw new HttpRequestException("Attribute and Attribute Group Code cannot be null", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }
            
            bool isValidGroupCode = dto.AttributeGroupCode == null || await _AttributeGroupFactory().GroupIsEnable(dto.AttributeGroupCode);
            if (!isValidGroupCode)
            {
                throw new HttpRequestException($"Invalid Group Code '{dto.AttributeGroupCode}'", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }
            
            bool isValidInputTypeCode = dto.InputTypeCode == null || await _AttributeInputTypeFactory().TypeIsEnable(dto.InputTypeCode);
            if (!isValidInputTypeCode)
            {
                throw new HttpRequestException($"Invalid Data Type Code '{dto.InputTypeCode}'", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            var exists = await GetAsync(dto.AttributeCode);
            if (exists == null)
            {
                await CreateAsync(dto);
            }
            else
            {
                
                string sql = @"
                    UPDATE [listing].[Attribute]
                    SET
                        [Label] = @Label,
                        [AttributeGroupCode] = @AttributeGroupCode,  
                        [AttributeValueTypeCode] = @AttributeValueTypeCode,
                        [InputTypeCode] = @InputTypeCode, 
                        [Description] = @Description,                         
                        [SortOrder] = @SortOrder, 
                        [IsEnabled] = @IsEnabled,
                        [IsManyAllowed] = @IsManyAllowed,
                        [NumericDecimalPlaces] = @NumericDecimalPlaces,
                        [Icon] = @Icon,
                        [ReadAccessClaim] = @ReadAccessClaim,
                        [WriteAccessClaim] = @WriteAccessClaim
                    WHERE [AttributeCode] = @AttributeCode";
                using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
                {
                    command.AddArguments(dto);
                    await command.Execute();
                }
                if (dto.DropdownValues != null)
                {
                    foreach (var dropDto in dto.DropdownValues)
                    {
                        dropDto.AttributeCode = dto.AttributeCode;
                        await _drop().UpdateAsync(dropDto);
                    }
                }
            }
        }

        /// <summary>
        /// Delete an Attribute
        /// </summary>
        /// <param name="attributeCode"></param>
        /// <returns></returns>
        /// <exception cref="HttpRequestException">Attribute not found</exception>
        internal async Task DeleteAsync(string attributeCode)
        {
            var exist = await GetAsync(attributeCode);
            if(exist == null)
            {
                throw new HttpRequestException($"Attribute with code '{attributeCode}' not found", null, System.Net.HttpStatusCode.NotFound);
            }
            else
            {
                string sql = @"
                    DELETE [listing].[Attribute]
                    WHERE [AttributeCode] = @AttributeCode;";
                using (var command = await _unitOfWork.CmdFactory(CmdType.Delete, sql))
                {
                    command.AddArguments(exist);
                    await command.Execute();
                }
            }
        }

        /// <summary>
        /// Enable an Attribute
        /// </summary>
        /// <param name="attributeCode"></param>
        /// <returns></returns>
        /// <exception cref="HttpRequestException">Attribute not found</exception>
        internal async Task EnableAsync(string attributeCode)
        {
            var exist = await GetAsync(attributeCode);
            if (exist == null)
            {
                throw new HttpRequestException($"Attribute with code '{attributeCode}' not found", null, System.Net.HttpStatusCode.NotFound);
            }
            else
            {
                string sql = @"
                    UPDATE [listing].[Attribute]
                    SET
	                      [IsEnabled] = 1
                    WHERE [AttributeCode] = @AttributeCode;";
                using (var command = await _unitOfWork.CmdFactory(CmdType.Delete, sql))
                {
                    command.AddArguments(exist);
                    await command.Execute();
                }
            }
        }

        /// <summary>
        /// Disable an Attribute
        /// </summary>
        /// <param name="attributeCode"></param>
        /// <returns></returns>
        /// <exception cref="HttpRequestException">Attribute not found</exception>
        internal async Task DisableAsync(string attributeCode)
        {
            var exist = await GetAsync(attributeCode);
            if (exist == null)
            {
                throw new HttpRequestException($"Attribute with code '{attributeCode}' not found", null, System.Net.HttpStatusCode.NotFound);
            }
            else
            {
                string sql = @"
                    UPDATE [listing].[Attribute]
                    SET
	                      [IsEnabled] = 0
                    WHERE [AttributeCode] = @AttributeCode;";
                using (var command = await _unitOfWork.CmdFactory(CmdType.Delete, sql))
                {
                    command.AddArguments(exist);
                    await command.Execute();
                }
            }
        }

        /// <summary>
        /// Return a list of all Attributes with filtering options
        /// </summary>
        /// <param name="standardListParameters"></param>
        /// <param name="showDisable"></param>
        /// <returns></returns>
        internal async Task<ListResponseDto<AttributeDto>> GetListAllAsync(StandardListParameters standardListParameters, bool? showDisable = false)
        {
            StringDictionary canBeSortedBy = new StringDictionary()
             {
                 { "SortOrder", "[Attribute].[SortOrder]" },
                 { "AttributeCode", "[Attribute].[AttributeCode]" },
                 { "Label", "[Attribute].[Label]" },
                 { "AttributeGroupCode", "[Attribute].[AttributeGroupCode]" },
                 { "InputTypeCode", "[Attribute].[InputTypeCode]" },
                 { "AttributeValueTypeCode", "[Attribute].[AttributeValueTypeCode]" },
                 { "IsManyAllowed", "[Attribute].[IsManyAllowed]" },
             };
            string sql = @"
                        SELECT  
                        [Attribute].[Label], 
                        [Attribute].[AttributeCode], 
                        [Attribute].[InputTypeCode],
                        [Attribute].[Description],                         
                        [Attribute].[SortOrder], 
                        [Attribute].[IsEnabled],
                        [Attribute].[NumericDecimalPlaces]
                        FROM [listing].[Attribute][Attribute]";
            
            if (showDisable.HasValue && (bool)!showDisable)
            {
                sql += $" AND [Attribute].[IsEnabled] = 1";
            }
            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "SortOrder");

            sql += $" OFFSET @offset ROWS";
            sql += $" FETCH NEXT @limit ROWS ONLY";
            var result = new ListResponseDto<AttributeDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {

                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);

                result.List = await command.SelectMany<AttributeDto>();
            }
            sql = @"
                        SELECT COUNT(*) AS totalNumOfRows
                        FROM [listing].[Attribute][Attribute]";

            if (showDisable.HasValue && (bool)!showDisable)
            {
                sql += $" AND [Attribute].[IsEnabled] = 1";
            }
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {

                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }
            return result;
        }

        /// <summary>
        /// Return a list of Attributes by Attribute Group Code with filtering options
        /// </summary>
        /// <param name="standardListParameters"></param>
        /// <param name="AttributeGroupCode"></param>
        /// <param name="showDisable"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        internal async Task<ListResponseDto<AttributeDto>> GetAttributesByGroupCodeAsync(StandardListParameters? standardListParameters, string? AttributeGroupCode, bool? showDisable = false)
        {
            if(standardListParameters == null)
            {
                standardListParameters = new StandardListParameters();
            }

            StringDictionary canBeSortedBy = new StringDictionary()
             {
                 { "SortOrder", "[Attribute].[SortOrder]" },
                 { "AttributeCode", "[Attribute].[AttributeCode]" },
                 { "Label", "[Attribute].[Label]" },
                 { "InputTypeCode", "[Attribute].[InputTypeCode]" },
                 { "AttributeValueTypeCode", "[Attribute].[AttributeValueTypeCode]" },
                 { "IsManyAllowed", "[Attribute].[IsManyAllowed]" },
             };
            bool isValidGroupCode = AttributeGroupCode == null || await _AttributeGroupFactory().GroupIsEnable(AttributeGroupCode);
            if (!isValidGroupCode)
            {
                throw new HttpRequestException($"Attribute Group Code {AttributeGroupCode} is invalid", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }
            string sql = @"
                        SELECT  
                        [Attribute].[Label], 
                        [Attribute].[AttributeCode], 
                        [Attribute].[InputTypeCode],
                        [Attribute].[Description],                         
                        [Attribute].[SortOrder], 
                        [Attribute].[IsEnabled],
                        [Attribute].[NumericDecimalPlaces]
                        FROM [listing].[Attribute][Attribute]
                        WHERE[Attribute].[AttributeGroupCode] = @AttributeGroupCode ";
            if (showDisable.HasValue && (bool)!showDisable)
            {
                sql += $" AND [Attribute].[IsEnabled] = 1";
            }
            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "SortOrder");

            sql += $" OFFSET @offset ROWS";
            sql += $" FETCH NEXT @limit ROWS ONLY";
            var result = new ListResponseDto<AttributeDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("AttributeGroupCode", AttributeGroupCode);
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);

                result.List = await command.SelectMany<AttributeDto>();
            }
            sql = @"
                        SELECT COUNT(*) AS totalNumOfRows
                        FROM [listing].[Attribute][Attribute]
                        WHERE[Attribute].[AttributeGroupCode] = @AttributeGroupCode";

            if (showDisable.HasValue && (bool)!showDisable)
            {
                sql += $" AND [Attribute].[IsEnabled] = 1";
            }
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("AttributeGroupCode", AttributeGroupCode);

                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }
            return result;
        }
    }
}
