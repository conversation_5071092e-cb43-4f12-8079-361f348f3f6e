﻿using MicroserviceBackendListing.BusinessLogic.Base;

using MicroserviceContract.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Microsoft.Extensions.Caching.Memory;
using Sql;
using System.Collections.Specialized;
using MicroserviceBackendListing.Dtos;

namespace MicroserviceBackendListing.BusinessLogic
{
    /// <summary>
    /// Tags can linked to any Listing.
    /// A Listing can have 1 or more tags.
    /// Tags may be put into Tag Sets to control how and where they are displayed - A Tag Set could be treated as a Field
    /// Tags can be returned either by Tag Set, or by TagSetGroup. The TagSetGroup wraps the tag sets allowing a call to request all tag sets and tags that are linked to the group.
    /// This class handles everything related to a Tag including: creating, updating and deleting tags; linking tags to listing and removing tags from listings.
    /// </summary>
    public class Tag : BusinessLogicBase
    {
        private UtilityFunctions _utils;
        private ManageListing _manageListingFactory;

        public Tag(IUnitOfWork u, UtilityFunctions utils, ManageListing manageListingFacory)
        {
            _unitOfWork = u;
            _utils = utils;
            _manageListingFactory = manageListingFacory;
        }

        public async Task<TagCDto> GetAsync(int? tagId = null, string? tagLabel = "", int? tagSetId = null)
        {
            string cacheKey = "GetTag" + tagId + tagLabel + tagSetId;

            if (_memoryCache.TryGetValue(cacheKey, out TagCDto? cacheValue))
            {
                return cacheValue.DeepClone();
            }

            string sql = @"
                 SELECT 
                         [Tag].[TagId]
                        ,[Tag].[Label]
                        ,[Tag].[TagSetId]
                        ,[TagSet].[Label] AS [TagSetLabel]
                        ,[Tag].[IsEnabled]
                        ,[Tag].[SortOrder]
                        ,[Tag].[Note]
                        ,[Tag].[Icon]
                        ,[Tag].[Colour]
                        ,[Tag].[ImageUrl]
                  FROM   [listing].[Tag]
             LEFT JOIN   [listing].[TagSet] ON [Tag].[TagSetId] = [TagSet].[TagSetId]";

            if (tagId != null)
            {
                sql += "WHERE   [Tag].[TagId] = @tagId";
            }
            else
            {
                sql += "WHERE   [Tag].[Label] = @tagLabel AND [Tag].[IsEnabled] = 1 AND [Tag].[TagSetId] = @tagSetId AND ([Tag].[TenantId] = @TenantId OR [Tag].[TenantId] is NULL  ) ";
            }

            var result = new TagCDto();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("tagId", tagId);
                command.AddArgument("tagLabel", tagLabel);
                command.AddArgument("tagSetId", tagSetId);
                command.AddArgument("TenantId", _utils.TenantId);
                result = await command.SelectSingle<TagCDto>();
            }

            _memoryCache.Set(cacheKey, result.DeepClone(), CacheOptions());

            return result;
        }

        public async Task<ListResponseDto<TagCDto>> GetListAsync(StandardListParameters standardListParameters, List<int> tagSetIds, bool isEnabled = true, int? tagSetGroupId = null, long? excludeTagsInListingId = null)
        {
            if (tagSetIds != null && tagSetIds.Count > 0)
            {
                foreach (var tagSetId in tagSetIds)
                {
                    if (await GetTagSetAsync(tagSetId) == null)
                    {
                        throw new ApiErrorException($"Cannot find a match for this '{tagSetId}' tagSetId");
                    }
                }
            }

            if (standardListParameters == null)
            {
                standardListParameters = new StandardListParameters();
            }

            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "SortOrder", "[Tag].[SortOrder]" },
                { "Label", "[Tag].[Label]" },
                { "TagSetLabel", "[TagSet].[Label]" },
                { "Icon", "[Tag].[Icon]" },
                { "Colour", "[Tag].[Colour]" }
            };

            string sql = @"
            SELECT 
                          [Tag].[TagId]
                        ,[Tag].[Label]
                        ,[Tag].[TagSetId]
                        ,[TagSet].[Label] AS [TagSetLabel]
                        ,[TagSet].[Icon] AS [TagSetIcon]
                        ,[TagSet].[Note] AS [TagSetNote]
                         ,[Tag].[IsEnabled]
                        ,[Tag].[SortOrder]
                        ,[Tag].[Note]
                        ,[Tag].[Icon]
                        ,[Tag].[Colour]
                        ,[Tag].[ImageUrl]
            FROM   [listing].[Tag]
            ";

            string sqlJoin = @"LEFT JOIN [listing].[TagSet] ON [Tag].[TagSetId] = [TagSet].[TagSetId]
            ";

            string whereCondition = " WHERE [Tag].[IsEnabled] = @isEnabled AND ([Tag].[TenantId] = @TenantId OR [Tag].[TenantId] is NULL  ) AND ([Tag].[TagSetId] IS NULL OR [TagSet].[IsEnabled] = 1) ";

            if (tagSetIds != null && tagSetIds.Count > 0)
            {
                whereCondition += " AND [Tag].[TagSetId] in (@tagSetIds) ";
            }
            if (tagSetGroupId != null)
            {
                whereCondition += " AND EXISTS (Select 1 FROM [Listing].[TagSetGroupTagSet] TSG WHERE TSG.[TagSetGroupId] = @TagSetGroupId AND TSG.TagSetId = [TagSet].[TagSetId] ) ";
            }
            if (excludeTagsInListingId != null)
            {
                whereCondition += " AND NOT EXISTS (Select 1 FROM [Listing].[ListingTag] LT WHERE LT.[TagId] = [Tag].[TagId] AND LT.[Deleted] = 0 AND LT.[ListingId] = @ListingId ) ";
            }

            sql += sqlJoin;
            sql += whereCondition;
            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "SortOrder");
            sql += $" OFFSET @Offset ROWS";
            sql += $" FETCH NEXT @Limit ROWS ONLY";

            var response = new ListResponseDto<TagCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("tagSetIds", tagSetIds);
                command.AddArgument("isEnabled", isEnabled);
                command.AddArgument("tagSetGroupId", tagSetGroupId);
                command.AddArgument("TenantId", _utils.TenantId);
                command.AddArgument("ListingId", excludeTagsInListingId);
                command.AddArgument("Offset", standardListParameters.Offset);
                command.AddArgument("Limit", standardListParameters.Limit);

                response.List = await command.SelectMany<TagCDto>();
            }

            sql = @"SELECT COUNT(*) AS totalNumOfRows
                    FROM [listing].[Tag] ";
            sql += sqlJoin;
            sql += whereCondition;

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("tagSetIds", tagSetIds);
                command.AddArgument("isEnabled", isEnabled);
                command.AddArgument("tagSetGroupId", tagSetGroupId);
                command.AddArgument("TenantId", _utils.TenantId);
                command.AddArgument("ListingId", excludeTagsInListingId);
                response.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return response;
        }

        public async Task<ListResponseDto<ListingTagListCDto>> GetTagsForListing(StandardListParameters standardListParameters, long listingId, List<int> tagSetIds, int? tagSetGroupId)
        {
            if (tagSetIds != null && tagSetIds.Count > 0)
            {
                foreach (var tagSetId in tagSetIds)
                {
                    if (await GetTagSetAsync(tagSetId) == null)
                    {
                        throw new ApiErrorException($"Cannot find a match for this '{tagSetId}' tagSetId");
                    }
                }
            }

            if (standardListParameters == null)
            {
                standardListParameters = new StandardListParameters();
            }

            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "SortOrder", "[ListingTag].[SortOrder], [TagSet].[SortOrder], [Tag].[SortOrder]" },
                { "Label", "[Tag].[Label]" },
                { "TagLabel", "[Tag].[Label]" },
                { "TagIcon", "[Tag].[Icon]" },
                { "TagColour", "[Tag].[Colour]" },
                { "TagSetLabel", "[TagSet].[Label]" },
                { "CreatedOn", "[ListingTag].[CreatedOn]" },
                { "ModifiedOn", "[ListingTag].[ModifiedOn]" },
                { "CreatedByName", "[ListingTag].[CreatedByName]" },
                { "ModifiedByName", "[ListingTag].[ModifiedByName]" }
            };

            string sql = @"
            SELECT 
                        [ListingTag].[ListingTagId], 
                        [ListingTag].[TagId], 
                        [ListingTag].[SortOrder],
                        [Tag].[Label] AS TagLabel,
                        [Tag].[Icon] AS TagIcon,
                        [Tag].[Colour] AS TagColour,
                        [Tag].[Note] AS TagNote,
                        [Tag].[ImageUrl] AS TagImageUrl,
                        [TagSet].[TagSetId], 
                        [TagSet].[Label] AS TagSetLabel, 
                        [TagSet].[Icon] AS TagSetIcon, 
                        [TagSet].[Note] AS TagSetNote, 
                        [ListingTag].[CreatedOn],
                        [ListingTag].[CreatedByName]
                        FROM [listing].[ListingTag] [ListingTag]
                        ";
            string sqlJoin = @"INNER JOIN [listing].[Tag] ON [ListingTag].[TagId] = [Tag].[TagId] AND [Tag].[IsEnabled] = 1
                        LEFT JOIN [listing].[TagSet] ON [TagSet].[TagSetId] = [Tag].[TagSetId]
                        ";
            string whereCondition = " WHERE [ListingTag].[ListingId] = @ListingId AND [ListingTag].[Deleted] = 0 AND ([Tag].[TagSetId] IS NULL OR [TagSet].[IsEnabled] = 1) ";

            if (tagSetIds != null && tagSetIds.Count > 0)
            {
                whereCondition += " AND [Tag].[TagSetId] IN (@tagSetIds) ";
            }
            if (tagSetGroupId != null)
            {
                whereCondition += " AND EXISTS (Select 1 FROM [Listing].[TagSetGroupTagSet] TSG WHERE TSG.[TagSetGroupId] = @TagSetGroupId AND TSG.TagSetId = [TagSet].[TagSetId] ) ";
            }

            sql += sqlJoin;
            sql += whereCondition;
            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "SortOrder");
            sql += $" OFFSET @Offset ROWS";
            sql += $" FETCH NEXT @Limit ROWS ONLY";

            var response = new ListResponseDto<ListingTagListCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("tagSetIds", tagSetIds);
                command.AddArgument("tagSetGroupId", tagSetGroupId);
                command.AddArgument("listingId", listingId);
                command.AddArgument("Offset", standardListParameters.Offset);
                command.AddArgument("Limit", standardListParameters.Limit);

                response.List = await command.SelectMany<ListingTagListCDto>();
            }

            sql = @"SELECT COUNT(*) AS totalNumOfRows
                    FROM   [listing].[ListingTag]
                    ";
            sql += sqlJoin;
            sql += whereCondition;

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("tagSetIds", tagSetIds);
                command.AddArgument("tagSetGroupId", tagSetGroupId);
                command.AddArgument("listingId", listingId);
                response.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return response;
        }

        public async Task<long> AddToListingAsync(long listingId, int? tagId, string? tagLabel, int? tagSetId, int? sortOrder = null, bool autoCreateNewTags = false)
        {
            // Check if tagSetId exists
            if (tagSetId != null && await GetTagSetAsync(tagSetId.Value) == null)
            {
                throw new ApiErrorException($"Cannot find a match for this '{tagSetId}' TagSetId");
            }

            if (tagId != null)
            {
                if (await GetAsync(tagId.Value) == null)
                {
                    throw new ApiErrorException($"TagId does not exist '{tagId}' tagId");
                }
            }
            else
            {
                if (string.IsNullOrEmpty(tagLabel))
                {
                    throw new ApiErrorException($"tagId or tagLabel must be specified.");
                }
                var tag = await GetAsync(tagLabel: tagLabel, tagSetId: tagSetId);
                if (tag != null)
                {
                    tagId = tag.TagId;
                }
                else
                {
                    if (autoCreateNewTags == false)
                    {
                        throw new ApiErrorException($"Unknown Tag '{tagLabel}'."); ;
                    }
                    // Create new tag
                    tagId = await CreateTagAsync(tagLabel, tagSetId, sortOrder);
                }
            }

            string sql = @"
            IF NOT EXISTS (SELECT 1 FROM [listing].[ListingTag] WHERE TagId = @TagId AND ListingId = @ListingId AND Deleted = 0)
            INSERT INTO  [listing].[ListingTag]
                        (
                         [ListingId]
                        ,[TagId]
                        ,[SortOrder]
                        ,[CreatedOn]
                        ,[CreatedByName]
                        ,[ModifiedOn]
                        ,[ModifiedByName]
                        ,[Deleted]
                        )
                VALUES
                        (
                        @ListingId
                        ,@TagId
                        ,@SortOrder
                        ,@CreatedOn
                        ,@CreatedByName
                        ,NULL
                        ,NULL
                        ,0
                        )";

            long listingTagId = 0;
            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArgument("TagId", tagId);
                command.AddArgument("ListingId", listingId);
                command.AddArgument("SortOrder", sortOrder);
                command.AddArgument("CreatedOn", DateTimeOffset.UtcNow);
                command.AddArgument("CreatedByName", _utils.UserFullName);

                listingTagId = await command.ExecuteAndReturnIdentity();
            }

            return listingTagId;
        }

        public async Task BulkAddToListingAsync(long listingId, List<int> tagIds)
        {
            if (tagIds != null && tagIds.Count > 0)
            {
                foreach (var tagId in tagIds)
                {
                    if (await GetAsync(tagId) == null)
                    {
                        throw new ApiErrorException($"Cannot find a match for this '{tagId}' tagId");
                    }
                }

                var now = DateTimeOffset.Now;
                // The sql package creates a table with _id as the column name
                string list_col_name = "_id";
                string sql = $@"
                    INSERT INTO [listing].[ListingTag]
                                (
                                    [ListingId]
                                ,[TagId]
                                ,[SortOrder]
                                ,[CreatedOn]
                                ,[CreatedByName]
                                ,[Deleted]
                                )
                    SELECT
                        @ListingId
                        , [tags].{list_col_name}
                        , isnull([existingTags].[SortOrder], 0) + row_number() over (order by [existingTags].[SortOrder])
                        , @CreatedOn
                        , @CreatedByName
                        , 0
                    FROM (@TagIds) [tags]
                                outer apply (select max([SortOrder]) as [SortOrder]
                        FROM [listing].[ListingTag]
                        WHERE [ListingId] = @ListingId ) as [existingTags]
                        WHERE [tags].{list_col_name} NOT IN (SELECT [TagId] FROM [listing].[ListingTag] WHERE [ListingId] = @ListingId AND [Deleted] = 0 )";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
                {
                    command.AddArgument("TagIds", tagIds);
                    command.AddArgument("ListingId", listingId);
                    command.AddArgument("CreatedOn", now);
                    command.AddArgument("CreatedByName", _utils.UserFullName);

                    await command.Execute();
                }
            }

        }

        /// <summary>
        /// Does the same as <see cref="BulkAddToListingAsync(long, List{int})"/> but also removes tags not in the provided list
        /// </summary>
        /// <param name="listingId">The Listing to modify, assumes the policy verified you have edit access to this listing.</param>
        /// <param name="tagIds">A list of tagIds</param>
        /// <returns></returns>
        /// <exception cref="ApiErrorException"></exception>
        public async Task BulkUpdateListingTagsAsync(long listingId, List<int> tagIds)
        {
            string list_col_name = "_id";
            string invalidTagSql = $@"
                SELECT 
                    [tags].{list_col_name} AS [InvalidTag]
                FROM (@TagIds) [tags]
                WHERE [tags].{list_col_name} NOT IN (SELECT [TagId] FROM [listing].[Tag] WHERE [IsEnabled] = 1)";
            List<int> invalidTags = new List<int>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, invalidTagSql))
            {
                command.AddArgument("TagIds", tagIds);

                invalidTags = await command.SelectMany<int>("InvalidTag");
            }

            if (invalidTags.Count > 0)
            {
                throw new ApiErrorException($"Cannot find a match for the following tags: {string.Join(",", invalidTags)}");
            }


            var now = DateTimeOffset.Now;
            // The sql package creates a table with _id as the column name

            string sql = $@"
                UPDATE [listing].[ListingTag]
                SET [Deleted] = 1
                    ,[ModifiedOn] = @Now
                    ,[ModifiedByName] = @Name
                WHERE [ListingId] = @ListingId AND [TagId] NOT IN (@TagIds) AND [Deleted] = 0

                INSERT INTO [listing].[ListingTag]
                            (
                                [ListingId]
                            ,[TagId]
                            ,[SortOrder]
                            ,[CreatedOn]
                            ,[CreatedByName]
                            ,[Deleted]
                            )
                SELECT
                    @ListingId
                    , [tags].{list_col_name}
                    , isnull([existingTags].[SortOrder], 0) + row_number() over (order by [existingTags].[SortOrder])
                    , @Now
                    , @Name
                    , 0
                FROM (@TagIds) [tags]
                            outer apply (select max([SortOrder]) as [SortOrder]
                    FROM [listing].[ListingTag]
                    WHERE [ListingId] = @ListingId AND [Deleted] = 0 ) as [existingTags]
                    WHERE [tags].{list_col_name} NOT IN (SELECT [TagId] FROM [listing].[ListingTag] WHERE [ListingId] = @ListingId AND [Deleted] = 0 )";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("TagIds", tagIds);
                command.AddArgument("ListingId", listingId);
                command.AddArgument("Now", now);
                command.AddArgument("Name", _utils.UserFullName);

                await command.Execute();
            }
        }

        public async Task<int> CreateTagAsync(string? tagLabel, int? tagSetId, int? sortOrder = null, string? tagColour = null, string? tagIcon = null, string? tagImageUrl = null, string? tagNote = null)
        {
            // Check if tagSetId exists
            if (tagSetId != null && await GetTagSetAsync(tagSetId.Value) == null)
            {
                throw new ApiErrorException($"Cannot find a match for this '{tagSetId}' TagSetId");
            }

            if (string.IsNullOrEmpty(tagLabel))
            {
                throw new ApiErrorException($"tagLabel must be specified.");
            }

            var tagExists = await GetAsync(tagLabel: tagLabel, tagSetId: tagSetId);

            if (tagExists != null && tagExists.TagId != null)
            {
                return (int)tagExists.TagId;
            }

            string sql = @"
            INSERT INTO  [listing].[Tag]
                        (
                         [SortOrder]
                        ,[IsEnabled]
                        ,[TagSetId]
                        ,[Label]
                        ,[TenantId]
                        ,[Icon]
                        ,[Colour]
                        ,[Note]
                        ,[ImageUrl]
                        )
                VALUES
                        (
                        ,@SortOrder
                        ,@IsEnabled
                        ,@TagSetId
                        ,@Label
                        ,@TenantId
                        ,@tagIcon
                        ,@tagColour
                        ,@tagNote
                        ,@tagImageUrl
                        )";

            int tagId = -1;
            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArgument("SortOrder", sortOrder);
                command.AddArgument("IsEnabled", true);
                command.AddArgument("Label", tagLabel);
                command.AddArgument("TagSetId", tagSetId);
                command.AddArgument("tagIcon", tagIcon);
                command.AddArgument("tagColour", tagColour);
                command.AddArgument("tagNote", tagNote);
                command.AddArgument("tagImageUrl", tagImageUrl);
                command.AddArgument("TenantId", _utils.TenantId);

                tagId = await command.ExecuteAndReturnIdentity();
            }

            return tagId;
        }

        public async Task UpdateTagAsync(TagCDto dto)
        {
            if (dto.TagId == null)
            {
                throw new ApiErrorException($"Cannot find a match for this '{dto.TagId}' TagId");
            }

            var exists = await GetAsync((int)dto.TagId);
            if (exists == null)
            {
                throw new ApiErrorException($"Cannot find a match for this '{dto.TagId}' TagId");
            }

            string sql = @"
            UPDATE  [listing].[Tag]
            SET
                     [Label] = @Label,
                     [SortOrder] = @SortOrder,
                     [IsEnabled] = @IsEnabled,
                     [TagSetId] = @TagSetId,
                     [Icon] = @Icon,
                     [Colour] = @Colour,
                     [Note] = @Note,
                     [ImageUrl] = @ImageUrl
            WHERE    [TagId] = @tagId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("Label", dto.TagSetId);
                command.AddArgument("SortOrder", dto.SortOrder);
                command.AddArgument("IsEnabled", dto.IsEnabled);
                command.AddArgument("TagSetId", dto.TagSetId);
                command.AddArgument("tagId", dto.TagId);
                command.AddArgument("Icon", dto.Icon);
                command.AddArgument("Colour", dto.Colour);
                command.AddArgument("Note", dto.Note);
                command.AddArgument("ImageUrl", dto.ImageUrl);
                await command.Execute();
            }
        }

        public async Task RemoveFromListingAsync(long listingId, long? listingTagId = null, List<int>? tagIds = null)
        {

            string sql = @$"
            UPDATE   [listing].[ListingTag]
            SET
                     [Deleted] = 1
                    ,[ModifiedOn] = @ModifiedOn
                    ,[ModifiedByName] = @ModifiedByName
            WHERE    [ListingId] = @listingId AND [Deleted] = 0
            {(listingTagId != null ? "AND [ListingTagId] = @listingTagId " : "")}
            {(tagIds != null && tagIds.Count > 0 ? "AND [TagId] IN (@tagIds)" : "")} ";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Delete, sql))
            {
                command.AddArgument("ModifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("ModifiedByName", _utils.UserFullName);
                command.AddArgument("listingId", listingId);
                command.AddArgument("listingTagId", listingTagId);
                command.AddArgument("tagIds", tagIds);

                await command.Execute();
            }
        }

        public async Task UpdateListingTagSortOrderAsync(long listingId, long? listingTagId = null, int? sortOrder = null, int? tagId = null)
        {

            string sql = @"
            UPDATE   [listing].[ListingTag]
            SET
                     [SortOrder] = @sortOrder
                    ,[ModifiedOn] = @ModifiedOn
                    ,[ModifiedByName] = @ModifiedByName
            WHERE    [ListingId] = @listingId AND ([ListingTagId] = @listingTagId OR [TagId] = @tagId ) AND [SortOrder] != @sortOrder ";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Delete, sql))
            {
                command.AddArgument("ModifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("ModifiedByName", _utils.UserFullName);
                command.AddArgument("sortOrder", sortOrder);
                command.AddArgument("listingId", listingId);
                command.AddArgument("tagId", tagId);
                command.AddArgument("listingTagId", listingTagId);

                await command.Execute();
            }
        }

        public async Task<TagSetCDto> GetTagSetAsync(int tagSetId)
        {
            string cacheKey = "GetTagSet" + tagSetId;

            if (_memoryCache.TryGetValue(cacheKey, out TagSetCDto? cacheValue))
            {
                return cacheValue.DeepClone();
            }

            string sql = @"
            SELECT 
                     [TagSetId]
                    ,[Label]
                    ,[Note]
                    ,[Icon]
                    ,[ParentTagSetId]
                    ,[SortOrder]
                    ,[IsEnabled]
                    ,[ParentTagSetId]
            FROM     [listing].[TagSet]
            WHERE    [TagSetId] = @TagSetId";

            var result = new TagSetCDto();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("TagSetId", tagSetId);
                result = await command.SelectSingle<TagSetCDto>();
            }

            _memoryCache.Set(cacheKey, result.DeepClone(), CacheOptions());

            return result;
        }

        public async Task<int> AddTagSetAsync(TagSetCDto dto)
        {
            string sql = @"
            INSERT INTO  [listing].[TagSet]
                        (
                         [Label]
                    ,[Note]
                    ,[Icon]
                    ,[ParentTagSetId]
                    ,[SortOrder]
                    ,[IsEnabled]
                    ,[ParentTagSetId]
                        )
                VALUES
                        (
                         @Label,
                         @Note,
                         @Icon,
                         @ParentTagSetId,
                         @SortOrder,
                         @IsEnabled,
                         @ParentTagSetId
                        )";

            int result = -1;
            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArguments(dto);

                result = await command.ExecuteAndReturnIdentity();
            }

            return result;
        }

        public async Task DeleteTagSetAsync(int tagSetId)
        {
            var exists = await GetTagSetAsync(tagSetId);
            if (exists == null)
            {
                throw new ApiErrorException($"Cannot find a match for this '{tagSetId}' tagSetId");
            }

            string sql = @"
            Delete FROM [listing].[TagSet]
            WHERE    [tagSetId] = @tagSetId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Delete, sql))
            {
                command.AddArgument("tagSetId", tagSetId);

                await command.Execute();
            }
        }

        public async Task UpdateTagSetAsync(TagSetCDto dto)
        {
            if (dto.TagSetId == null)
            {
                throw new ApiErrorException($"Cannot find a match for this '{dto.TagSetId}' TagSetId");
            }

            var exists = await GetTagSetAsync((int)dto.TagSetId);
            if (exists == null)
            {
                throw new ApiErrorException($"Cannot find a match for this '{dto.TagSetId}' TagSetId");
            }

            string sql = @"
            UPDATE  [listing].[TagSet]
            SET
                     [Label] = @Label,
                     [Note] = @Note,
                     [Icon] = @Icon,
                     [IsEnabled] = @IsEnabled,
                     [SortOrder] = @SortOrder
            WHERE    [tagSetId] = @tagSetId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("tagSetId", dto.TagSetId);
                command.AddArgument("Label", dto.Label);
                await command.Execute();
            }
        }

        public async Task<List<TagSetCDto>> GetTagSetListAsync(bool? isEnabled, int? parentTagSetId, int? tagSetGroupId)
        {
            string sql = @$"
            SELECT 
                     [TagSetId]
                    ,[Label]
                    ,[Note]
                    ,[Icon]
                    ,[ParentTagSetId]
                    ,[SortOrder]
                    ,[IsEnabled]
            FROM     [listing].[TagSet]
            Where 1 = 1 
            {(isEnabled != null ? "AND IsEnabled = @IsEnabled" : "")}
            {(parentTagSetId != null ? "AND ParentTagSetId = @ParentTagSetId" : "")}
            {(tagSetGroupId != null ? "AND EXISTS (Select 1 FROM [Listing].[TagSetGroupTagSet] TSG WHERE TSG.[TagSetGroupId] = @TagSetGroupId AND TSG.TagSetId = [TagSet].[TagSetId]) " : "")}
            ORDER By SortOrder, Label ";

            var result = new List<TagSetCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ParentTagSetId", parentTagSetId);
                command.AddArgument("IsEnabled", isEnabled);
                command.AddArgument("TagSetGroupId", tagSetGroupId);
                result = await command.SelectMany<TagSetCDto>();
            }

            return result;
        }


    }
}
