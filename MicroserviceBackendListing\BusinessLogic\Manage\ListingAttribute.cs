﻿using MicroserviceContract.Dtos.ListingManagement;
using Microsoft.Extensions.Caching.Memory;
using Redi.Prime3.MicroService.BaseLib;
using Sql;

namespace MicroserviceBackendListing.BusinessLogic
{
    public class ListingAttribute : BusinessLogicBase
    {
        private UtilityFunctions _utils;

        public ListingAttribute(IUnitOfWork u, IMemoryCache memoryCache, UtilityFunctions utils)
        {
            _unitOfWork = u;
            _memoryCache = memoryCache;
            _utils = utils;
        }

        internal async Task<ManageListingAttributeCDto> GetAsync(long ListingAttributeId)
        {
            string sql = @"
                        SELECT 
                        [ListingAttribute].[ListingAttributeId], 
                        [ListingAttribute].[AttributeCode], 
                        [ListingAttribute].[ValueString],
                        [ListingAttribute].[ValueNumeric],
                        [ListingAttribute].[ValueDateTime],
                        [ListingAttribute].[ValueStringMax],
                        [ListingAttribute].[ValueGeography],
                        [Attribute].[Label],
                        [Attribute].[SortOrder], 
                        [Attribute].[Description],
                        [Attribute].[NumericDecimalPlaces]
                        FROM [listing].[ListingAttribute][ListingAttribute]
                        LEFT JOIN [listing].[Attribute] ON [ListingAttribute].[AttributeCode] = [Attribute].[AttributeCode]
                        WHERE [ListingAttribute].[ListingAttributeId] = @ListingAttributeId";
            var query = new ManageListingAttributeCDto();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ListingAttributeId", ListingAttributeId);
                query = await command.SelectSingle<ManageListingAttributeCDto>();
                query.ValueNumeric = Extensions.DecimalTrimPlaces(query.ValueNumeric, query.NumericDecimalPlaces);
            }
            return query;
        }

        internal async Task UpdateAsync(ManageListingAttributeCDto dto, long ListingId)
        {
            if (dto.ListingAttributeId == 0)
            {
                await CreateAsync(dto, ListingId);
            }
            else
            {
                string sql = @$"
                        UPDATE [listing].[ListingAttribute]
                        SET 
                        [ValueString] =  @ValueString,
                        [ValueNumeric] =  @ValueNumeric,
                        [ValueDateTime] =  @ValueDateTime,
                        [ValueStringMax] =  @ValueStringMax,
                        {(dto.ValueGeography?.Latitude != null ? "[ValueGeography] = geography::Point(@Gpslat, @Gpslong, 4326)" : "[ValueGeography] = NULL")}
                        WHERE [ListingAttributeId] = @ListingAttributeId";
                using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
                {
                    command.AddArguments(dto);
                    command.AddArgument("Gpslat", dto.ValueGeography?.Latitude);
                    command.AddArgument("Gpslong", dto.ValueGeography?.Longitude);
                    await command.Execute();
                }
            }

        }

        internal async Task<List<ManageListingAttributeCDto>> GetListingAttibutesbyId(long ListingId)
        {
            string sql = @"
                        SELECT 
                        [ListingAttribute].[ListingAttributeId], 
                        [ListingAttribute].[AttributeCode], 
                        [ListingAttribute].[ValueString],
                        [ListingAttribute].[ValueNumeric],
                        [ListingAttribute].[ValueDateTime],
                        [ListingAttribute].[ValueStringMax],
                        [ListingAttribute].[ValueGeography],
                        [Attribute].[Label],
                        [Attribute].[SortOrder],
                        [Attribute].[AttributeGroupCode], 
                        [Attribute].[Description],
                        [Attribute].[NumericDecimalPlaces]
                        FROM [listing].[ListingAttribute] [ListingAttribute]
                        INNER JOIN [listing].[Listing] ON [Listing].[ListingId] = [ListingAttribute].[ListingId]
                        Inner Join [listing].[ListingType] ON [ListingType].[ListingTypeId] = [Listing].[ListingTypeId]
                                    AND ([ListingType].[IsTenantBased] = 0 OR [Listing].[TenantId] = @TenantId )
                        LEFT JOIN [listing].[Attribute] ON [ListingAttribute].[AttributeCode] = [Attribute].[AttributeCode]
                        WHERE [ListingAttribute].[ListingId] = @ListingId
                        ORDER BY [Attribute].[SortOrder]";
            var result = new List<ManageListingAttributeCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ListingId", ListingId);
                command.AddArgument("TenantId", _utils.TenantId);
                result = await command.SelectMany<ManageListingAttributeCDto>();
                foreach (var rec in result)
                { 
                    rec.ValueNumeric = Extensions.DecimalTrimPlaces(rec.ValueNumeric, rec.NumericDecimalPlaces);
                }
            }
            return result;
        }

        internal async Task CreateAsync(ManageListingAttributeCDto dto, long ListingId)
        {
            if (dto.ListingAttributeId > 0)
            {
                var exists = await GetAsync(dto.ListingAttributeId);
                if (exists == null)
                {
                    await UpdateAsync(dto, ListingId);
                }
            }
            else
            {
                string sql = @$"
                        INSERT INTO [listing].[ListingAttribute]
                        ([ListingId], 
                        [AttributeCode],
                        [ValueString],
                        [ValueNumeric],
                        [ValueDateTime],
                        [ValueStringMax],
                        [ValueGeography]
                        )
                        VALUES (
                        @ListingId, 
                        @AttributeCode, 
                        @ValueString,
                        @ValueNumeric,
                        @ValueDateTime,
                        @ValueStringMax,
                        {(dto.ValueGeography?.Latitude != null ? "geography::Point(@Gpslat, @Gpslong, 4326)" : "null")}
                        )";
                using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
                {
                    command.AddArguments(dto);
                    command.AddArgument("ListingId", ListingId);
                    command.AddArgument("Gpslat", dto.ValueGeography?.Latitude);
                    command.AddArgument("Gpslong", dto.ValueGeography?.Longitude);
                    await command.Execute();
                }
            }
        }

    }

}
