﻿using MicroserviceBackendListing.BusinessLogic.Base;
using MicroserviceContract.Dtos.Booking;
using MicroserviceContract.Dtos.ListingManagement;
using Microsoft.Extensions.Caching.Memory;
using Redi.Prime3.MicroService.BaseLib;
using Sql;

namespace MicroserviceBackendListing.BusinessLogic.Booking.Management
{
    public class BookingItemManagement : BusinessLogicBase
    {
        private UtilityFunctions _utils;
        //private readonly string ENTITY_TYPE = "BookingItemAvailability";

        private readonly Func<BookingItem> _bookingItem;
        private readonly Func<BookingItemAvailability> _bookingItemAvailability;
        private readonly Func<ManageListing> _manageListing;
        private readonly Func<ListingMedia> _listingMedia;

        public BookingItemManagement(IUnitOfWork u, IMemoryCache memoryCache, UtilityFunctions utils,
            Notifications writeNotificationEvent,
            Func<BookingItem> bookingItem,
            Func<ManageListing> manageListing,
            Func<ListingMedia> listingMedia,
            Func<BookingItemAvailability> bookingItemAvailability
            )
        {
            _utils = utils;
            _unitOfWork = u;
            _memoryCache = memoryCache;
            _bookingItem = bookingItem;
            _bookingItemAvailability = bookingItemAvailability;
            _manageListing = manageListing;
            _listingMedia = listingMedia;
        }

        internal async Task<long> CreateListingAndBookingItemPair(ManageListingWithDisplayGroupedDataAndMediaCDto dto, bool? supportOfflineReferenceCreate = false)
        {
            // Step 1: Create listing
            var listingLogic = _manageListing;
            long newListingId = await listingLogic().CreateNewListing(dto, supportOfflineReferenceCreate ?? false);
            await listingLogic().CreateListingMediaAndDisplayGroupedAttributes(dto, newListingId);
            if (dto.ProfileListingMediaUrl != null)
            {
                var profileMediaListing = await _listingMedia().GetListingMediaFromUrl(dto.ProfileListingMediaUrl);
                dto.ProfileListingMediaId = profileMediaListing.ListingMediaId;
                await listingLogic().UpdateListingWithDisplayContainers(dto);
            }

            // Step 2: Create Booking Item using Listing
            RequestCDto<BookingItemRequestCDto> itemRequestCDto = new RequestCDto<BookingItemRequestCDto>()
            {
                Data = new()
                {
                    ParentEntityType = "Listing",
                    ParentEntityIntId = newListingId,
                    //TimeZoneIanaId = "Australia/Perth",
                    MinimumGapBetweenBookingsMinutes = (short)Config.BookingDefaultMinimumGapBetweenBookingsMinutes,
                    MinimumBookingTimeInMinutes = (short)Config.BookingDefaultMinimumBookingTimeInMinutes,
                    MaximumBookingTimeInMinutes = (short)Config.BookingDefaultMaximumBookingTimeInMinutes,
                    MaximumBookingIntoFutureDays = (short)Config.BookingDefaultMaximumBookingIntoFutureDays,
                    IsRecurringBookingEnabled = Config.BookingDefaultIsRecurringBookingEnabled,
                    IsEnabled = true
                },
                ClearFields = new()
            };

            var bookingItemLogic = _bookingItem;
            int bookingItemId = (int)await bookingItemLogic().CreateBookingItemAsync(itemRequestCDto);

            // Step 3: Create Booking Item Availability using Booking Item
            RequestCDto<BookingItemAvailableRequestCDto> availability = new()
            {
                Data = new()
                {
                    BookingItemId = bookingItemId,

                    IncludeMonday = false,
                    IncludeTuesday = false,
                    IncludeWednesday = false,
                    IncludeThursday = false,
                    IncludeFriday = false,
                    IncludeSaturday = false,
                    IncludeSunday = false,
                    IncludePublicHolidays = false
                },
                ClearFields = new()
            };
            await _bookingItemAvailability().CreateBookingItemAvailabilityAsync(availability);

            return newListingId;
        }

        internal async Task UpdateBookingItemAndSettingsAsync(BookingItemAndSettingsCDto dto)
        {
            await _bookingItem().UpdateBookingItemAsync(dto.BookingItem);
            await _bookingItemAvailability().UpdateBookingItemAvailabilityAsync(dto.BookingItemAvailable);
        }
    }
}
