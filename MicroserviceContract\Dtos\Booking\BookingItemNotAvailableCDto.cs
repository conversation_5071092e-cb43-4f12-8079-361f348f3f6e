﻿using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MicroserviceContract.Dtos.Booking
{
    /// <summary>
    /// Booking Item Not Available Record
    /// AvailabilityModeId = 0 (Not Available)
    /// </summary>
    [Mappable(nameof(BookingItemAvailabilityId))]
    public class BookingItemNotAvailableRequestCDto
    {
        public int? BookingItemAvailabilityId { get; set; }
        public int? BookingItemId { get; set; }
        /// <summary>
        /// Date the not available record applies from
        /// Optional
        /// </summary>
        public DateOnly? FromDate { get; set; }
        /// <summary>
        /// Date the not available record applies to
        /// Optional
        /// </summary>
        public DateOnly? ToDate { get; set; }
        /// <summary>
        /// Time the item is not available to be booked from
        /// Optional
        /// </summary>
        public TimeOnly? FromTime { get; set; }
        /// <summary>
        /// Time the item is not available to be booked to
        /// Optional
        /// </summary>
        public TimeOnly? ToTime { get; set; }
        /// <summary>
        /// When true indicates item cannot be booked on a Monday
        /// </summary>
        public bool? IncludeMonday { get; set; }
        /// <summary>
        /// When true indicates item cannot be booked on a Tuesday
        /// </summary>
        public bool? IncludeTuesday { get; set; }
        /// <summary>
        /// When true indicates item cannot be booked on a Wednesday
        /// </summary>
        public bool? IncludeWednesday { get; set; }
        /// <summary>
        /// When true indicates item cannot be booked on a Thursday
        /// </summary>
        public bool? IncludeThursday { get; set; }
        /// <summary>
        /// When true indicates item cannot be booked on a Friday
        /// </summary>
        public bool? IncludeFriday { get; set; }
        /// <summary>
        /// When true indicates item cannot be booked on a Saturday
        /// </summary>
        public bool? IncludeSaturday { get; set; }
        /// <summary>
        /// When true indicates item cannot be booked on a Sunday
        /// </summary>
        public bool? IncludeSunday { get; set; }
        /// <summary>
        /// When true indicates item cannot be booked on Public Holidays
        /// </summary>
        public bool? IncludePublicHolidays { get; set; }
    }

    /// <summary>
    /// Booking Item Not Available Record
    /// AvailabilityModeId = 0 (Not Available)
    /// </summary>
    [Mappable(nameof(BookingItemAvailabilityId))]
    public class BookingItemNotAvailableResponseCDto : DtoBase
    {
        public int? BookingItemAvailabilityId { get; set; }
        public int? BookingItemId { get; set; }
        /// <summary>
        /// Date the not available record applies from
        /// Optional
        /// </summary>
        public DateOnly? FromDate { get; set; }
        /// <summary>
        /// Date the not available record applies to
        /// Optional
        /// </summary>
        public DateOnly? ToDate { get; set; }
        /// <summary>
        /// Time the item is not available to be booked from
        /// Optional
        /// </summary>
        public TimeOnly? FromTime { get; set; }
        /// <summary>
        /// Time the item is not available to be booked to
        /// Optional
        /// </summary>
        public TimeOnly? ToTime { get; set; }
        /// <summary>
        /// When true indicates item cannot be booked on a Monday
        /// </summary>
        public bool? IncludeMonday { get; set; }
        /// <summary>
        /// When true indicates item cannot be booked on a Tuesday
        /// </summary>
        public bool? IncludeTuesday { get; set; }
        /// <summary>
        /// When true indicates item cannot be booked on a Wednesday
        /// </summary>
        public bool? IncludeWednesday { get; set; }
        /// <summary>
        /// When true indicates item cannot be booked on a Thursday
        /// </summary>
        public bool? IncludeThursday { get; set; }
        /// <summary>
        /// When true indicates item cannot be booked on a Friday
        /// </summary>
        public bool? IncludeFriday { get; set; }
        /// <summary>
        /// When true indicates item cannot be booked on a Saturday
        /// </summary>
        public bool? IncludeSaturday { get; set; }
        /// <summary>
        /// When true indicates item cannot be booked on a Sunday
        /// </summary>
        public bool? IncludeSunday { get; set; }
        /// <summary>
        /// When true indicates item cannot be booked on Public Holidays
        /// </summary>
        public bool? IncludePublicHolidays { get; set; }
    }
}
