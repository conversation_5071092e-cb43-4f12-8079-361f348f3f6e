﻿using Redi.Prime3.MicroService.BaseLib;
using Sql;

namespace MicroserviceContract.Dtos.CRM
{
    [Mappable(nameof(OrganisationId))]
    public class BaseOrganisationCDto : DtoBase
    {
        public Guid OrganisationId { get; set; }
        public string? Name { get; set; }
        public string? OrganisationReference { get; set; }
        public string? Description { get; set; }
        public string? OrganisationTypeCode { get; set; }
        public string? OrganisationTypeDescription { get; set; }
        public Boolean? IsRoot { get; set; }
    }

    public class GetOrganisationCDto : BaseOrganisationCDto {
        // Party
        public Guid PartyId { get; set; }
        public string? PartyType { get; set; }
        public string? StatusCode { get; set; }
        public Guid? AvatarImageId { get; set; }
        public string? AvatarImageUrl { get; set; }
        public Guid? UserId { get; set; }
        public List<GetListAddressCDto>? Addresses { get; set; }
        // Contact
        public string? PrimaryEmail { get; set; }
        public string? PrimaryPhone { get; set; }
        public string? PrimaryAddress { get; set; }
        public List<GetListContactMethodCDto>? ContactMethods { get; set; }
    }

    public class GetListOrganisationCDto : BaseOrganisationCDto {
        // Party
        public Guid PartyId { get; set; }
        public string? PartyType { get; set; }
        public string? StatusCode { get; set; }
        public Guid? AvatarImageId { get; set; }
        public string? AvatarImageUrl { get; set; }
        public Guid? UserId { get; set; }
        public List<GetListAddressCDto>? Addresses { get; set; }
        // Contact
        public string? PrimaryEmail { get; set; }
        public string? PrimaryPhone { get; set; }
        public string? PrimaryAddress { get; set; }
        public List<GetListContactMethodCDto>? ContactMethods { get; set; }
    }
}
