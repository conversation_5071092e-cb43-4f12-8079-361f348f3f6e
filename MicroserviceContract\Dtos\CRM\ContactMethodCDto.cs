﻿using Redi.Prime3.MicroService.BaseLib;
using Sql;

namespace MicroserviceContract.Dtos.CRM
{
    [Mappable(nameof(ContactMethodId))]
    public class BaseContactMethodCDto : DtoBase
    {
        public Guid ContactMethodId { get; set; }
        public string? ContactMethodTypeCode { get; set; }
        public string? ContactMethodSubTypeCode { get; set; }
        public string? Value { get; set; }
        public bool IsPreferredContactMethod { get; set; }
        public bool IsPrimaryForMethodType { get; set; }
        public string? Note { get; set; }
        public Guid ParentEntityId { get; set; }
        public string? ParentEntityType { get; set; }
        public int SortOrder { get; set; }
    }

    public class GetContactMethodCDto : BaseContactMethodCDto { }

    public class GetListContactMethodCDto : BaseContactMethodCDto {
        public bool ContactMethodTypeIsEnabled { get; set; }
        public bool ContactMethodSubTypeIsEnabled { get; set; }
        public string? ContactMethodTypeLabel { get; set; }
        public string? ContactMethodSubTypeLabel { get; set; }
    }
}