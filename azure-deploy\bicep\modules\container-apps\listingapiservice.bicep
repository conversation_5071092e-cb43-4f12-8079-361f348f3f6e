param location string
param enviornmentName string
param dockerUserName string
@secure()
param dockerPasswordRef string
param managedIdentityObjectId string
param systemEnvironment string
param imageTagVersion string
param dockerRepoName string
@secure()
param secretStoreName string
param hostName string
param azureStorageAccountName string
param managedIdentityClientId string

var environmentVars = systemEnvironment == 'Production' ? loadJsonContent('../../../environment/envapi.production.json', 'settings') : loadJsonContent('../../../environment/envapi.dev.json', 'settings')

resource enviornment 'Microsoft.App/managedEnvironments@2022-10-01' existing = {
  name: enviornmentName
}

resource listingapiservice 'Microsoft.App/containerApps@2022-10-01' = {
  name: dockerRepoName
  location: location
  identity: {
     type:'UserAssigned'
     userAssignedIdentities: {
      '${managedIdentityObjectId}': {}
     }
  }
  properties: {
    environmentId: enviornment.id
    template: { 
      containers: [
        {
          name: dockerRepoName
          image: 'redisoftware/${dockerRepoName}:${imageTagVersion}'
          env:concat([
            {
              name: 'ASPNETCORE_ENVIRONMENT'
              value: systemEnvironment
            }
            {
              name: 'SECRETS_STORE_NAME'
              value: secretStoreName
            }
            {
              name: 'HOSTNAME'
              value: hostName
            }
            {
              name: 'AZURE_CLIENT_ID'
              value: managedIdentityClientId
            }
            {
              name: 'AzureStorageAccountName'
              value: azureStorageAccountName
            }
          ], environmentVars)
          resources: {
            cpu: json('0.25')
            memory: '.5Gi'
          }
        }
      ]
      scale: {
        minReplicas: 0
        maxReplicas: 1
      }
    }
    configuration: {
      secrets: [
        {
          name: 'dockerpasswordref'
          value: dockerPasswordRef
        }
      ]
      registries: [
        {
          server: 'index.docker.io'
          username: dockerUserName
          passwordSecretRef: 'dockerpasswordref'
        }
      ]
      dapr: {
        enabled: true
        appId: dockerRepoName
        appPort: 8080
        appProtocol: 'http'
        enableApiLogging: true
        httpMaxRequestSize:  64
      }
      ingress: {
        external: false
        targetPort: 8080
      }
    }
  }
}
