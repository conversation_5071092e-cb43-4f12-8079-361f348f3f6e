namespace MicroserviceBackendListing.Interfaces
{

	/// <summary>
	/// Interface to upload files to remote storage e.g azure, amazon 
	/// </summary>
	public interface ICloudStorage
	{

        /// <summary>
        /// Upload a data stream to cloud server
        /// </summary>
        /// <param name="dataStream">Data to upload</param>
        /// <param name="cloudFolderName">Path of file within container</param>
        /// <param name="cloudFileName">Name of file within container</param>
        /// <param name="isPublic">Should this file be publicly available at the url or should the url be generated with GetSAS whenever access is required ?</param>
        /// <returns>Url of file</returns>
        Task<string> UploadStreamAsync(Stream dataStream, string cloudFolderName, string cloudFileName, bool isPublic);

        /// <summary>
        /// Upload file from path on server
        /// </summary>
        /// <param name="path">path to file to upload</param>
        /// <param name="cloudFolderName">Path of file within container</param>
        /// <param name="cloudFileName">Name of file within container</param>
        /// <param name="deleteOncomplete">Delete file from local after upload?</param>
        /// <param name="isPublic">Should this file be publicly available at the url or should the url be generated with GetSAS whenever access is required ?</param>
        /// <returns>Url of file</returns>
        Task<string> UploadFileAsync(string path, string cloudFolderName, string cloudFileName, bool isPublic, bool deleteOncomplete = true);

        /// <summary>
        /// Returns a Url to a cloud resource that will expire in one minute
        /// </summary>
        /// <param name="url"></param>
        /// <param name="permissions"></param>
        /// <returns></returns>
        Task<Uri> GetAuthenticatedUrl(string url, CloudItemPermission permissions);


        /// <summary>
        /// Delete item from cloud storage
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        Task Delete(string url);
	}


	[Flags]
	public enum CloudItemPermission
	{
		None = 0,
		Read = 1,
		Delete = 1 << 1,
		Write = 1 << 2,
	}
}