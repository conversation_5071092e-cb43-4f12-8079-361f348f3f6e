﻿using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MicroserviceContract.Dtos.Booking
{
    [Mappable(nameof(HoldingLocationId))]
    public class HoldingLocationRequestCDto
    {
        public int? HoldingLocationId { get; set; }
        public string? LocationParentEntityType { get; set; }
        public Guid? LocationParentEntityId { get; set; }
        public long? LocationParentEntityIntId { get; set; }
        public string? HoldingLocationName { get; set; }
    }

    [Mappable(nameof(HoldingLocationId))]
    public class HoldingLocationResponseCDto : DtoBase
    {
        public int HoldingLocationId { get; set; }
        public string? HoldingLocationName { get; set; }
        public string? LocationParentEntityType { get; set; }
        public Guid? LocationParentEntityId { get; set; }
        public long? LocationParentEntityIntId { get; set; }
    }
}
