﻿using MicroserviceBackendListing.BusinessLogic;
using Redi.Prime3.MicroService.BaseLib;
using MicroserviceDashboardContract.Dtos;
using Microsoft.AspNetCore.Mvc;
using Sql;
using MicroserviceBackendListing.Policies;
using Microsoft.AspNetCore.Authorization;

namespace MicroserviceBackendListing.Services
{
    /// <summary>
    /// Allows Creation, Fetching and Management of Dashboard Presentation.
    /// </summary>
    [Route("api/DashboardPresentation")]
    public class DashboardPresentationController : AppController
    {
        private Func<DashboardPresentation> _dashboardPresentationFactory;
        public DashboardPresentationController(IUnitOfWork unitOfWork, Func<DashboardPresentation> dashboardPresentationFactory)
        {
            _unitOfWork = unitOfWork;
            _dashboardPresentationFactory = dashboardPresentationFactory;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <param name="dashboardPresentationId"></param>
        /// <response code="200">Attribute returned, or an empty attribute if not found</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("Get")]
        [ProducesResponseType(typeof(DashboardPresentationWithConfigCDto), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetAsync(int dashboardPresentationId)
        {
            var attribute = _dashboardPresentationFactory();
            var result = await attribute.GetAsync(dashboardPresentationId);
            return Ok(result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="name"></param>
        /// <param name="standardListParameters"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetList")]
        [ResponseCache(Duration = 90)]
        [ProducesResponseType(typeof(ListResponseDto<DashboardPresentationCDto>), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetListAsync([FromQuery] StandardListParameters standardListParameters, [FromQuery] string? name = null)
        {
            var result = await _dashboardPresentationFactory().GetListAsync(standardListParameters, name);
            return Ok(result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dto"></param>
        /// <response code="200"></response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("Update")]
        [Authorize(Policy = PolicyType.UpdateDashboardPresentationPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> Update([FromBody] DashboardPresentationCDto dto)
        {
            await _dashboardPresentationFactory().UpdateAsync(dto);
            _unitOfWork.Commit();

            return Ok();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dto"></param>
        /// <response code="200"></response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("Create")]
        [Authorize(Policy = PolicyType.CreateDashboardPresentationPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> Create([FromBody] DashboardPresentationCDto dto)
        {
            var dashboardPresentationFactory = _dashboardPresentationFactory();
            var id = await dashboardPresentationFactory.CreateAsync(dto);
            _unitOfWork.Commit();

            return Ok(id);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dashboardPresentationId"></param>
        /// <response code="200"></response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("Delete")]
        [Authorize(Policy = PolicyType.DeleteDashboardPresentationPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> Delete([FromQuery] int dashboardPresentationId)
        {
            await _dashboardPresentationFactory().DeleteAsync(dashboardPresentationId);
            _unitOfWork.Commit();

            return Ok();
        }
    }
}
