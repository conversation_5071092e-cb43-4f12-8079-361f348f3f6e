﻿/************************************************************************************************
All standard Listing Tables, Indexes and Views created here.
Feature based tables are in feature specific sql files.
Updates to previously created tables go in s002 file (the original create sql in here must also be changed)
**************************************************************************************************/

IF NOT EXISTS (SELECT * FROM sys.schemas s WHERE s.name='listing')
EXEC ('CREATE SCHEMA [listing]');
GO

-- ************************************** [listing].[Visibility]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='listing' and t.name='Visibility')
BEGIN
CREATE TABLE [listing].[Visibility]
(
 [VisibilityId] tinyint NOT NULL PRIMARY KEY,
 [Label]        nvarchar(100) NOT NULL 

);


EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Controls when a listing is visible.
0 = public (anyone can see)
1 = private (only owner can see)
2 = members (only logged in users can see)', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Visibility';


-- ************************************** PostScript

INSERT INTO [Listing].[Visibility]
(VisibilityId, Label)
values
(0, 'Public'),
(1, 'Private'),
(2, 'Members')

END
GO

IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='listing' and t.name='ListingStatus')
BEGIN

    CREATE TABLE [listing].[ListingStatus]
    (
     [StatusCode] varchar(40) NOT NULL ,
     [Label]      nvarchar(100) NOT NULL ,
     [Note]       nvarchar(500) NULL,

     CONSTRAINT [PK_ListingStatus] PRIMARY KEY CLUSTERED ([StatusCode] ASC)
    );

    EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Defines the possible Tenant Status values', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'ListingStatus';

    INSERT INTO [listing].[ListingStatus]
    (StatusCode, Label, Note)
    VALUES
    ('Active','Active','Currently active'),
    ('Draft','Draft','Listing created but not yet published'),
    ('Pending','Pending','Listing published but not yet active as FromDate has not been reached'),
    ('Expired','Expired','Listing no longer active. ToDate passed, or explicitly set to expired.');
END


-- ************************************** PostScript End [listing].[Visibility]
-- ************************************** [listing].[MediaType]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='listing' and t.name='MediaType')
BEGIN
CREATE TABLE [listing].[MediaType]
(
 [MediaTypeCode] varchar(20) NOT NULL ,
 [Label]         nvarchar(200) NOT NULL ,
 [SortOrder]     smallint NOT NULL CONSTRAINT [DF_MediaType_SortOrder] DEFAULT 0 ,
 [IsEnabled]     bit NOT NULL CONSTRAINT [DF_MediaType_IsEnabled] DEFAULT 1 ,


 CONSTRAINT [PK_MediaType] PRIMARY KEY CLUSTERED ([MediaTypeCode] ASC)
);


EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Describes the type of Media. Image, Video, File, etc', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'MediaType';


-- ************************************** PostScript

INSERT INTO [listing].[MediaType] (MediaTypeCode, Label, SortOrder, IsEnabled)
VALUES
('Image','Image',1,1),
('Video','Video',2,1),
('Pdf','Pdf',3,1),
('Youtube','Youtube',4,1)

END
GO




-- ************************************** PostScript End [listing].[MediaType]
-- ************************************** [listing].[MediaCategory]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='listing' and t.name='MediaCategory')
BEGIN
    CREATE TABLE [listing].[MediaCategory]
    (
     [MediaCategoryCode] varchar(20) NOT NULL ,
     [Label]             nvarchar(200) NOT NULL ,
     [SortOrder]         smallint NOT NULL CONSTRAINT [DF_MediaCategory_SortOrder] DEFAULT 0 ,
     [IsEnabled]         bit NOT NULL CONSTRAINT [DF_MediaCategory_IsEnabled] DEFAULT 1 ,


     CONSTRAINT [PK_MediaCategory] PRIMARY KEY CLUSTERED ([MediaCategoryCode] ASC)
    );

    EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Identifies a media category. This could be a  pixel resolution, public, private, restricted, etc', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'MediaCategory';

    INSERT INTO [listing].[MediaCategory]
        ([MediaCategoryCode], [Label], [SortOrder], [IsEnabled])
        VALUES
        ('Listings','Listings',1,1);

END
GO




-- ************************************** [listing].[DisplayContainer]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='listing' and t.name='DisplayContainer')
BEGIN
CREATE TABLE [listing].[DisplayContainer]
(
 [DisplayContainerCode] varchar(60) NOT NULL PRIMARY KEY,
 [Title]                nvarchar(400) NOT NULL ,
 [Description]          nvarchar(1000) NULL ,
 [Enabled]              bit NOT NULL CONSTRAINT [DF_AttributeDisplaySet_Enabled] DEFAULT 1 ,
 [IsShowTitle]          bit NOT NULL CONSTRAINT [DF_AttributeDisplaySet_IsShowTitle] DEFAULT 1 ,
 [Icon]                 varchar(400) NULL ,
 [HelpText]             nvarchar(max) NULL 

);


EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'A display container supports defining which attributes are to be included in a filter or on a ui container. For example we could create a ListSet which has just 2 attributes that need to be included in display of a list.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'DisplayContainer';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Title to be displayed if supported on the UI.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'DisplayContainer', @level2type=N'COLUMN', @level2name=N'Title';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Free text field to describe the purpose of the Display Set. Used for documentation purposes only.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'DisplayContainer', @level2type=N'COLUMN', @level2name=N'Description';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Tells the Ui whether the set title should be displayed.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'DisplayContainer', @level2type=N'COLUMN', @level2name=N'IsShowTitle';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Specifies the icon to be displayed prior to the title.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'DisplayContainer', @level2type=N'COLUMN', @level2name=N'Icon';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Optional help text that can be displayed to the user.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'DisplayContainer', @level2type=N'COLUMN', @level2name=N'HelpText';

END
GO



-- ************************************** [listing].[AttributeValueType]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='listing' and t.name='AttributeValueType')
BEGIN
CREATE TABLE [listing].[AttributeValueType]
(
 [AttributeValueTypeCode] varchar(40) NOT NULL ,
 [Label]                  varchar(100) NOT NULL ,
 [IsEnabled]              bit NOT NULL CONSTRAINT [DF_AttributeValueType_IsEnabled] DEFAULT 1 ,
 [SortOrder]              smallint NOT NULL CONSTRAINT [DF_AttributeValueType_SortOrder] DEFAULT 0 

);

CREATE UNIQUE NONCLUSTERED INDEX [PK_AttributeValueType] ON [listing].[AttributeValueType] 
 (
  [AttributeValueTypeCode] ASC
 )


CREATE CLUSTERED INDEX [IX_AttributeValueType_SortOrderLabelIsEnabled] ON [listing].[AttributeValueType] 
 (
  [SortOrder] ASC, 
  [Label] ASC, 
  [IsEnabled] DESC
 )


EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Attribute Value Type is used to define  how a value is stored in the ListingAttribute table.
ValueString, ValueStringMax, ValueDateTime, ValueNumeric, ValueGeography', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'AttributeValueType';

-- ************************************** PostScript

Insert INTO listing.AttributeValueType
(AttributeValueTypeCode, Label)
Values 
('ValueString','String (Small <500 characters)'),
('ValueStringMax','String (Large)'),
('ValueDateTime','DateTime'),
('ValueNumeric','Numeric (integer, decimal, boolean)'),
('ValueGeography','Geography(Lat/Long)')

END
GO



-- ************************************** PostScript End [listing].[AttributeValueType]
-- ************************************** [listing].[AttributeInputType]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='listing' and t.name='AttributeInputType')
BEGIN
CREATE TABLE [listing].[AttributeInputType]
(
 [InputTypeCode] varchar(40) NOT NULL ,
 [Label]         varchar(100) NOT NULL ,
 [IsEnabled]     bit NOT NULL CONSTRAINT [DF_AttributeInputType_IsEnabled] DEFAULT 1 ,
 [SortOrder]     smallint NOT NULL CONSTRAINT [DF_AttributeInputType_SortOrder] DEFAULT 0 ,


 CONSTRAINT [PK_AttributeInputType] PRIMARY KEY CLUSTERED ([InputTypeCode] ASC)
);

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The type of input field to be displayed in the UI for an attribute. Examples: Dropdown, Text, Number, Decimal, Date, Time, Date Range, Number Range, Distance, Etc.
The input can be used for entry/update of an attribute as well as for search filters.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'AttributeInputType';


-- ************************************** PostScript

Insert INTO listing.AttributeInputType (InputTypeCode, Label)
Values 
('Dropdown','Dropdown'),
('Text','Text'),
('TextArea','TextArea'),
('Checkbox','Checkbox'),
('Integer','Integer'),
('Decimal','Decimal'),
('RadioButton','Radio Button'),
('StarRating','Star Rating'),
('Address','Address'),
('Date','Date'),
('Time','Time'),
('Map','Map'),
('DateRange','Date Range'),
('NumericRange','Numeric Range'),
('PriceRange','Price Range'),
('Distance','Distance'),
('Suburb','Suburb (Google)'),
('MultiSelect','Multi Select'),
('DateTime', 'Date Time')

END
GO

-- ************************************** PostScript End [listing].[AttributeInputType]
-- ************************************** [listing].[AttributeGroup]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='listing' and t.name='AttributeGroup')
BEGIN
CREATE TABLE [listing].[AttributeGroup]
(
 [AttributeGroupCode] varchar(40) NOT NULL ,
 [Label]              nvarchar(100) NOT NULL ,
 [Description]        nvarchar(max) NULL ,
 [Enabled]            bit NOT NULL CONSTRAINT [DF_AttributeGroup_Enabled] DEFAULT 1 ,
 [SortOrder]          smallint NOT NULL CONSTRAINT [DF_AttributeGroup_SortOrder] DEFAULT 1 ,


 CONSTRAINT [PK_AttributeGroup] PRIMARY KEY CLUSTERED ([AttributeGroupCode] ASC)
);

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Attribute grouping can be used to group attributes for easier management and understanding.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'AttributeGroup';


-- ************************************** PostScript

Insert INTO [Listing].AttributeGroup
(AttributeGroupCode, Label, Enabled, SortOrder)
Values
('Default','Default',1,1)

END
GO



-- ************************************** PostScript End [listing].[AttributeGroup]
-- ************************************** [listing].[SortOptions]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='listing' and t.name='SortOptions')
BEGIN
CREATE TABLE [listing].[SortOptions]
(
 [SortOptionId]         int NOT NULL ,
 [Label]                nvarchar(100) NOT NULL ,
 [SqlSortStmt]          nvarchar(500) NOT NULL ,
 [SortOrder]            int NOT NULL CONSTRAINT [DF_SortOptions_SortOrder] DEFAULT 1 ,
 [IsEnabled]            bit NOT NULL CONSTRAINT [DF_SortOptions_IsEnabled] DEFAULT 1 ,
 [DisplayContainerCode] varchar(60) NULL 

 CONSTRAINT [PK_SortOptions] PRIMARY KEY CLUSTERED ([SortOptionId] ASC),
);


CREATE NONCLUSTERED INDEX [FK_SortOptions_DisplayContainerCode] ON [listing].[SortOptions] 
 (
  [DisplayContainerCode] ASC
 )


EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Defines the sort options available for a listing.
These are provided to a user in a dropdown.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'SortOptions';


EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'This is the sql that will be added to perform this sort.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'SortOptions', @level2type=N'COLUMN', @level2name=N'SqlSortStmt';


-- ************************************** PostScript

INSERT INTO [Listing].SortOptions 
(
    [SortOptionId], [Label], [SqlSortStmt], [SortOrder], [IsEnabled], [DisplayContainerCode]
)
Values
(1,'Newest to oldest','[Listing].[CreatedOn] DESC',1,1,NULL),
(2,'Oldest to newest','[Listing].[CreatedOn] ASC',1,1,NULL),
(3,'Nearest','@fromLocation.STDistance([Listing].[GpsLocation]) ASC',1,0,NULL),
(4,'Furthest','@fromLocation.STDistance([Listing].[GpsLocation]) DESC',1,0,NULL);

END
GO

-- ************************************** PostScript End [listing].[SortOptions]
-- ************************************** [listing].[Attribute]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='listing' and t.name='Attribute')
BEGIN
CREATE TABLE [listing].[Attribute]
(
 [AttributeCode]          varchar(80) NOT NULL ,
 [Label]                  nvarchar(100) NOT NULL ,
 [AttributeGroupCode]     varchar(40) NOT NULL CONSTRAINT [DF_Attribute_AttributeGroupCode] DEFAULT 'Default' ,
 [AttributeValueTypeCode] varchar(40) NOT NULL ,
 [Description]            nvarchar(max) NOT NULL ,
 [SortOrder]              smallint NOT NULL ,
 [IsEnabled]              bit NOT NULL CONSTRAINT [DF_Attribute_IsEnabled] DEFAULT 1 ,
 [IsManyAllowed]          bit NOT NULL CONSTRAINT [DF_Attribute_IsManyAllowed] DEFAULT 0 ,
 [Icon]                   varchar(400) NULL ,
 [ReadAccessClaim]        varchar(200) NULL ,
 [WriteAccessClaim]       varchar(200) NULL ,
 [InputTypeCode]          varchar(40) NULL ,
 [NumericDecimalPlaces]     tinyint NULL,
 [FilterGetModeCode] varchar(40) NOT NULL CONSTRAINT [DF_Attribute_FilterGetModeCode] DEFAULT 'None',
 [IsRequired] bit NULL,
 [NumericMinValue] decimal NULL,
 [NumericMaxValue] decimal NULL,
 [TextMinCharacters] int NULL,
 [TextMaxCharacters] int NULL,
 [MinDateDaysFromToday] int NULL,
 [MaxDateDaysFromToday] int NULL,
 [MinSelectionsRequired] int NULL,
 [MaxSelectionsAllowed] int NULL,
 [MaxStars] int NULL,
 [HelpText] nvarchar(1000) NULL,


 CONSTRAINT [PK_Attribute] PRIMARY KEY CLUSTERED ([AttributeCode] ASC),
 CONSTRAINT [FK_Attribute_AttributeInputType] FOREIGN KEY ([InputTypeCode])  REFERENCES [listing].[AttributeInputType]([InputTypeCode]),
 CONSTRAINT [FK_Attribute_AttributeValueType] FOREIGN KEY ([AttributeValueTypeCode])  REFERENCES [listing].[AttributeValueType]([AttributeValueTypeCode]),
 CONSTRAINT [FK_Attribute_AttributeGroup] FOREIGN KEY ([AttributeGroupCode])  REFERENCES [listing].[AttributeGroup]([AttributeGroupCode])
);

CREATE NONCLUSTERED INDEX [FK_Attribute_InputTypeCode] ON [listing].[Attribute] 
 (
  [InputTypeCode] ASC
 )


CREATE NONCLUSTERED INDEX [FK_Attribute_AttributeValueTypeCode] ON [listing].[Attribute] 
 (
  [AttributeValueTypeCode] ASC
 )


CREATE NONCLUSTERED INDEX [FK_Attribute_AttributeGroupCode] ON [listing].[Attribute] 
 (
  [AttributeGroupCode] ASC
 )


EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'A listing attribute defines a piece of data for a listing. Examples: Model, Serial, Age, Colour, Category, Tag.
The Attribute defines how this is entered (text, checkbox, number, dropdown, etc) and how it is stored (numeric, string, date, etc).', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Attribute';


EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The Attribute Group this Attribute belongs to. For management purposes. Display Sets are used for controlling display of attributes.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Attribute', @level2type=N'COLUMN', @level2name=N'AttributeGroupCode';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'When true this indicates that many values can be recorded for this attribute for a single listing.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Attribute', @level2type=N'COLUMN', @level2name=N'IsManyAllowed';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Specifies the icon to be displayed on the UI.
Optional.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Attribute', @level2type=N'COLUMN', @level2name=N'Icon';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Specifies the user access claim a user must have to view this attribute. 
If null all users can view.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Attribute', @level2type=N'COLUMN', @level2name=N'ReadAccessClaim';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Specifies the user access claim a user must have to edit/update this attribute. 
If null all users can edit.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Attribute', @level2type=N'COLUMN', @level2name=N'WriteAccessClaim';

END;

-- ************************************** [listing].[DisplayContainerAttribute]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='listing' and t.name='DisplayContainerAttribute')
BEGIN
CREATE TABLE [listing].[DisplayContainerAttribute]
(
 [DisplayContainerAttributeId] int IDENTITY (1, 1) NOT NULL PRIMARY KEY,
 [AttributeCode]               varchar(80) NOT NULL ,
 [SortOrder]                   smallint NOT NULL CONSTRAINT [DF_DisplaySetAttribute_SortOrder] DEFAULT 0 ,
 [IsReadOnly]                  bit NOT NULL CONSTRAINT [DF_DisplaySetAttribute_IsReadOnly] DEFAULT 0 ,
 [CreatedOn]                   datetimeoffset(7) NOT NULL , -- From template: "CreatedAndModified"
 [CreatedByName]               nvarchar(60) NULL , -- From template: "CreatedAndModified"
 [ModifiedOn]                  datetimeoffset(7) NULL , -- From template: "CreatedAndModified"
 [ModifiedByName]              nvarchar(60) NULL , -- From template: "CreatedAndModified"
 [Deleted]                     bit NOT NULL , -- From template: "CreatedAndModified"
 [InputTypeCode]               varchar(40) NULL ,
 [DisplayContainerCode]        varchar(60) NOT NULL,
 [DefaultTextValue]            nvarchar(100) NULL,
 [DefaultNumericValue]         decimal NULL,
 [DefaultDateAddDays]          int NULL,
 [DefaultDateAddMonths]        int NULL,
 [DefaultTimeAddMinutes]       int NULL

);


CREATE NONCLUSTERED INDEX [FK_DisplayContainerAttribute_DisplayContainerCode] ON [listing].[DisplayContainerAttribute] 
 (
  [DisplayContainerCode] ASC
 )


CREATE NONCLUSTERED INDEX [FK_DisplayContainerAttribute_AttributeCode] ON [listing].[DisplayContainerAttribute] 
 (
  [AttributeCode] ASC
 )


CREATE NONCLUSTERED INDEX [FK_DisplayContainerAttribute_InputTypeCode] ON [listing].[DisplayContainerAttribute] 
 (
  [InputTypeCode] ASC
 )


EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Defines the attributes and their sort order to include in a display set', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'DisplayContainerAttribute';


EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'When supported by the frontend indicates if the field can be edited/modified by the user.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'DisplayContainerAttribute', @level2type=N'COLUMN', @level2name=N'IsReadOnly';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Overrides the attributes default Input Type.
This can be useful for filters that require a different completely different behaviour to the entry of the attribute data.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'DisplayContainerAttribute', @level2type=N'COLUMN', @level2name=N'InputTypeCode';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The display set the attribute is part of.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'DisplayContainerAttribute', @level2type=N'COLUMN', @level2name=N'DisplayContainerCode';

END
GO


-- ************************************** [listing].[AttributeDropdownValue]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='listing' and t.name='AttributeDropdownValue')
BEGIN
CREATE TABLE [listing].[AttributeDropdownValue]
(
 [AttributeDropdownValueId] bigint NOT NULL ,
 [AttributeCode]            varchar(80) NOT NULL ,
 [DisplayValue]             nvarchar(500) NULL ,
 [Value]                    nvarchar(300) NOT NULL ,
 [SortOder]                 smallint NOT NULL ,
 [IsEnabled]                bit NOT NULL CONSTRAINT [DF_AttributeDropdownValue_IsEnabled] DEFAULT 1 ,


 CONSTRAINT [PK_AttributeDropdownValue] PRIMARY KEY NONCLUSTERED ([AttributeDropdownValueId] ASC),
 CONSTRAINT [FK_AttributeDropdownValue_Attribute] FOREIGN KEY ([AttributeCode])  REFERENCES [listing].[Attribute]([AttributeCode])
);


CREATE CLUSTERED INDEX [IX_AttributeDropdownValue_ByAttributeCodeSortOrder] ON [listing].[AttributeDropdownValue] 
 (
  [AttributeCode] ASC, 
  [SortOder] ASC
 )
END
GO

-- ************************************** [listing].[ListingType]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='listing' and t.name='ListingType')
BEGIN
CREATE TABLE [listing].[ListingType]
(
 [ListingTypeId] tinyint NOT NULL ,
 [Label]         nvarchar(200) NOT NULL ,
 [Description]   nvarchar(2000) NULL ,
 [IsTenantBased] bit NOT NULL CONSTRAINT [DF_ListingType_IsTenantBased] DEFAULT 0,
 [NonOwnerEditClaimId]     uniqueidentifier NULL,

 CONSTRAINT [PK_ListingType] PRIMARY KEY CLUSTERED ([ListingTypeId] ASC)
);

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Listing Type enables listings to be used for different purposes. 
type 0 is default. 
type 1 is settings,
type 2 is support tickets
type 3 is tasks', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'ListingType';

Insert Into [listing].[Listingtype]
(ListingtypeId, Label, [IsTenantBased], [NonOwnerEditClaimId])
Values
(0,'Default', 0, '99bb8065-da85-42b6-9f39-ee07fdca291b'), -- Anyone with Edit Manage Listing Claim can edit the listing
(1,'Settings', 0, NULL),
(2,'Support Tickets', 0, NULL),
(3,'Tasks', 1, NULL),
(4,'Dashboard', 1, NULL),
(5,'Folder', 1, NULL);

END
GO

IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='listing' and t.name='Listing')
BEGIN
SET ANSI_NULLS ON
SET ARITHABORT ON
SET CONCAT_NULL_YIELDS_NULL ON
SET QUOTED_IDENTIFIER ON
SET ANSI_PADDING ON
SET ANSI_WARNINGS ON
SET NUMERIC_ROUNDABORT OFF
CREATE TABLE [listing].[Listing]
(
 [ListingId]             bigint IDENTITY (1, 1) NOT NULL ,
 [Subject]               nvarchar(2000) NOT NULL ,
 [StatusCode]            varchar(40) NOT NULL ,
 [VisibilityId]          tinyint NOT NULL CONSTRAINT [DF_Listing_VisibilityId] DEFAULT 0 ,
 [ProfileListingMediaId] bigint NULL ,
 [ParentEntityId]        uniqueidentifier NULL , -- From template: "ParentEntity"
 [ParentEntityIntId]     bigint NULL,
 [ParentEntityType]      varchar(100) NULL , -- From template: "ParentEntity"
 [CreatedOn]             datetimeoffset(7) NOT NULL , -- From template: "CreatedAndModified"
 [CreatedByName]         nvarchar(60) NULL , -- From template: "CreatedAndModified"
 [ModifiedOn]            datetimeoffset(7) NULL , -- From template: "CreatedAndModified"
 [ModifiedByName]        nvarchar(60) NULL , -- From template: "CreatedAndModified"
 [Deleted]               bit NOT NULL , -- From template: "CreatedAndModified"
 [FromDate]              datetimeoffset(7) NOT NULL , -- From template: "FromandToDate"
 [ToDate]                datetimeoffset(7) NULL , -- From template: "FromandToDate"
 [Description]           nvarchar(max) NULL ,
 [GpsLocation]           geography NULL ,
 [ParentEntityId2]       uniqueidentifier NULL ,
 [ParentEntityType2]     varchar(100) NULL ,
 [Icon]                  varchar(400) NULL ,
 [ReferenceNo]           nvarchar(20) NOT NULL,
 [TenantId]              int NULL,
 [ListingTypeId]         tinyint NOT NULL CONSTRAINT [DF_Listing_ListingTypeId] DEFAULT 0,
 [ParentListingId]       bigint NULL,
 [SortOrder]             int NULL,
 [VisibilityClaimId]     uniqueidentifier NULL,
    
 CONSTRAINT [PK_Listing] PRIMARY KEY CLUSTERED ([ListingId] ASC),
 CONSTRAINT [FK_Listing_ListingType] FOREIGN KEY ([ListingTypeId])  REFERENCES [listing].[ListingType]([ListingTypeId]),
 CONSTRAINT [FK_Listing_ParentListingId] FOREIGN KEY ([ParentListingId])  REFERENCES [listing].[Listing]([ListingId]),
 CONSTRAINT [FK_Listing_ListingStatus] FOREIGN KEY ([StatusCode])  REFERENCES [listing].[ListingStatus]([StatusCode])
);


CREATE NONCLUSTERED INDEX [IX_Listing_Fil_VisibilityIdFromDate] ON [listing].[Listing] 
 (
  [VisibilityId] ASC,
  [FromDate] DESC
 )
 WHERE StatusCode = 'Active' AND Deleted = 0


CREATE NONCLUSTERED INDEX [IX_Listing_Fil_StatusActive_] ON [listing].[Listing] 
 (
  [Subject] ASC
 )
 WHERE StatusCode = 'Active' AND Deleted = 0

CREATE SPATIAL INDEX [IX_Listing_GpsLocation] ON [listing].[Listing] 
 (
  [GpsLocation]
 )
 USING GEOGRAPHY_GRID

CREATE NONCLUSTERED INDEX [IX_Listing_ParentEntityIdTypeListingTypeIdToDateFromDateStatus] ON [listing].[Listing] 
 (
  [ParentEntityId] ASC, 
  [ParentEntityType] ASC, 
  [ListingTypeId] ASC, 
  [ToDate] ASC, 
  [FromDate] ASC, 
  [StatusCode] ASC
 )

CREATE NONCLUSTERED INDEX [IX_Listing_ReferenceNoStatusCode] ON [listing].[Listing] 
 (
  [ReferenceNo] ASC,
  [StatusCode] ASC
 )
 INCLUDE (
  [ListingId]
 )

 CREATE NONCLUSTERED INDEX [IX_Listing_TenantDeletedTypeStatusVisSubject] ON [listing].[Listing] 
 (
  [TenantId],
  [Deleted],
  [ListingTypeId],
  [StatusCode],
  [VisibilityId],
  [Subject] ASC
 )
 INCLUDE (
  [ListingId],
  [ReferenceNo]
 );

 CREATE NONCLUSTERED INDEX [IX_Listing_ParentListingIdListingTypeIdSortOrder] ON [listing].[Listing] 
     (
      [ParentListingId] ASC, 
      [ListingTypeId] ASC, 
      [SortOrder] ASC
     );
 CREATE NONCLUSTERED INDEX [IX_Listing_ParentEntityIntIdTypeListingTypeToDateFromDateStatus] ON [listing].[Listing] 
     (
      [ParentEntityIntId] ASC, 
      [ParentEntityType] ASC, 
      [ListingTypeId] ASC, 
      [ToDate] ASC, 
      [FromDate] ASC, 
      [StatusCode] ASC
     );

CREATE NONCLUSTERED INDEX [IX_Listing_FI_StatusActive_Type0] ON [listing].[Listing] 
     (
      [Subject] ASC
     )
     INCLUDE (
      [ListingId]
     )
     WHERE StatusCode = 'Active' AND Deleted = 0 AND ListingTypeId = 0;

    CREATE NONCLUSTERED INDEX [IX_Listing_FI_StatusActive_Type1] ON [listing].[Listing] 
     (
      [Subject] ASC
     )
     INCLUDE (
      [ListingId]
     )
     WHERE StatusCode = 'Active' AND Deleted = 0 AND ListingTypeId = 1;

    CREATE NONCLUSTERED INDEX [IX_Listing_FI_StatusActive_Type2] ON [listing].[Listing] 
     (
      [Subject] ASC
     )
     INCLUDE (
      [ListingId]
     )
     WHERE StatusCode = 'Active' AND Deleted = 0 AND ListingTypeId = 2;

    CREATE NONCLUSTERED INDEX [IX_Listing_FI_StatusActive_Type3] ON [listing].[Listing] 
     (
      [Subject] ASC
     )
     INCLUDE (
      [ListingId]
     )
     WHERE StatusCode = 'Active' AND Deleted = 0 AND ListingTypeId = 3;

    CREATE NONCLUSTERED INDEX [IX_Listing_FI_StatusActive_type4] ON [listing].[Listing] 
     (
      [Subject] ASC
     )
     INCLUDE (
      [ListingId]
     )
     WHERE StatusCode = 'Active' AND Deleted = 0 AND ListingTypeId = 4;

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'A listing can represent an items, services, businesses, events, etc.
An item could be listed for sale, or a business could be displayed on a business directory', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Listing';

EXEC sp_addextendedproperty @name = N'MS_Description',
@value = N'The listing reference that is presented to public and customers. It allows the system to hide the internal database ListingId from public facing use.',
@level0type = N'SCHEMA', @level0name = N'listing',
@level1type = N'TABLE', @level1name = N'Listing',
@level2type=N'COLUMN', @level2name=N'ReferenceNo';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Identifies the type of listing. Default, Settings, Task, etc. Supports Listings been used for different purposes.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Listing', @level2type=N'COLUMN', @level2name=N'ListingTypeId';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Default status is Active and represents a listing that is displayed. Various other statuses will control the lifecycle of a listing
Active - displayed
Draft - listing been created
Pending - FromDate not reached
Expired - Past ToDate', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Listing', @level2type=N'COLUMN', @level2name=N'StatusCode';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Indicates who can view the listing.
0 - public, everyone (default)
1 - private, only the owner
2 - members', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Listing', @level2type=N'COLUMN', @level2name=N'VisibilityId';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The main profile image/video for this listing.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Listing', @level2type=N'COLUMN', @level2name=N'ProfileListingMediaId';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The id of the parent entity that owns this record', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Listing', @level2type=N'COLUMN', @level2name=N'ParentEntityId';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Identifies the type of parent entity (usually a table name). Such as Party, Account, etc', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Listing', @level2type=N'COLUMN', @level2name=N'ParentEntityType';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'record is active from this date, inclusive of this date', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Listing', @level2type=N'COLUMN', @level2name=N'FromDate';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Record is active up until and including this date.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Listing', @level2type=N'COLUMN', @level2name=N'ToDate';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Records the Point(Lat, Long) which is the physical location of the item associated with a listing.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Listing', @level2type=N'COLUMN', @level2name=N'GpsLocation';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Additional parent entity related to this listing.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Listing', @level2type=N'COLUMN', @level2name=N'ParentEntityId2';

END

-- ************************************** [listing].[ListingMedia]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='listing' and t.name='ListingMedia')
BEGIN
CREATE TABLE [listing].[ListingMedia]
(
 [ListingMediaId]    bigint IDENTITY (1, 1) NOT NULL ,
 [Title]             nvarchar(200) NULL ,
 [MediaUrl]          varchar(2000) NOT NULL ,
 [ListingId]         bigint NOT NULL ,
 [MediaTypeCode]     varchar(20) NOT NULL ,
 [MediaCategoryCode] varchar(20) NULL ,
 [SortOrder]         int NOT NULL ,
 [CreatedOn]         datetimeoffset(7) NOT NULL , -- From template: "CreatedAndModified"
 [CreatedByName]     nvarchar(60) NULL , -- From template: "CreatedAndModified"
 [ModifiedOn]        datetimeoffset(7) NULL , -- From template: "CreatedAndModified"
 [ModifiedByName]    nvarchar(60) NULL , -- From template: "CreatedAndModified"
 [Deleted]           bit NOT NULL  -- From template: "CreatedAndModified"


 CONSTRAINT [PK_ListingMedia] PRIMARY KEY NONCLUSTERED ([ListingMediaId] ASC),
 CONSTRAINT [FK_ListingMedia_MediaType] FOREIGN KEY ([MediaTypeCode])  REFERENCES [listing].[MediaType]([MediaTypeCode]),
 CONSTRAINT [FK_ListingMedia_MediaCategory] FOREIGN KEY ([MediaCategoryCode])  REFERENCES [listing].[MediaCategory]([MediaCategoryCode]),
 CONSTRAINT [FK_ListingMedia_Listing] FOREIGN KEY ([ListingId])  REFERENCES [listing].[Listing]([ListingId])
);

CREATE NONCLUSTERED INDEX [FK_ListingMedia_2] ON [listing].[ListingMedia] 
 (
  [MediaTypeCode] ASC
 )


CREATE NONCLUSTERED INDEX [FK_ListingMedia_3] ON [listing].[ListingMedia] 
 (
  [MediaCategoryCode] ASC
 )


CREATE CLUSTERED INDEX [IX_ListingMedia_ListingIdSortOrder] ON [listing].[ListingMedia] 
 (
  [ListingId] ASC, 
  [SortOrder] ASC
 )

 CREATE NONCLUSTERED INDEX [IX_ListingMedia_MediaUrlListingId] ON [listing].[ListingMedia] 
 (
  [MediaUrl] ASC,
  [ListingId] ASC
 )


EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Identifies the type of media, Image, Video, Pdf, etc', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'ListingMedia', @level2type=N'COLUMN', @level2name=N'MediaTypeCode';

END
GO

-- ************************************** [listing].[ListingAudit]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='listing' and t.name='ListingAudit')
BEGIN
CREATE TABLE [listing].[ListingAudit]
(
 [ListingAuditId] bigint IDENTITY (1, 1) NOT NULL ,
 [Description]    nvarchar(max) NOT NULL ,
 [CreatedOn]      datetimeoffset(7) NOT NULL ,
 [CreatedByName]  nvarchar(60) NULL ,
 [ListingId]      bigint NOT NULL ,


 CONSTRAINT [PK_ListingAudit] PRIMARY KEY NONCLUSTERED ([ListingAuditId] ASC),
 CONSTRAINT [FK_ListingAudit_Listing] FOREIGN KEY ([ListingId])  REFERENCES [listing].[Listing]([ListingId])
);

CREATE CLUSTERED INDEX [IX_ListingAudit_ListingIdCreatedOn] ON [listing].[ListingAudit] 
 (
  [ListingId] ASC, 
  [CreatedOn] DESC
 )



EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'A Listing Audit record is created everytime a listing is modified. It tracks all changes from creation through to adding attributes, change of status and deletion.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'ListingAudit';


EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Describes the Listing change', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'ListingAudit', @level2type=N'COLUMN', @level2name=N'Description';

END
GO

-- ************************************** [listing].[ListingAttribute]

IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='listing' and t.name='ListingAttribute')
BEGIN
SET ANSI_NULLS ON
SET ARITHABORT ON
SET CONCAT_NULL_YIELDS_NULL ON
SET QUOTED_IDENTIFIER ON
SET ANSI_PADDING ON
SET ANSI_WARNINGS ON
SET NUMERIC_ROUNDABORT OFF
CREATE TABLE [listing].[ListingAttribute]
(
 [ListingAttributeId] bigint IDENTITY (1, 1) NOT NULL ,
 [AttributeCode]      varchar(80) NOT NULL ,
 [ValueString]        nvarchar(500) NULL ,
 [ValueNumeric]       decimal(18,7) NULL ,
 [ValueDateTime]      datetimeoffset(7) NULL ,
 [ValueStringMax]     nvarchar(max) NULL ,
 [ValueGeography]     geography NULL ,
 [ListingId]          bigint NOT NULL ,


 CONSTRAINT [PK_ListingAttribute] PRIMARY KEY CLUSTERED ([ListingAttributeId] ASC),
 CONSTRAINT [FK_ListingAttribute_Listing] FOREIGN KEY ([ListingId])  REFERENCES [listing].[Listing]([ListingId]),
 CONSTRAINT [FK_ListingAttribute_Attribute] FOREIGN KEY ([AttributeCode])  REFERENCES [listing].[Attribute]([AttributeCode])
);

 CREATE NONCLUSTERED INDEX [IX_ListingAttribute_ListingAttributeCode] ON [listing].[ListingAttribute] 
 (
  [ListingId] ASC, 
  [AttributeCode] ASC
 ) INCLUDE ([ListingAttributeId])


CREATE NONCLUSTERED INDEX [IX_ListingAttribute_Filt_VDateTime_3] ON [listing].[ListingAttribute] 
 (
  [AttributeCode] ASC, 
  [ValueDateTime] ASC, 
  [ListingId] ASC
 )
 WHERE ValueDateTime IS NOT NULL


CREATE NONCLUSTERED INDEX [IX_ListingAttribute_Filt_VNumeric_2] ON [listing].[ListingAttribute] 
 (
  [AttributeCode] ASC, 
  [ValueNumeric] ASC, 
  [ListingId] ASC
 )
 WHERE ValueNumeric IS NOT NULL


CREATE NONCLUSTERED INDEX [IX_ListingAttribute_Filt_VString_1] ON [listing].[ListingAttribute] 
 (
  [AttributeCode] ASC, 
  [ValueString] ASC, 
  [ListingId] ASC
 )
 WHERE ValueString IS NOT NULL


EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Listing Attributes store information related to a listing, such as model, serial, size, price etc.
The attribute can be used in searching or can be hidden and used in processing.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'ListingAttribute';


EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Storage of a lat/long or polygon.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'ListingAttribute', @level2type=N'COLUMN', @level2name=N'ValueGeography';

END
GO

IF INDEXPROPERTY ( object_ID('listing.ListingAttribute'), 'IX_ListingAttribute_Filt_VGeography_4' , 'IndexID' ) IS NULL
BEGIN
CREATE SPATIAL INDEX [IX_ListingAttribute_Filt_VGeography_4] ON [listing].[ListingAttribute] 
 (
  [ValueGeography]
 )
END
GO


IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='listing' and t.name='FavouriteSet')
BEGIN
CREATE TABLE [listing].[FavouriteSet]
(
 [FavouriteSetId]   int IDENTITY (1, 1) NOT NULL PRIMARY KEY ,
 [Name]             nvarchar(250) NOT NULL ,
 [ParentEntityId]   uniqueidentifier NOT NULL , 
 [ParentEntityType] varchar(100) NOT NULL , 
 [SortOrder]        int NOT NULL CONSTRAINT [DF_FavouriteSet_SortOrder] DEFAULT 1 ,
 [CreatedOn]        datetimeoffset(7) NOT NULL , 
 [CreatedByName]    nvarchar(60) NULL , 
 [ModifiedOn]       datetimeoffset(7) NULL , 
 [ModifiedByName]   nvarchar(60) NULL , 
 [Deleted]          bit NOT NULL  

);

CREATE NONCLUSTERED INDEX [IX_FavouriteSet_ParentEntityIdTypeDelFavSet] ON [listing].[FavouriteSet] 
 (
  [ParentEntityId] ASC, 
  [ParentEntityType] ASC, 
  [Deleted] ASC, 
  [FavouriteSetId] ASC
 )
 INCLUDE (
  [Name], 
  [SortOrder]
 )

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'A favourite set allows a user/customer to group there favourites into different sets.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'FavouriteSet';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Internal Id used to identify a set. It must always be used in combination with the ParentEntityId', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'FavouriteSet', @level2type=N'COLUMN', @level2name=N'FavouriteSetId';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'mandatory name given to a favourite set. This is what is displayed to the user.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'FavouriteSet', @level2type=N'COLUMN', @level2name=N'Name';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The id of the parent entity that owns this record', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'FavouriteSet', @level2type=N'COLUMN', @level2name=N'ParentEntityId';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Identifies the type of parent entity (usually a table name). Such as Party, Account, etc', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'FavouriteSet', @level2type=N'COLUMN', @level2name=N'ParentEntityType';

END
GO

IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='listing' and t.name='Favourite')
BEGIN
CREATE TABLE [listing].[Favourite]
(
 [FavouriteId]      int IDENTITY (1, 1) NOT NULL ,
 [FavouriteSetId]   int NULL ,
 [ListingId]        bigint NOT NULL ,
 [ParentEntityId]   uniqueidentifier NOT NULL , 
 [ParentEntityType] varchar(100) NOT NULL , 
 [Note]             nvarchar(3000) NULL ,
 [SortOrder]        int NOT NULL CONSTRAINT [DF_Favourite_SortOrder] DEFAULT 1 ,
 [CreatedOn]        datetimeoffset(7) NOT NULL , 
 [CreatedByName]    nvarchar(60) NULL , 
 [ModifiedOn]       datetimeoffset(7) NULL , 
 [ModifiedByName]   nvarchar(60) NULL , 
 [Deleted]          bit NOT NULL,

  CONSTRAINT [PK_Favourite] PRIMARY KEY CLUSTERED ([FavouriteId] ASC),

);

CREATE NONCLUSTERED INDEX [FK_Favourite_ListingId] ON [listing].[Favourite] 
 (
  [ListingId] ASC
 )

CREATE NONCLUSTERED INDEX [IX_Favourite_ParentEntityIdTypeDelSortOrder] ON [listing].[Favourite] 
 (
  [ParentEntityId] ASC, 
  [ParentEntityType] ASC, 
  [Deleted] ASC, 
  [SortOrder] ASC
 )
 INCLUDE (
  [ListingId]
 )

CREATE NONCLUSTERED INDEX [IX_Favourite_FavSetIdParentEntityIdDelSortOrder] ON [listing].[Favourite] 
 (
  [FavouriteSetId] ASC, 
  [ParentEntityId] ASC, 
  [Deleted] ASC, 
  [SortOrder] ASC
 )
 INCLUDE (
  [ListingId], 
  [ParentEntityType]
 )

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Favourite can be used by users/customers to remember, tag, wishlist a listing.
The parentEntityId will generally be a UserId or PartyId but could also be any other entity.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Favourite';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Internal Id used to identify a favourite. It must always be used in combination with the ParentEntityId', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Favourite', @level2type=N'COLUMN', @level2name=N'FavouriteId';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Optional set/group the favourited listing is placed in.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Favourite', @level2type=N'COLUMN', @level2name=N'FavouriteSetId';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The listing been favourited.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Favourite', @level2type=N'COLUMN', @level2name=N'ListingId';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The id of the parent entity that owns this record', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Favourite', @level2type=N'COLUMN', @level2name=N'ParentEntityId';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Identifies the type of parent entity (usually a table name). Such as Party, Account, etc', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Favourite', @level2type=N'COLUMN', @level2name=N'ParentEntityType';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Optional Note the creator of the favourite can add.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Favourite', @level2type=N'COLUMN', @level2name=N'Note';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Allows user to sort their favourites into there desired order.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Favourite', @level2type=N'COLUMN', @level2name=N'SortOrder';

END
GO


IF COL_LENGTH('listing.FilterGetMode', 'FilterGetModeCode') IS NULL
BEGIN

CREATE TABLE [listing].[FilterGetMode]
(
 [FilterGetModeCode] varchar(40) NOT NULL ,
 [Label]             nvarchar(400) NOT NULL ,

 CONSTRAINT [PK_FilterGetMode] PRIMARY KEY CLUSTERED ([FilterGetModeCode] ASC)
);

Insert Into [listing].[FilterGetMode]
(FilterGetModeCode, Label)
Values
('None','No processing done for get filters'),
('Distinct','Return each distinct value'),
('MinMax','Return Min and Max values');

ALTER TABLE [listing].[Attribute]
    ADD CONSTRAINT [FK_Attribute_FilterGetMode] FOREIGN KEY ([FilterGetModeCode])  REFERENCES [listing].[FilterGetMode]([FilterGetModeCode]);

END

IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='listing' and t.name='DisplayGroup')
BEGIN

CREATE TABLE [listing].[DisplayGroup]
(
 [DisplayGroupCode] varchar(60) NOT NULL ,
 [Label]            nvarchar(100) NULL ,
 [IsEnabled]        bit NOT NULL CONSTRAINT [DF_DisplayGroup_IsEnabled] DEFAULT 1 ,
 [Description]      nvarchar(1000) NULL ,

 CONSTRAINT [PK_DisplayGroup] PRIMARY KEY CLUSTERED ([DisplayGroupCode] ASC)
);

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'A DisplayGroup consists of 1 or more Display Containers. A front-end can request a Display Group and have all the related Display Container automatically returned (in order). This removes the need for every display container to be coded in the front-end allowing easy config in the database', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'DisplayGroup';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Free text field to describe the purpose of the Display Group. Used for documentation purposes only.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'DisplayGroup', @level2type=N'COLUMN', @level2name=N'Description';

CREATE TABLE [listing].[DisplayGroupDisplayContainer]
(
 [DisplayGroupDisplayContainerId] int IDENTITY (1, 1) NOT NULL ,
 [DisplayGroupCode]               varchar(60) NOT NULL ,
 [DisplayContainerCode]           varchar(60) NOT NULL ,
 [SortOrder]                      smallint NOT NULL CONSTRAINT [DF_DisplayGroupDisplayContainer_SortOrder] DEFAULT 99 ,

 CONSTRAINT [PK_DisplayGroupDisplayContainer] PRIMARY KEY NONCLUSTERED ([DisplayGroupDisplayContainerId] ASC),
 CONSTRAINT [FK_DisplayGroupDisplayContainer_DisplayGroup] FOREIGN KEY ([DisplayGroupCode])  REFERENCES [listing].[DisplayGroup]([DisplayGroupCode]),
 CONSTRAINT [FK_DisplayGroupDisplayContainer_DisplayContainer] FOREIGN KEY ([DisplayContainerCode])  REFERENCES [listing].[DisplayContainer]([DisplayContainerCode])
);

CREATE CLUSTERED INDEX [IX_DisplayGroupDisplayContainer_1] ON [listing].[DisplayGroupDisplayContainer] 
 (
  [DisplayGroupCode] ASC, 
  [SortOrder] ASC
 );

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Contains the Display Containers that are part of a Display Group.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'DisplayGroupDisplayContainer';
END

IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='listing' and t.name='TagSet')
BEGIN

CREATE TABLE [listing].[TagSet]
(
 [TagSetId] int IDENTITY (1, 1) NOT NULL ,
 [Label]    nvarchar(100) NOT NULL ,
 [SortOrder] int NULL,
 [IsEnabled] bit NOT NULL CONSTRAINT [DF_TagSet_IsEnabled] DEFAULT 1,
 [ParentTagSetId] int NULL,
 [Note] nvarchar(1000) NULL,
 [Icon] varchar(400) NULL,

 CONSTRAINT [PK_TagSet] PRIMARY KEY CLUSTERED ([TagSetId] ASC),
 CONSTRAINT [FK_TagSet_ParentTagSetId] FOREIGN KEY ([ParentTagSetId])  REFERENCES [listing].[TagSet]([TagSetId])
);

CREATE NONCLUSTERED INDEX [IX_TagSet_ParentTagSetIsEnabled] ON [listing].[TagSet] 
 (
  [ParentTagSetId] ASC, 
  [IsEnabled] ASC
 )
 INCLUDE (
  [TagSetId], 
  [Label], 
  [SortOrder]
 );

CREATE NONCLUSTERED INDEX [IX_TagSet_TagSetGroupIsEnabled] ON [listing].[TagSet] 
 (
  [IsEnabled] ASC
 )
 INCLUDE (
  [TagSetId], 
  [Label], 
  [SortOrder], 
  [ParentTagSetId]
 );

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'TagSet allows tags to be grouped into different sets for different use cases. A set could represent tags for a customer, and another set tags for an account.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'TagSet';

EXEC sp_addextendedproperty @name = N'MS_Description',
@value = N'Optional Parent TagSetId. Supports nesting tag sets',
@level0type = N'SCHEMA', @level0name = N'listing',
@level1type = N'TABLE', @level1name = N'TagSet',
@level2type=N'COLUMN', @level2name=N'ParentTagSetId';

EXEC sp_addextendedproperty @name = N'MS_Description',
@value = N'Additional information about a tag set that could be shown when a user hovers over a tag. set name',
@level0type = N'SCHEMA', @level0name = N'listing',
@level1type = N'TABLE', @level1name = N'TagSet',
@level2type=N'COLUMN', @level2name=N'Note';

EXEC sp_addextendedproperty @name = N'MS_Description',
@value = N'Specifies the icon to be displayed on the UI.
Optional.',
@level0type = N'SCHEMA', @level0name = N'listing',
@level1type = N'TABLE', @level1name = N'TagSet',
@level2type=N'COLUMN', @level2name=N'Icon';

CREATE TABLE [listing].[Tag]
(
 [TagId]     int IDENTITY (1, 1) NOT NULL ,
 [Label]     nvarchar(100) NOT NULL ,
 [SortOrder] int NULL ,
 [IsEnabled] bit NOT NULL CONSTRAINT [DF_Tag_IsEnabled] DEFAULT 1 ,
 [TagSetId]  int NOT NULL ,
 [TenantId]  int NULL ,
 [Note] nvarchar(1000) NULL,
 [Icon] varchar(400) NULL,
 [Colour] varchar(20) NULL,
 [ImageUrl] varchar(2000) NULL

 CONSTRAINT [PK_Tag] PRIMARY KEY CLUSTERED ([TagId] ASC)
);

CREATE NONCLUSTERED INDEX [FK_Tag_TagSet] ON [listing].[Tag] 
 (
  [TagSetId] ASC
 );


CREATE NONCLUSTERED INDEX [IX_Tag_TagSet] ON [listing].[Tag] 
 (
  [IsEnabled] ASC, 
  [TagSetId] ASC
 );

CREATE NONCLUSTERED INDEX [IX_Tag_Tenant] ON [listing].[Tag] 
 (
  [TenantId] ASC, 
  [IsEnabled] ASC, 
  [TagSetId] ASC
 )
 INCLUDE (
  [SortOrder], 
  [Label]
 );

 EXEC sp_addextendedproperty @name = N'MS_Description',
@value = N'Specifies the icon to be displayed on the UI.
Optional.',
@level0type = N'SCHEMA', @level0name = N'listing',
@level1type = N'TABLE', @level1name = N'Tag',
@level2type=N'COLUMN', @level2name=N'Icon';

EXEC sp_addextendedproperty @name = N'MS_Description',
@value = N'Optional colour code that can be used in the UI to colour a tag.',
@level0type = N'SCHEMA', @level0name = N'listing',
@level1type = N'TABLE', @level1name = N'Tag',
@level2type=N'COLUMN', @level2name=N'Colour';

EXEC sp_addextendedproperty @name = N'MS_Description',
@value = N'Optional Image URL that can be used to show an image for a tag.',
@level0type = N'SCHEMA', @level0name = N'listing',
@level1type = N'TABLE', @level1name = N'Tag',
@level2type=N'COLUMN', @level2name=N'ImageUrl';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'A tag is a text string (with no spaces) that can assign meaning or context to something. Useful for searching and filtering.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Tag';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Optional TenantId the Tag belongs to.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'Tag', @level2type=N'COLUMN', @level2name=N'TenantId';

CREATE TABLE [listing].[ListingTag]
(
 [ListingTagId]   bigint IDENTITY (1, 1) NOT NULL ,
 [ListingId]      bigint NOT NULL ,
 [TagId]          int NOT NULL ,
 [SortOrder]      int NULL ,
 [CreatedOn]      datetimeoffset(7) NOT NULL , 
 [CreatedByName]  nvarchar(60) NULL , 
 [ModifiedOn]     datetimeoffset(7) NULL , 
 [ModifiedByName] nvarchar(60) NULL , 
 [Deleted]        bit NOT NULL  

 CONSTRAINT [PK_ListingTag] PRIMARY KEY NONCLUSTERED ([ListingTagId] ASC),
 CONSTRAINT [FK_ListingTag_Listing] FOREIGN KEY ([ListingId])  REFERENCES [listing].[Listing]([ListingId]),
 CONSTRAINT [FK_ListingTag_Tag] FOREIGN KEY ([TagId])  REFERENCES [listing].[Tag]([TagId])
);

CREATE CLUSTERED INDEX [IX_Listing_Listing] ON [listing].[ListingTag] 
 (
  [ListingId] ASC, 
  [Deleted] ASC, 
  [TagId] ASC
 );

CREATE NONCLUSTERED INDEX [IX_ListingTag_TagId] ON [listing].[ListingTag] 
 (
  [TagId] ASC, 
  [Deleted] ASC, 
  [ListingId] ASC
 )
 INCLUDE (
  [SortOrder]
 );

END

IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='listing' and t.name='TagSetGroup')
BEGIN
    CREATE TABLE [listing].[TagSetGroup]
    (
     [TagSetGroupId] smallint NOT NULL ,
     [Label]         nvarchar(100) NOT NULL 

    );

    CREATE UNIQUE CLUSTERED INDEX [PK_TagSetGroup] ON [listing].[TagSetGroup] 
     (
      [TagSetGroupId] ASC
     )

    EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Tag Set Groups are used to optionally group tag sets together for display purposes avoiding the need to request specific tag sets.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'TagSetGroup';

    EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Tag Set Groups are used to optionally group tag sets together for display purposes avoiding the need to request specific tag sets.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'TagSetGroup', @level2type=N'COLUMN', @level2name=N'TagSetGroupId';

    CREATE TABLE [listing].[TagSetGroupTagSet]
    (
     [TagSetGroupTagSetId] int NOT NULL ,
     [TagSetGroupId]       smallint NOT NULL ,
     [TagSetId]            int NOT NULL ,

     CONSTRAINT [IX_TagSetGroupTagSet_GroupTageSet] UNIQUE NONCLUSTERED ([TagSetGroupId] ASC, [TagSetId] ASC),
     CONSTRAINT [FK_TagSetGroupTagSet_TagSetGroupId] FOREIGN KEY ([TagSetGroupId])  REFERENCES [listing].[TagSetGroup]([TagSetGroupId]),
     CONSTRAINT [FK_TagSetGroupTagSet_TagSetId] FOREIGN KEY ([TagSetId])  REFERENCES [listing].[TagSet]([TagSetId])
    );

    CREATE UNIQUE CLUSTERED INDEX [PK_TagSetGroupTagSet] ON [listing].[TagSetGroupTagSet] 
     (
      [TagSetGroupTagSetId] ASC
     )

    CREATE NONCLUSTERED INDEX [FK_TagSetGroupTagSet_TagSetGroup] ON [listing].[TagSetGroupTagSet] 
     (
      [TagSetGroupId] ASC
     )

    CREATE NONCLUSTERED INDEX [FK_TagSetGroupTagSet_TagSetId] ON [listing].[TagSetGroupTagSet] 
     (
      [TagSetId] ASC
     )

    EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Allows grouping Tag Sets into different Tag Set Groups for display purposes.
    A Tag Set could be in zero to many Tag Set Groups', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'TagSetGroupTagSet';

    EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Tag Set Groups are used to optionally group tag sets together for display purposes avoiding the need to request specific tag sets.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'TagSetGroupTagSet', @level2type=N'COLUMN', @level2name=N'TagSetGroupId';
END

IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='listing' and t.name='ListingAccessRole')
BEGIN
    CREATE TABLE [listing].[ListingAccessRole]
    (
     [ListingAccessRoleId] tinyint NOT NULL ,
     [Label]                 nvarchar(100) NOT NULL ,
     CONSTRAINT [PK_ListingAccessRoleId] PRIMARY KEY CLUSTERED ([ListingAccessRoleId] ASC)
    );

    EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Defines the level of access to a listing.
    0 - no access
    1 - view access
    2 - editor access', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'ListingAccessRole';

    EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Defines the level of access to a listing.
    0 - no access
    1 - view access
    2 - editor access', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'ListingAccessRole', @level2type=N'COLUMN', @level2name=N'ListingAccessRoleId';

    IF NOT EXISTS(SELECT 1 FROM [listing].[ListingAccessRole] WHERE [Label] = 'No Access')
    INSERT INTO [listing].[ListingAccessRole] (ListingAccessRoleId, Label)
    Values
    (0, 'No Access'),
    (1, 'View Only'),
    (2, 'Editor');

    CREATE TABLE [listing].[ListingAccess]
    (
     [ListingAccessId]		int IDENTITY (1, 1) NOT NULL ,
     [ListingId]              bigint NOT NULL ,
     [PartyId]                uniqueidentifier NOT NULL ,
     [ListingAccessRoleId] tinyint NOT NULL ,
     [PartyType]           nvarchar(100) NULL,
     [TenantId]              int NOT NULL ,
     [DisplayName]			nvarchar(100) NOT NULL,
     [CreatedOn]             datetimeoffset(7) NOT NULL , 
     [CreatedByName]         nvarchar(60) NULL , 
     [ModifiedOn]            datetimeoffset(7) NULL , 
     [ModifiedByName]        nvarchar(60) NULL , 
     [Deleted]               bit NOT NULL  
     CONSTRAINT [PK_ListingAccess] PRIMARY KEY CLUSTERED ([ListingAccessId] ASC),
     CONSTRAINT [FK_ListingAccess_ListingId] FOREIGN KEY ([ListingId])  REFERENCES [listing].[Listing]
    );

    CREATE NONCLUSTERED INDEX [IX_ListingAccess_Listing_1] ON [listing].[ListingAccess]
     (
	      [ListingId] ASC,
	      [Deleted] ASC
     )
     INCLUDE (
	     [ListingAccessRoleId],
	     [DisplayName],
	     [PartyId],
	     [ListingAccessId]
     );

    CREATE NONCLUSTERED INDEX [IX_ListingAccess_PartyListing_1] ON [listing].[ListingAccess]
     (
      [ListingId] ASC,
      [PartyId] ASC,
      [Deleted] ASC
     ) 
     INCLUDE (
	     [DisplayName],
	     [ListingAccessRoleId],
	     [ListingAccessId]
     );
     CREATE NONCLUSTERED INDEX [IX_ListingAccess_ListingIdAccessRolePartyTypePartyIdDel] ON [listing].[ListingAccess]
	(
		[ListingId] ASC,
		[ListingAccessRoleId] ASC,
		[PartyType] ASC,
		[PartyId] ASC,
		[Deleted] ASC
	);

    EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Controls access to a listing.
    Maps party to listings along with the level of access (via role). A Party can have View or Edit access.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'ListingAccess';

    EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Unique Listing Id.', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'ListingAccess', @level2type=N'COLUMN', @level2name=N'ListingId';

    EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Defines the level of access to a listing.
    0 - no access
    1 - view access
    2 - editor access', @level0type = N'SCHEMA', @level0name = N'listing', @level1type = N'TABLE', @level1name = N'ListingAccess', @level2type=N'COLUMN', @level2name=N'ListingAccessRoleId';
END
GO

/* All standard Base Claim's for Listing go here */
IF NOT EXISTS (SELECT * FROM [users].[Claim] WHERE [Name] = 'Edit Manage Listing')
    BEGIN
		INSERT INTO [users].[Claim] ([ClaimId], [Name], [Value], [ClaimGroup], [Deleted])
		VALUES
			('99bb8065-da85-42b6-9f39-ee07fdca291b', 'Edit Manage Listing', NULL, 'ListingManagement', 0),
            ('45425d7c-7bc0-4c1c-b50f-8115a3902573', 'Delete Manage Listing', NULL, 'ListingManagement', 0),
            ('32463903-2a03-4bc7-9215-287f1a3b4160', 'View Manage Listing', NULL, 'ListingManagement', 0),
            ('c72b296b-9fbb-4f81-8f3a-b543d81d0991', 'View Manage Listing List', NULL, 'ListingManagement', 0),
            ('a2488c40-ace0-4b46-95ce-1179489c23b9', 'Create Manage Listing', NULL, 'ListingManagement', 0);
	END
GO

IF NOT EXISTS (SELECT * FROM [users].[Claim] WHERE [Name] = 'Global Admin')
    BEGIN
		INSERT INTO [users].[Claim] ([ClaimId], [Name], [Value], [ClaimGroup], [Deleted])
		VALUES
			('eba905c0-5d22-4b22-8046-6249a4c99f5d', 'Global Admin', NULL, 'Global Admin', 0);
	END
GO

IF NOT EXISTS (SELECT * FROM [users].[Claim] WHERE [Name] = 'Manage Listing Upload File')
    BEGIN
		INSERT INTO [users].[Claim] ([ClaimId], [Name], [Value], [ClaimGroup], [Deleted])
		VALUES
            ('d7390cf4-a8f6-4fdd-9d2b-096d318e61ee', 'Manage Listing Upload File', NULL, 'ListingManagement', 0);
	END
GO

