﻿using Redi.Prime3.MicroService.BaseLib;
using Sql;

namespace MicroserviceDashboardContract.Dtos
{
    [Mappable(nameof(DashboardId))]
    public class BaseDashboardManagementCDto : DtoBase
    {
        public int DashboardId { get; set; }
        public string? Title { get; set; }
        public bool IsShowTitle { get; set; }
        public int DashboardStatusId { get; set; }
        public string? Description { get; set; }
        public string? Icon { get; set; }
        public bool IsTemplate { get; set; }
        public int? InheritedFromTemplateDashboardId { get; set; }
        public int? TenantId { get; set; }
        public string? PublicDashboardUrl { get; set; }
        public int PublicDashboardStatusId { get; set; }
        public int? SortOrder { get; set; }
        public bool IsDisplayAtRootLevel { get; set; }
    }

    public class GetDashboardManagementCDto : BaseDashboardManagementCDto {  }

    public class GetListDashboardManagementCDto : BaseDashboardManagementCDto { }



    public class GetDashboardManagementPlusAccessCDto : BaseDashboardManagementCDto
    {
        public int DashboardAccessRoleId { get; set; }
    }

    public class GetDashboardManagementPlusConfigCDto : BaseDashboardManagementCDto 
    {
        public string? ConfigJson { get; set; }
        public string? HelpText { get; set; }
        public int CurrentVersionNo { get; set; }
    }


    [Mappable(nameof(DashboardStatusId))]
    public class DashboardManagementStatusCDto
    {
        public int DashboardStatusId { get; set; }
        public string? Label { get; set; }
    }

    [Mappable(nameof(PublicDashboardStatusId))]
    public class DashboardManagementPublicStatusCDto
    {
        public int PublicDashboardStatusId { get; set; }
        public string? Label { get; set; }
    }

    public class DashboardManagementAuditCDto
    {
        public int DashboardId { get; set; }
        public string? Description { get; set; }
        public DateTimeOffset CreatedOn { get; set; }
        public string? CreatedByName { get; set; }
    }
}
