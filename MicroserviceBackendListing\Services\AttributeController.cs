using Castle.Core.Internal;
using MicroserviceBackendListing.BusinessLogic;
using MicroserviceBackendListing.Dtos;
using MicroserviceContract.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Microsoft.AspNetCore.Mvc;
using Sql;
using MicroserviceBackendListing.Policies;
using Microsoft.AspNetCore.Authorization;

namespace MicroserviceBackendListing.Services
{
    /// <summary>
    /// Get and Manage Attribute Definitions.
    /// </summary>
    [Route("api/Attribute")]
    public class AttributeController : AppController
    {
        private readonly BusinessLogic.Attribute _attribute;
        private readonly AttributeGroup _attributeG;
        public AttributeController(BusinessLogic.Attribute attribute, AttributeGroup attributeG, IUnitOfWork unitOfWork)
        {
            _attribute = attribute;
            _attributeG = attributeG;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a Single Attribute
        /// </summary>
        /// <remarks>
        /// Returns a single Attribute record for a given Attribute Code.
        /// </remarks>
        /// <param name="code">A code of an attribute record</param>
        /// <response code="200">Attribute returned, or an empty attribute if not found</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("Get")]
        [ProducesResponseType(typeof(GetAttributeCDto), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetAsync(string code)
        {
            var attribute = _attribute;
            var result = await attribute.GetAsync(code);
            return Ok(result);
        }

        /// <summary>
        /// Create an Attribute
        /// </summary>
        /// <remarks>
        /// Create an Attribute
        /// </remarks>
        /// <param name="dto">An Attribute to be created</param>
        /// <response code="200">Attribute created</response>
        /// <response code="422">Attribute not be processed</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("Create")]
        [Authorize(Policy = PolicyType.GlobalAdminPolicy)]
        [ProducesResponseType(typeof(GetAttributeCDto), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> CreateAsync([FromBody] GetAttributeCDto dto)
        {
            var logic = _attribute;
            await logic.CreateAsync(dto);
            _unitOfWork.Commit();
            GetAttributeDto result = await logic.GetAsync(dto.AttributeCode);
            return Ok(result);
        }

        /// <summary>
        /// Update an Attribute
        /// </summary>
        /// <remarks>
        /// Update an Attribute
        /// </remarks>
        /// <param name="dto"></param>
        /// <response code="200">Attribute updated</response>
        /// <response code="422">Attribute not be processed</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("Update")]
        [Authorize(Policy = PolicyType.GlobalAdminPolicy)]
        [ProducesResponseType(typeof(GetAttributeCDto), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> UpdateAsync([FromBody] GetAttributeCDto dto)
        {
            var attribute = _attribute;
            await attribute.UpdateAsync(dto);
            _unitOfWork.Commit();
            GetAttributeDto result = await attribute.GetAsync(dto.AttributeCode);
            return Ok(result);
        }

        /// <summary>
        /// Delete an Attribute record
        /// </summary>
        /// <remarks>
        /// Delete an Attribute with the given code.
        /// </remarks>
        /// <param name="code">A code of an attribute record</param>
        /// <response code="200">Attribute has been deleted</response>
        /// <response code="404">Attribute not found</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("Delete")]
        [Authorize(Policy = PolicyType.GlobalAdminPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> DeleteAsync([FromQuery] string code)
        {
            var attribute = _attribute;
            await attribute.DeleteAsync(code);
            _unitOfWork.Commit();
            return Ok();
        }

        /// <summary>
        /// Enable an Attribute
        /// </summary>
        /// <remarks>
        /// Enables an Attribute with the given code.
        /// </remarks>
        /// <param name="code">A code of an attribute record</param>
        /// <response code="200">Attribute has been enabled</response>
        /// <response code="404">Attribute not found</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("Enable")]
        [Authorize(Policy = PolicyType.GlobalAdminPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> EnableAsync([FromQuery] string code)
        {
            var attributeCode = _attribute;
            await attributeCode.EnableAsync(code);
            _unitOfWork.Commit();
            return Ok();
        }

        /// <summary>
        /// Disable an Attribute
        /// </summary>
        /// <remarks>
        /// Disables an Attribute with the given code.
        /// </remarks>
        /// <param name="code">A code of an attribute record</param>
        /// <response code="200">Attribute has been disabled</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("Disable")]
        [Authorize(Policy = PolicyType.GlobalAdminPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> DisableAsync([FromQuery] string code)
        {
            var attributeCode = _attribute;
            await attributeCode.DisableAsync(code);
            _unitOfWork.Commit();
            return Ok();
        }

        /// <summary>
        /// Get a List of Attributes
        /// </summary>
        /// <remarks>
        /// This is the primary call for fetching attributes based filters or search parameters. \
        /// Returns only enabled Attributes by default. \
        /// </remarks>
        /// <param name="standardListParameters">Determines the limit, offset, sortby</param>
        /// <param name="attributeGroupCode">Filter the Attributes by the Attribute Group Code</param>
        /// <param name="allowDisabled">Increase the range of Attributes returned to include disabled Attributes</param>
        /// <response code="200">List of Attributes returned</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("ListAttribute")]
        [ProducesResponseType(typeof(List<GetAttributeDto>), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetListAsync([FromQuery] StandardListParameters standardListParameters, string? attributeGroupCode, bool? allowDisabled)
        {
            var groupLogic = _attributeG;            
            if(attributeGroupCode != null) {
                var result = await groupLogic.GetAsync(attributeGroupCode); 
                return Ok(result);
            }
            else
            {
                var result = await groupLogic.ListtAsync(standardListParameters, allowDisabled);
                return Ok(result);
            }
        }
    }
}
