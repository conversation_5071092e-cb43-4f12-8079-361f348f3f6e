﻿using MicroserviceBackendListing.Dtos;
using MicroserviceContract.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
namespace MicroserviceBackendListing.BusinessLogic
{
    public class AttributeGroup : BusinessLogicBase
    {
        private readonly Func<Attribute> _attribute;
        public AttributeGroup(IUnitOfWork u, Func<Attribute> attribute)
        {
            _unitOfWork = u;
            _attribute = attribute;
        }
        internal async Task<AttributeByGroupListDto> GetAsync(string AttributeGroupCode)
        {
            string sql = @"
                        SELECT  
                        [AttributeGroup].[Label] AS GroupLabel, 
                        [AttributeGroup].[AttributeGroupCode], 
                        [AttributeGroup].[Description] AS GroupDescription,                          
                        [AttributeGroup].[Enabled] AS GroupIsEnabled
                        FROM [listing].[AttributeGroup][AttributeGroup] 
                        WHERE [AttributeGroup].[AttributeGroupCode] = @AttributeGroupCode";
            AttributeByGroupListDto result = new AttributeByGroupListDto();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("AttributeGroupCode", AttributeGroupCode);
                result = await command.SelectSingle<AttributeByGroupListDto>();
            }
            if (result == null)
            {
                throw new HttpRequestException($"AttributeGroupCode '{AttributeGroupCode}' does not exist", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }
            string? code = result.AttributeGroupCode;
            var list = await _attribute().GetAttributesByGroupCodeAsync(null, code,false);
            result.Attributes = list.List;
            return result;
        }

        public async Task<bool> GroupIsEnable(string AttributeGroupCode)
        {
            string sql = @"
                        SELECT  
                        [AttributeGroup].[AttributeGroupCode],                        
                        [AttributeGroup].[Enabled] AS GroupIsEnabled
                        FROM [listing].[AttributeGroup][AttributeGroup]
                        WHERE [AttributeGroup].[AttributeGroupCode] = @AttributeGroupCode";
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("AttributeGroupCode", AttributeGroupCode);

                var result = await command.SelectSingle<AttributeByGroupListDto>();
                if (result == null)
                    return false;
                return result.GroupIsEnabled;
            }
        }

            internal async Task<List<AttributeByGroupListDto>> ListtAsync(StandardListParameters? paging = null, bool? showDisable = false)
            {
            if (paging == null)
            {
                paging = new StandardListParameters();
            }
            string sql = @"
                        SELECT  
                        [AttributeGroup].[Label] AS GroupLabel, 
                        [AttributeGroup].[AttributeGroupCode], 
                        [AttributeGroup].[Description] AS GroupDescription,                          
                        [AttributeGroup].[Enabled] AS GroupIsEnabled
                        FROM [listing].[AttributeGroup] [AttributeGroup]
                        WHERE [AttributeGroup].[Enabled] = 1";
            List<AttributeByGroupListDto> result;
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
    
                result = await command.SelectMany<AttributeByGroupListDto>();
            }
            foreach (AttributeByGroupListDto dto in result)
            {
                if (dto.AttributeGroupCode != null)
                {
                    string code = dto.AttributeGroupCode;
                    var list = await _attribute().GetAttributesByGroupCodeAsync(paging, code, showDisable);
                    dto.Attributes = list.List;
                }
            }
            return result;
        }
    }
}
