param environmentName string
param azureClientId string
param keyVaultName string
param dockerRepoName string
@secure()
param secretStoreName string 

resource containerAppsEnv 'Microsoft.App/managedEnvironments@2022-10-01' existing = {
  name: environmentName

  resource daprComponent 'daprComponents@2022-10-01' = {
    name: secretStoreName
    properties: {
      componentType: 'secretstores.azure.keyvault'
      version: 'v1'
      secrets: [
        {
          name: 'azureclientref'
          value: azureClientId
        }
      ]
      metadata: [
        {
          name: 'vaultName'
          value: keyVaultName
        }
        {
          name: 'azureClientId'
          secretRef: 'azureclientref'
        }
      ]
      scopes: [
        dockerRepoName
      ]
    }
  }
}
