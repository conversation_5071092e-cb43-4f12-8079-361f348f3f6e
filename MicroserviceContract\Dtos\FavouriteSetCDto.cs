﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Sql;
using System.Threading.Tasks;

namespace MicroserviceContract.Dtos
{
    [Mappable(nameof(FavouriteSetId))]
    public class FavouriteSetCDto
    {
        public int FavouriteSetId { get; set; }
        public string? Name { get; set; }
        public Guid ParentEntityId { get; set; }
        public string? ParentEntityType { get; set; }
        public int SortOrder { get; set; }
    }

    public class GetFavouriteSetCDto : FavouriteSetCDto
    {

    }
}
