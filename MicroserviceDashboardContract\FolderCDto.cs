﻿using Redi.Prime3.MicroService.BaseLib;
using Sql;

namespace MicroserviceDashboardContract.Dtos
{
    [Mappable(nameof(DashboardFolderId))]
    public class BaseFolderCDto : DtoBase
    {
        public int DashboardFolderId { get; set; }
        public string? Name { get; set; }
        public int? SortOrder { get; set; }
        public int? TenantId { get; set; }
    }

    public class GetFolderCDto : BaseFolderCDto {  }

    public class GetListFolderCDto : BaseFolderCDto {  }

    /// <summary>
    /// Extend BaseFolderCDto for conveniece of 
    /// checking user access to all the folder dashboards
    /// </summary>
    public class FolderWithAccessDto : BaseFolderCDto
    {
        public int DashboardId { get; set; }
    }



    [Mappable(nameof(DashboardFolderDashboardId))]
    public class BaseFolderDashboardCDto : DtoBase
    {
        public int DashboardFolderDashboardId { get; set; }
        public int DashboardFolderId { get; set; }
        public int DashboardId { get; set; }
        public int? SortOrder { get; set; }
    }
    public class GetFolderDashboardCDto : BaseFolderDashboardCDto { }
}
