﻿using MicroserviceBackendListing.Enums;
using MicroserviceContract.Dtos;
using MicroserviceContract.Dtos.Booking;
using Redi.Prime3.MicroService.BaseLib;
using Microsoft.Extensions.Caching.Memory;
using Sql;

namespace MicroserviceBackendListing.BusinessLogic.Booking
{
    /// <summary>
    /// Booking Record Update - manages all aspects of an actual booking or reservation
    /// </summary>
    public partial class BookingRecord : BusinessLogicBase
    {
        internal async Task<long?> CreateNewBookingRecord(RequestCDto<BookingRecordRequestCDto> requestCDto, BookingRecordStatusEnum bookingStatus, int? forceToTenantId = null)
        {
            if (requestCDto == null || requestCDto.Data == null) { return null; }
            BookingRecordRequestCDto dto = requestCDto.Data;

            var exists = dto.BookingRecordId != null ? await GetSingleBookingRecord((long)dto.BookingRecordId) : null;
            if (exists != null && dto.BookingRecordId != null)
            {
                await Update((long)dto.BookingRecordId, requestCDto);
                return dto.BookingRecordId;
            }

            var tenantId = forceToTenantId ?? _utils.TenantId;
            // Set default values
            // ******************
            if (dto.BookingOwnerParentEntityId.GuidIsNullOrEmpty() && (dto.BookingOwnerParentEntityIntId == null || dto.BookingOwnerParentEntityIntId <= 0)) 
            {
                if (_utils.PartyId != null)
                {
                    dto.BookingOwnerParentEntityId = _utils.PartyId;
                    dto.BookingOwnerParentEntityType = "Party"; 
                }
                else
                {
                    dto.BookingOwnerParentEntityId = _utils.UserId;
                    dto.BookingOwnerParentEntityType = "User";
                }
            }
            if (dto.IsRecurringBookingCustomised == null) { dto.IsRecurringBookingCustomised = false; }

            Guid? ownerPartyId = dto.BookingOwnerParentEntityType == "Party" ? dto.BookingOwnerParentEntityId : null;
            List<long> bookingItemIds = new List<long>();
            if (string.IsNullOrEmpty(dto.BookingItemIds)) { throw new HttpRequestException("BookingItemIds required."); }
            try
            {
                bookingItemIds = dto.BookingItemIds.Split(",").Select(x => long.Parse(x.ToString())).ToList();
            }
            catch (Exception)
            {
                throw new HttpRequestException($"BookingItemIds ({dto.BookingItemIds}) are not valid. Must be numeric and comma separated if more than 1 booking item.");
            }

            if (dto.PickupFromHoldingLocationId == null)
            {
                // Get the Holding Location for the requested date
#pragma warning disable CS8629 // Nullable value type may be null.
                dto.PickupFromHoldingLocationId = await GetBookingItemHoldingLocation(bookingItemIds[0], (DateTimeOffset)dto.FromDateTime, (DateTimeOffset)dto.FromDateTime);
#pragma warning restore CS8629 // Nullable value type may be null.
            }
            if (dto.ReturnToHoldingLocationId == null) { dto.ReturnToHoldingLocationId = dto.PickupFromHoldingLocationId; }

            await ValidateBookingRecord(dto.BookingRecordId, requestCDto);
            long? newBookingRecordId = null;
            string bookingReference = await SetBookingRecordReference(false, "");
            string sql = @"
        INSERT INTO [booking].[BookingRecord]
                    (
                     [BookingRecurringId]
                    ,[BookingRecordReference]
                    ,[IsRecurringBookingCustomised]
                    ,[FromDateTime]
                    ,[ToDateTime]
                    ,[BookingRecordStatusId]
                    ,[BookingOwnerParentEntityId]
                    ,[BookingOwnerParentEntityIntId]
                    ,[BookingOwnerParentEntityType]
                    ,[PickupFromHoldingLocationId]
                    ,[ReturnToHoldingLocationId]
                    ,[Note]
                    ,[TenantId]
                    ,[CreatedOn]
                    ,[CreatedByName]
                    ,[Deleted]
                    )
        VALUES
                    (
                     @BookingRecurringId
                    ,@BookingRecordReference
                    ,@IsRecurringBookingCustomised
                    ,@FromDateTime
                    ,@ToDateTime
                    ,@BookingRecordStatusId
                    ,@BookingOwnerParentEntityId
                    ,@BookingOwnerParentEntityIntId
                    ,@BookingOwnerParentEntityType
                    ,@PickupFromHoldingLocationId
                    ,@ReturnToHoldingLocationId
                    ,@Note
                    ,@TenantId
                    ,@CreatedOn
                    ,@CreatedByName
                    ,@Deleted
                    )";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArgument("BookingRecordStatusId", (short)bookingStatus);
                command.AddArgument("TenantId", tenantId);
                command.AddArgument("CreatedOn", DateTimeOffset.UtcNow);
                command.AddArgument("CreatedByName", _utils.UserFullName);
                command.AddArgument("Deleted", false);
                command.AddArgument("BookingRecordReference", bookingReference);

                // Create
                command.AddArguments(dto);

                newBookingRecordId = await command.ExecuteAndReturnIdentityLong();

                // Log
                string description = $"New Booking {bookingReference} created by {_utils.UserFullName} with Status {await GetBookingRecordStatusLabel((short)bookingStatus)}";
                await AddBookingRecordEvent((long)newBookingRecordId, description);
            }

            await InsertBookingRecordItems((long)newBookingRecordId, bookingItemIds);
            HoldingLocationResponseCDto? pickupFromHoldingLocation = null;
            HoldingLocationResponseCDto? returnToHoldingLocation = null;

            if (dto.PickupFromHoldingLocationId != null)
            {
                pickupFromHoldingLocation = await _holdingLocation.GetHoldingLocationAsync((int)dto.PickupFromHoldingLocationId);
            }
            if (dto.ReturnToHoldingLocationId != null)
            {
                returnToHoldingLocation = await _holdingLocation.GetHoldingLocationAsync((int)dto.ReturnToHoldingLocationId);
            }

            await _writeNotificationEvent.WriteEvent(NotificationEventCodeEnum.BookingBooked.ToString(), tenantId, ownerPartyId, ownerPartyId, _utils.UserId, metaData: new Dictionary<string, object?>() {
                                    { "BookingRecordReference", bookingReference },
                                    { "BookingRecordId",newBookingRecordId },
                                    { "FromDateTime", dto.FromDateTime },
                                    { "ToDateTime", dto.ToDateTime },
                                    { "BookingRecordStatus", bookingStatus },
                                    { "BookingRecordStatusLabel", await GetBookingRecordStatusLabel((short)bookingStatus) },
                                    { "PickupFromHoldingLocationName", pickupFromHoldingLocation?.HoldingLocationName },
                                    { "ReturnToHoldingLocationName", returnToHoldingLocation?.HoldingLocationName },
                                    { "PickupFromHoldingLocationId", pickupFromHoldingLocation?.HoldingLocationId },
                                    { "ReturnToHoldingLocationId", returnToHoldingLocation?.HoldingLocationId }});
            return newBookingRecordId;
        }

        internal async Task Update(long bookingRecordId, RequestCDto<BookingRecordRequestCDto> requestCDto)
        {
            if (requestCDto == null || requestCDto.Data == null) { throw new HttpRequestException("Missing dto Data"); }
            BookingRecordRequestCDto dto = requestCDto.Data;
            // Validate order exists in db
            var exists = await GetSingleBookingRecord(bookingRecordId, forUpdate: true);

            if (exists == null)
            {
                throw new HttpRequestException($"BookingRecordId not found {(_utils.TenantId != null ? "for Tenant" : "")}.", null, System.Net.HttpStatusCode.NotFound);
            }
            (bool changes, bool itemsChanged) = await ValidateBookingRecord(bookingRecordId, requestCDto, exists);
            if (changes == false)
            {
                // nothing has changed.
                return;
            }

            string sql = @"
            UPDATE  [booking].[BookingRecord]
            SET
                     [FromDateTime] = @FromDateTime
                    ,[ToDateTime] = @ToDateTime
                    ,[BookingOwnerParentEntityId] = @BookingOwnerParentEntityId
                    ,[BookingOwnerParentEntityIntId] = @BookingOwnerParentEntityIntId
                    ,[BookingOwnerParentEntityType] = @BookingOwnerParentEntityType
                    ,[Note] = @Note
                    ,[ModifiedOn] = @ModifiedOn
                    ,[ModifiedByName] = @ModifiedByName
                    ,[IsRecurringBookingCustomised] = @IsRecurringBookingCustomised
            WHERE    [BookingRecordId] = @BookingRecordId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {

                // Create
                command.AddArguments(dto);
                command.AddArgument("ModifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("ModifiedByName", _utils.UserFullName);

                await command.Execute();

                string description = $"Booking updated ";
                await AddBookingRecordEvent(bookingRecordId, description);
            }

            if (itemsChanged)
            {
                // Add or Remove any Booking Items for the Booking Record.
                var existBookingItemIds = exists.BookingItemIds?.Split(",").Select(x => long.Parse(x.ToString())).ToList() ?? [];
                var requestBookingItemIds = dto.BookingItemIds?.Split(",").Select(x => long.Parse(x.ToString())).ToList() ?? [];
                List<long> itemsToRemove = new List<long>();
                List<long> itemsToAdd = new List<long>();   
                foreach(var item in existBookingItemIds)
                {
                    if (!requestBookingItemIds.Contains(item))
                    {
                        itemsToRemove.Add(item);
                    }
                }

                foreach (var item in requestBookingItemIds)
                {
                    if (!existBookingItemIds.Contains(item))
                    {
                        itemsToAdd.Add(item);
                    }
                }

                if (itemsToRemove.Count > 0)
                {
                    await RemoveBookingRecordItems(bookingRecordId, itemsToRemove);
                }
                if (itemsToAdd.Count > 0)
                {
                    await InsertBookingRecordItems(bookingRecordId, itemsToAdd);
                }
            }

        }


#pragma warning disable CS1998 // Async method lacks 'await' operators and will run synchronously
        internal async Task<(bool, bool)> ValidateBookingRecord(long? bookingRecordId, RequestCDto<BookingRecordRequestCDto> newRequestCDto, BookingRecordResponseCDto? existCDto = null)
#pragma warning restore CS1998 // Async method lacks 'await' operators and will run synchronously
        {
            if (newRequestCDto == null || newRequestCDto.Data == null) { return (false, false); }
            // The ExcludeFields cannot be directly updated by data passed into this call.
            List<string> excludeFields = new List<string>() { "BookingRecordId", "BookingRecordStatusId", "CreatedOn", "CreatedByName", "Deleted", "ModifiedOn", "ModifiedByName", "Statistics" };

            var dto = newRequestCDto.Data;
            bool isNew = existCDto == null;
            // Validate fields
            bool hasChanges = false;
            bool itemsChanged = false;

            // Check what data has changed (determine what is to be updated in the database)
            if (isNew == false && existCDto != null)
            {
                if (newRequestCDto?.ClearFields?.ContainsKey("Note") == true) { dto.Note = null; hasChanges = true; }
                else { if (!string.IsNullOrEmpty(dto.Note) && dto.Note != existCDto.Note) { hasChanges = true; } else { dto.Note = existCDto.Note; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("FromDateTime") == true) { dto.FromDateTime = null; hasChanges = true; }
                else { if (dto.FromDateTime != null && dto.FromDateTime != existCDto.FromDateTime) { hasChanges = true; } else { dto.FromDateTime = existCDto.FromDateTime; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("ToDateTime") == true) { dto.ToDateTime = null; hasChanges = true; }
                else { if (dto.ToDateTime != null && dto.ToDateTime != existCDto.ToDateTime) { hasChanges = true; } else { dto.ToDateTime = existCDto.ToDateTime; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("BookingOwnerParentEntityId") == true) { dto.BookingOwnerParentEntityId = null; hasChanges = true; }
                else { if (dto.BookingOwnerParentEntityId != null && dto.BookingOwnerParentEntityId != existCDto.BookingOwnerParentEntityId) { hasChanges = true; } else { dto.BookingOwnerParentEntityId = existCDto.BookingOwnerParentEntityId; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("BookingOwnerParentEntityIntId") == true) { dto.BookingOwnerParentEntityIntId = null; hasChanges = true; }
                else { if (dto.BookingOwnerParentEntityIntId != null && dto.BookingOwnerParentEntityIntId != existCDto.BookingOwnerParentEntityIntId) { hasChanges = true; } else { dto.BookingOwnerParentEntityIntId = existCDto.BookingOwnerParentEntityIntId; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("BookingOwnerParentEntityType") == true) { dto.BookingOwnerParentEntityType = null; hasChanges = true; }
                else { if (!string.IsNullOrEmpty(dto.BookingOwnerParentEntityType) && dto.BookingOwnerParentEntityType != existCDto.BookingOwnerParentEntityType) { hasChanges = true; } else { dto.BookingOwnerParentEntityType = existCDto.BookingOwnerParentEntityType; } }

                if (string.IsNullOrEmpty(dto.BookingItemIds)) { dto.BookingItemIds = existCDto.BookingItemIds; } // Can't remove all booking item ids.
                var existBookingItemIds = existCDto.BookingItemIds?.Split(",").Select(x => long.Parse(x.ToString())).ToList();
                var requestBookingItemIds = dto.BookingItemIds?.Split(",").Select(x => long.Parse(x.ToString())).ToList();
                if (existBookingItemIds != null && requestBookingItemIds != null && !Enumerable.SequenceEqual(existBookingItemIds.OrderBy(fElement => fElement),
                         requestBookingItemIds.OrderBy(sElement => sElement))) { hasChanges = true; itemsChanged = true; }
            }

            if (!hasChanges && !isNew)
            {
                // no data has changed to exit.
                return (false, false);
            }

            if (dto.Note?.Length > 2000) { throw new HttpRequestException("Note must be less than 2,000 characters."); }
            if (string.IsNullOrEmpty(dto.BookingItemIds)) { throw new HttpRequestException("At least 1 bookingItemId is required."); }
            if (dto.FromDateTime == null) { throw new HttpRequestException($"FromDateTime is required."); }
            if (dto.ToDateTime == null) { throw new HttpRequestException($"ToDateTime is required."); }
            if (dto.FromDateTime > dto.ToDateTime) { throw new HttpRequestException($"From Datetime ({dto.FromDateTime}) must be before the To Datetime ({dto.ToDateTime})."); }
            if (isNew && string.IsNullOrEmpty(dto.BookingOwnerParentEntityType)) { throw new HttpRequestException("BookingOwnerParentEntityType is required."); }

            return (true, itemsChanged);
        }

        internal async Task UpdateStatusAsync(long bookingRecordId, Int16 newBookingRecordStatusId, string? note = null)
        {
            // Validate Invoice exists
            var dto = await GetSingleBookingRecord(bookingRecordId, forUpdate: true);

            if (dto == null)
            {
                throw new HttpRequestException($"BookingRecordId not found {(_utils.TenantId != null ? "for Tenant" : "")}.", null, System.Net.HttpStatusCode.NotFound);
            }

            // Validate InvoiceStatusId exists
            bool statusValid = await IsBookingRecordStatusIdValid(newBookingRecordStatusId);
            if (statusValid == false)
            {
                throw new HttpRequestException($"Requested status id update {newBookingRecordStatusId} does not exist.");
            }

            if (newBookingRecordStatusId == dto.BookingRecordStatusId)
            {
                // No change.
                return;
            }

            // Update Booking
            string sql = @"
            UPDATE   [booking].[BookingRecord]
            SET      [BookingRecordStatusId] = @BookingRecordStatusId,
                     [ModifiedByName] = @ModifiedByName,
                     [ModifiedOn] = @ModifiedOn
            WHERE    [BookingRecordId] = @BookingRecordId";

            DateTimeOffset now = DateTimeOffset.UtcNow;
            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("BookingRecordStatusId", newBookingRecordStatusId);
                command.AddArgument("ModifiedOn", now);
                command.AddArgument("ModifiedByName", _utils.UserFullName);
                command.AddArgument("BookingRecordId", bookingRecordId);

                await command.Execute();
            }

            // Log
            string description = $@"Status changed to {await GetBookingRecordStatusLabel(newBookingRecordStatusId)}({newBookingRecordStatusId}) from {await GetBookingRecordStatusLabel(dto.BookingRecordStatusId)}({dto.BookingRecordStatusId}) ";
            await AddBookingRecordEvent(bookingRecordId, description);

            if (note != null)
            {
                await _daprCommonServiceClient().CreateBookingRecordNote(note, bookingRecordId);
            }
        }

        internal async Task ExtendBooking(long bookingRecordId, DateTimeOffset? newEndTime, int? extendByMinutes)
        {
            // Validate Invoice exists
            var dto = await GetSingleBookingRecord(bookingRecordId, forUpdate: true);

            if (dto == null)
            {
                throw new HttpRequestException($"BookingRecordId not found {(_utils.TenantId != null ? "for Tenant" : "")}.", null, System.Net.HttpStatusCode.NotFound);
            }

            if (dto.Deleted == true) { return; }

            // Update ToDateTime
            string sql = @"
            UPDATE   [booking].[BookingRecord]
            SET      [ToDateTime] = @ToDateTime,
                     [ModifiedByName] = @ModifiedByName,
                     [ModifiedOn] = @ModifiedOn
            WHERE    [BookingRecordId] = @BookingRecordId";

            DateTimeOffset now = DateTimeOffset.UtcNow;
            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
#pragma warning disable CS8629 // Nullable value type may be null.
                command.AddArgument("ToDateTime", newEndTime != null ? newEndTime : dto.ToDateTime.Value.AddMinutes(extendByMinutes ?? 0));
#pragma warning restore CS8629 // Nullable value type may be null.
                command.AddArgument("ModifiedOn", now);
                command.AddArgument("ModifiedByName", _utils.UserFullName);
                command.AddArgument("bookingRecordId", bookingRecordId);

                await command.Execute();
            }

        }

        internal async Task DeleteBookingRecord(long bookingRecordId)
        {
            // Validate Invoice exists
            var dto = await GetSingleBookingRecord(bookingRecordId, forUpdate: true);

            if (dto == null)
            {
                throw new HttpRequestException($"BookingRecordId not found {(_utils.TenantId != null ? "for Tenant" : "")}.", null, System.Net.HttpStatusCode.NotFound);
            }

            if (dto.Deleted == true) { return; }

            // Update Booking
            string sql = @"
            UPDATE   [booking].[BookingRecord]
            SET      [Deleted] = @Deleted,
                     [ModifiedByName] = @ModifiedByName,
                     [ModifiedOn] = @ModifiedOn
            WHERE    [BookingRecordId] = @BookingRecordId";

            DateTimeOffset now = DateTimeOffset.UtcNow;
            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("Deleted", true);
                command.AddArgument("ModifiedOn", now);
                command.AddArgument("ModifiedByName", _utils.UserFullName);
                command.AddArgument("bookingRecordId", bookingRecordId);

                await command.Execute();
            }

        }

        internal async Task RemoveItemFromBooking(long bookingRecordId, long bookingItemId)
        {
            await RemoveBookingRecordItems(bookingRecordId, new List<long>() { bookingItemId });
        }

        internal async Task AddItemToBooking(long bookingRecordId, long bookingItemId)
        {
            await InsertBookingRecordItems(bookingRecordId, new List<long>() { bookingItemId });
        }

        internal async Task<string> SetBookingRecordReference(bool isTest, string? exstingReference)
        {
            const string testText = "**Test**";
            if (isTest)
            {
                if (exstingReference == null || !exstingReference.Contains(testText))
                {
                    return testText;
                }
                return exstingReference;
            }

            if (string.IsNullOrEmpty(exstingReference) == true || exstingReference == testText)
            {
                // Booking Reference Reference runs off the Next Number table. 
                // For Tenant based solutions each tenant has there own sequence of numbers
                exstingReference = await _daprCommonServiceClient().GetNextNumber(nextNumberTypeCode: "BookingRecord",
                                                                                    entityId: (_utils.TenantId != null ? "Tenant" + _utils.TenantId?.ToString() : ""),
                                                                                    formatString: "BYY{0:D6}",
                                                                                    prefixWithCurrentYear: true);
            }

            return exstingReference;
        }

        private async Task AddBookingRecordEvent(long bookingRecordId, string description)
        {
            EventCDto dto = new EventCDto()
            {
                EventId = 0,
                ParentEntityIntId = bookingRecordId,
                ParentEntityType = ENTITY_TYPE,
                Description = (description.Length > 4000 ? description.Substring(0, 4000) : description),
                CreatedOn = DateTimeOffset.UtcNow,
                CreatedByName = _utils.UserFullName
            };

            string sql = @"
            INSERT INTO [common].[Event]
                        (
                         [ParentEntityIntId]
                        ,[ParentEntityType]
                        ,[Description]
                        ,[CreatedOn]
                        ,[CreatedByName]
                        )
            VALUES
                        (
                         @ParentEntityIntId
                        ,@ParentEntityType
                        ,@Description
                        ,@CreatedOn
                        ,@CreatedByName
                        )";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArguments(dto);

                await command.Execute();
            }
        }

        internal async Task<string?> GetBookingRecordStatusLabel(short? bookingRecordStatusId)
        {
            string cacheKey = "GetBookingRecordStatusLabel" + bookingRecordStatusId.ToString();
            if (_memoryCache.TryGetValue(cacheKey, out string? cacheValue))
            {
                return cacheValue;
            }

            string sqlStr = @"
                    SELECT 
                             [Label]
                    FROM     [booking].[BookingRecordStatus]
                    WHERE    [BookingRecordStatusId] = @BookingRecordStatusId AND [IsEnabled] = 1";

            using (var query = await _unitOfWork.CmdFactory(CmdType.Select, sqlStr))
            {
                query.AddArgument("BookingRecordStatusId", bookingRecordStatusId);
                var result = await query.SelectSingle<string?>("Label");
                if (result == null)
                {
                    return null;
                }

                _memoryCache.Set(cacheKey, result, CacheOptions());

                return result;
            }
        }

        internal async Task<bool> IsBookingRecordStatusIdValid(int? bookingRecordStatusId)
        {
            string sqlStr = @"
                    SELECT 
                             [Label]
                    FROM     [booking].[BookingRecordStatus]
                    WHERE    [BookingRecordStatusId] = @bookingRecordStatusId AND [IsEnabled] = 1";

            using (var query = await _unitOfWork.CmdFactory(CmdType.Select, sqlStr))
            {
                query.AddArgument("bookingRecordStatusId", bookingRecordStatusId);
                var result = await query.SelectSingle<string?>("Label");
                if (result == null)
                {
                    return false;
                }

                return true;
            }
        }

        internal async Task<int?> GetBookingItemHoldingLocation(long bookingItemId, DateTimeOffset fromDate, DateTimeOffset toDate)
        {
            string sqlStr = @"
                    SELECT    TOP(1)
                             [BookingItemHoldingLocation].[HoldingLocationId]
                    FROM     [booking].[BookingItemHoldingLocation]
                    INNER JOIN [booking].[HoldingLocation] ON [HoldingLocation].[HoldingLocationId] = [BookingItemHoldingLocation].[HoldingLocationId] AND [HoldingLocation].[Deleted] = 0
                    WHERE    [BookingItemHoldingLocation].[BookingItemId] = @bookingItemId 
                      AND    [BookingItemHoldingLocation].[Deleted] = 0
                      AND    [BookingItemHoldingLocation].[FromDateTime] <= @fromDate
                      AND    ([BookingItemHoldingLocation].[ToDateTime] IS NULL OR [BookingItemHoldingLocation].[ToDateTime] >= @toDate ) ";

            using (var query = await _unitOfWork.CmdFactory(CmdType.Select, sqlStr))
            {
                query.AddArgument("bookingItemId", bookingItemId);
                query.AddArgument("fromDate", fromDate);
                query.AddArgument("toDate", toDate);
                var result = await query.SelectSingle<int?>("HoldingLocationId");

                return result;
            }
        }

        internal async Task InsertBookingRecordItems(long bookingRecordId, List<long> bookingItemIds)
        {
            foreach (var itemId in bookingItemIds)
            {
                string sql = @"
        IF NOT EXISTS (SELECT * FROM [booking].[BookingRecordItem] WHERE BookingItemId = @BookingItemId AND BookingRecordId = @BookingRecordId AND [Deleted] = 0 )
        INSERT INTO [booking].[BookingRecordItem]
                    (
                     [BookingItemId]
                    ,[BookingRecordId]
                    ,[CreatedOn]
                    ,[CreatedByName]
                    ,[Deleted]
                    )
        VALUES
                    (
                     @BookingItemId
                    ,@BookingRecordId
                    ,@CreatedOn
                    ,@CreatedByName
                    ,@Deleted
                    )";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
                {
                    command.AddArgument("BookingItemId", (int)itemId);
                    command.AddArgument("BookingRecordId", bookingRecordId);
                    command.AddArgument("CreatedOn", DateTimeOffset.UtcNow);
                    command.AddArgument("CreatedByName", _utils.UserFullName);
                    command.AddArgument("Deleted", false);

                    await command.Execute();
                }
            }
        }

        internal async Task RemoveBookingRecordItems(long bookingRecordId, List<long> bookingItemIds)
        {
            string sql = @"
            UPDATE   [booking].[BookingRecordItem]
            SET      [Deleted] = @Deleted,
                     [ModifiedByName] = @ModifiedByName,
                     [ModifiedOn] = @ModifiedOn
            WHERE    [BookingRecordId] = @BookingRecordId AND [BookingItemId] in (@BookingItemIds) AND [Deleted] = 0 ";

            DateTimeOffset now = DateTimeOffset.UtcNow;
            List<int> intTypeBookingItems = bookingItemIds.Select(i => (int)i).ToList();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("Deleted", true);
                command.AddArgument("ModifiedOn", now);
                command.AddArgument("ModifiedByName", _utils.UserFullName);
                command.AddArgument("BookingRecordId", bookingRecordId);
                command.AddArgument("BookingItemIds", intTypeBookingItems);

                await command.Execute();
            }
        }

    }
}
