﻿using MicroserviceBackendListing.Dtos;
using MicroserviceBackendListing.Interfaces;

using Redi.Prime3.MicroService.BaseLib;
using MicroserviceContract.Dtos.ListingManagement;
using Microsoft.Extensions.Caching.Memory;
using Sql;
using System.Collections.Specialized;
using MicroserviceContract.Dtos.Common;

namespace MicroserviceBackendListing.BusinessLogic.Base
{
    /// <summary>
    /// Manage Listing is used in the backend to manage a listing and all its associated data
    /// </summary>
    public class ManageListing : BusinessLogicBase
    {
        private UtilityFunctions _utils;
        private readonly Func<ListingMedia> _image;
        private readonly Func<ListingAttribute> _lAttribute;
        private readonly Func<StatusCode> _statusCode;
        private readonly Func<ListingType> _listingType;
        private readonly Func<Listing> _listingFactory;
        private readonly Func<Favourite> _favouriteFactory;
#pragma warning disable CS0649 // Field 'ManageListing.cloud' is never assigned to, and will always have its default value null
        private readonly ICloudStorage? cloud;
#pragma warning restore CS0649 // Field 'ManageListing.cloud' is never assigned to, and will always have its default value null
        /// <summary>
        /// 
        /// </summary>
        /// <param name="u"></param>
        /// <param name="image"></param>
        /// <param name="lAttribute"></param>
        /// <param name="utils"></param>
        /// <param name="statusCode"></param>
        /// <param name="memoryCache"></param>
        /// <param name="listingType"></param>
        /// <param name="listingFactory"></param>
        /// <param name="favouriteFactory"></param>
        /// <param name="config"></param>
        public ManageListing(IUnitOfWork u, Func<ListingMedia> image, Func<ListingAttribute> lAttribute, UtilityFunctions utils, Func<StatusCode> statusCode, IMemoryCache memoryCache, Func<ListingType> listingType, Func<Listing> listingFactory, Func<Favourite> favouriteFactory)
        {
            _utils = utils;
            _unitOfWork = u;
            _image = image;
            _lAttribute = lAttribute;
            _statusCode = statusCode;
            _memoryCache = memoryCache;
            _listingType = listingType;
            _listingFactory = listingFactory;
            _favouriteFactory = favouriteFactory;
        }

        /// <summary>
        /// Get listingId using ReferenceNo.
        /// </summary>
        /// <param name="referenceNo"></param>
        /// <param name="returnNullIfNotFound"></param>
        /// <returns></returns>
        /// <exception cref="HttpRequestException"></exception>
        internal async Task<Int64?> GetListingId(string? referenceNo, bool returnNullIfNotFound = false)
        {
            if (string.IsNullOrEmpty(referenceNo))
            {
                throw new HttpRequestException($"No listing found for referenceNo '{referenceNo}'", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            string cacheKey = "ListingReferenceNoId" + referenceNo;

            if (_memoryCache.TryGetValue(cacheKey, out Int64 cacheValue))
            {
                return cacheValue;
            }

            string sql = @"SELECT [Listing].[ListingId]
                        FROM [listing].[Listing] [Listing]
                        Inner Join [listing].[ListingType] ON [ListingType].[ListingTypeId] = [Listing].[ListingTypeId]
                                                           AND ([ListingType].[IsTenantBased] = 0 OR [Listing].[TenantId] = @TenantId )
                        WHERE [Listing].[ReferenceNo] = @ReferenceNo";
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ReferenceNo", referenceNo);
                command.AddArgument("TenantId", _utils.TenantId);
                Int64? listingId = (await command.SelectSingle<Int64?>("ListingId"));
                if (listingId != null)
                {
                    _memoryCache.Set(cacheKey, listingId, CacheOptions());
                    return listingId;
                }
            }

            if (returnNullIfNotFound) return null;

            throw new HttpRequestException($"No listing found for referenceNo '{referenceNo}'", null, System.Net.HttpStatusCode.UnprocessableEntity);
        }

        /// <summary>
        /// Get a single listing by listingId.
        /// Optionally include Media and Attributes in the response.
        /// </summary>
        /// <param name="listingId">The ListingId to fetch</param>
        /// <param name="includeMedia">Default False. Return Media (images, videos) when true</param>
        /// <param name="includeAttributes">Default False. Return Attributes (data) when true</param>
        /// <param name="displayContainerCodesArray"></param>
        /// <param name="excludeAttributesWithNoData"></param>
        /// <param name="displayGroupCode"></param>
        /// <param name="flattenToFields"></param>
        /// <param name="includeTagsInResponse"></param>
        /// <returns></returns>
        internal async Task<ManageListingWithDisplayGroupedDataAndMediaCDto> GetListingByListingId(long listingId, bool includeMedia = false, bool includeAttributes = false, string[]? displayContainerCodesArray = null, bool? excludeAttributesWithNoData = true, string? displayGroupCode = "", bool flattenToFields = false, bool includeTagsInResponse = true)
        {
            string sql = @"SELECT [Listing].[ListingId], 
                        [Listing].[ReferenceNo], 
                        [Listing].[Subject], 
                        [Listing].[StatusCode], 
                        [Listing].[VisibilityId],
                        [Listing].[ListingTypeId], 
                        [Listing].[ProfileListingMediaId], 
                        [ListingMedia].[MediaUrl] as ProfileListingMediaUrl,
                        [Listing].[ParentEntityId],
                        [Listing].[ParentEntityIntId],
                        [Listing].[ParentEntityType],    
                        [Listing].[ParentEntityId2],
                        [Listing].[ParentEntityType2],  
                        [Listing].[ParentListingId],
                        [Listing].[Icon],
                        [Listing].[SortOrder],
                        [Listing].[FromDate], 
                        [Listing].[ToDate],
                        [Listing].[Description],
                        [Listing].[CreatedOn],
                        [Listing].[CreatedByName],
                        [Listing].[ModifiedOn],
                        [Listing].[ModifiedByName],
                        [Listing].[GpsLocation].Lat AS Latitude,
                        [Listing].[GpsLocation].Long AS Longitude
                        FROM [listing].[Listing] [Listing]
                        Inner Join [listing].[ListingType] ON [ListingType].[ListingTypeId] = [Listing].[ListingTypeId]
                                                           AND ([ListingType].[IsTenantBased] = 0 OR [Listing].[TenantId] = @TenantId )
                        LEFT JOIN [listing].[ListingMedia] ON [Listing].[ProfileListingMediaId] = [ListingMedia].[ListingMediaId]
                        WHERE [Listing].[ListingId] = @ListingId";
            var response = new ManageListingWithDisplayGroupedDataAndMediaCDto();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ListingId", listingId);
                command.AddArgument("TenantId", _utils.TenantId);
                response = await command.SelectSingle<ManageListingWithDisplayGroupedDataAndMediaCDto>();
            }
            if (response == null || response.ListingId == null)
            {
                return response;
            }
            if (includeMedia)
            {
                response.Media = await _image().GetListingListMediaForListingId((long)response.ListingId);
            }
            if (includeAttributes)
            {
                List<ManageListingDisplayContainerCDto> DisplayContainers = await GetAttributeDataAsync(listingId, displayContainerCodesArray, excludeAttributesWithNoData, displayGroupCode);
                response.Attributes = DisplayContainers;
                if (flattenToFields == true)
                {
                    FlattenToFieldsInsteadOfAttributes(response);
                    response.Attributes = null;
                }
            }
            if (includeTagsInResponse == true)
            {
                // Get all Tags linked to the listing and return
                var tags = await _listingFactory().GetListingTags((long)response.ListingId);
                response.Tags = tags;
            }
            if (response.ParentListingId != null)
            {
                response.ParentListing = await _listingFactory().GetSingleListing(response.ParentListingId.Value, includeTagsInResponse: includeTagsInResponse);
            }

            return response;
        }

        internal void FlattenToFieldsInsteadOfAttributes(ManageListingWithDisplayGroupedDataAndMediaCDto dto)
        {
            dto.Fields = new Dictionary<string, object?>();
            if (dto.Attributes == null) { return; }
            foreach (var dc in dto.Attributes)
            {
                if (dc.Attributes == null) { continue; }
                foreach (var att in dc.Attributes)
                {
                    if (att != null && att.AttributeCode != null)
                    {
                        string attName = $"{Char.ToLowerInvariant(att.AttributeCode[0]) + att.AttributeCode.Substring(1)}";
                        object? val = null;
                        switch (att.AttributeValueTypeCode)
                        {
                            case "ValueString":
                                val = att.ValueString;
                                break;
                            case "ValueStringMax":
                                val = att.ValueStringMax;
                                break;
                            case "ValueNumeric":
                                val = att.ValueNumeric;
                                break;
                            case "ValueDateTime":
                                val = att.ValueDateTime;
                                break;
                            case "ValueGeography":
                                val = att.ValueGeography;
                                break;
                            default:
                                break;
                        }
                        if (!dto.Fields.ContainsKey(attName))
                        {
                            dto.Fields.Add(attName, val);
                        }
                    }
                }
            }
        }

        internal void FlattenToFieldsInsteadOfAttributes(ManageListingWithDataAndMediaCDto dto)
        {
            dto.Fields = new Dictionary<string, object?>();
            if (dto == null || dto.Attributes == null) { return; }
            foreach (var att in dto.Attributes)
            {
                if (att != null && att.AttributeCode != null)
                {
                    string attName = $"{Char.ToLowerInvariant(att.AttributeCode[0]) + att.AttributeCode.Substring(1)}";
                    object? val = null;
                    switch (att.AttributeValueTypeCode)
                    {
                        case "ValueString":
                            val = att.ValueString;
                            break;
                        case "ValueStringMax":
                            val = att.ValueStringMax;
                            break;
                        case "ValueNumeric":
                            val = att.ValueNumeric;
                            break;
                        case "ValueDateTime":
                            val = att.ValueDateTime;
                            break;
                        case "ValueGeography":
                            val = att.ValueGeography;
                            break;
                        default:
                            break;
                    }
                    if (!dto.Fields.ContainsKey(attName))
                    {
                        dto.Fields.Add(attName, val);
                    }
                }
            }
        }

        /// <summary>
        /// Returns all Attributes for a Listing.
        /// </summary>
        /// <param name="ListingId"></param>
        /// <returns></returns>
        internal async Task<List<ManageListingAttributeCDto>> GetListingDataAsync(long ListingId)
        {
            return await _lAttribute().GetListingAttibutesbyId(ListingId);
        }


        internal Dictionary<string, object?> FlattenListingData(List<ManageListingAttributeCDto> data)
        {
            var response = new Dictionary<string, object?>();
            if (data == null) { return response; }
            foreach (var att in data)
            {
                if (att != null && att.AttributeCode != null)
                {
                    string attName = $"{Char.ToLowerInvariant(att.AttributeCode[0]) + att.AttributeCode.Substring(1)}";
                    object? val = null;
                    switch (att.AttributeValueTypeCode)
                    {
                        case "ValueString":
                            val = att.ValueString;
                            break;
                        case "ValueStringMax":
                            val = att.ValueStringMax;
                            break;
                        case "ValueNumeric":
                            val = att.ValueNumeric;
                            break;
                        case "ValueDateTime":
                            val = att.ValueDateTime;
                            break;
                        case "ValueGeography":
                            val = att.ValueGeography;
                            break;
                        default:
                            break;
                    }
                    if (!response.ContainsKey(attName))
                    {
                        response.Add(attName, val);
                    }
                }
            }
            return response;
        }

        internal async Task<ListResponseDto<ManageListingWithDataAndMediaCDto>> GetListAsync(StandardListParameters standardListParameters, string? query, string[]? category, Int64? parentEntityIntId, Guid? parentEntityId, Guid? parentEntityId2, Int64? parentListingId, string[] returnAttributeCodes, string? subject, bool includeMedia = false, string? statusCode = null, string? mediaCategoryCode = null, short listingTypeId = 0, int? tenantId = null, bool flattenToFields = false)
        {
            StringDictionary canBeSortedBy = new StringDictionary()
                         {
                             { "CreatedOn", "[Listing].[CreatedOn]" },
                             { "StatusCode", "[Listing].[StatusCode]" },
                             { "SortOrder", "[Listing].[SortOrder]" },
                             { "ModifiedOn", "[Listing].[ModifiedOn]" },
                             { "FromDate", "[Listing].[FromDate]" },
                             { "ToDate", "[Listing].[ToDate]" },
                             { "ReferenceNo", "[Listing].[ReferenceNo]" },
                             { "CreatedByName", "[Listing].[CreatedByName]" },
                             { "ModifiedByName", "[Listing].[ModifiedByName]" },
                             { "Subject", "[Listing].[Subject]" },
                             { "ListingStatusLabel", "[ListingStatus].[Label]" },
                             { "VisibilityLabel", "[Visibility].[Label]" },
                             { "ListingTypeLabel", "[ListingType].[Label]" },
                             { "ParentEntityName", "[ParentEntityName]" },
                             { "ParentEntityName2", "[ParentEntityName2]" },
                             { "ParentListingSubject", "[ParentListing].[Subject]" }
                             // Plus any requested Attribute Codes can be sorted by (where not many allowed)
                         };

            string extraCols = "";
            Dictionary<string, object> queryParameters = new Dictionary<string, object>();
            string sqlJoin = "";
            string sqlWhere = "";

            if (returnAttributeCodes.Length > 0)
            {
                // Include the requested attributes in the sql as Left Joins or Sub-Selects.
                int i = 0;
                foreach (var attCode in returnAttributeCodes)
                {
                    i++;
                    var att = await GetAttribute(attCode);
                    if (att == null)
                    {
                        throw new HttpRequestException($"Requested returnAttributeCodes does not exist '{attCode}'", null, System.Net.HttpStatusCode.UnprocessableEntity);
                    }

                    if (att.IsManyAllowed == false)
                    {
                        if (att.AttributeValueTypeCode == "ValueGeography")
                        {
                            extraCols += $",[Attr{att.AttributeCode}].[{att.AttributeValueTypeCode}].[Lat] AS [Attr{att.AttributeCode}ValueLat] ";
                            extraCols += $",[Attr{att.AttributeCode}].[{att.AttributeValueTypeCode}].[Long] AS [Attr{att.AttributeCode}ValueLong] ";
                            canBeSortedBy.Add(attCode, $"[Attr{att.AttributeCode}].[{att.AttributeValueTypeCode}].[Lat]");
                        }
                        else
                        {
                            extraCols += $",[Attr{att.AttributeCode}].[{att.AttributeValueTypeCode}] AS [Attr{att.AttributeCode}Value] ";
                            canBeSortedBy.Add(attCode, $"[Attr{att.AttributeCode}].[{att.AttributeValueTypeCode}]");
                        }
                        sqlJoin += $" LEFT JOIN [listing].[ListingAttribute] [Attr{att.AttributeCode}] ON [Attr{att.AttributeCode}].[ListingId] = [Listing].[ListingId] AND [Attr{att.AttributeCode}].[AttributeCode] = @AttributeCode{att.AttributeCode} ";
                    }
                    else
                    {
                        extraCols += $",STUFF((Select ',' + CAST(AA.[{att.AttributeValueTypeCode}] AS VARCHAR(MAX)) From [Listing].[ListingAttribute] [AA] Where [Listing].ListingId = AA.ListingId AND AA.[AttributeCode] = @AttributeCode{att.AttributeCode}  FOR XML PATH('')),1,1,'') AS [Attr{att.AttributeCode}Value] ";
                    }
#pragma warning disable CS8604 // Possible null reference argument.
                    queryParameters.Add($"AttributeCode{att.AttributeCode}", att.AttributeCode);
#pragma warning restore CS8604 // Possible null reference argument.
                }
            }

            if (includeMedia)
            {
                extraCols += $",STUFF((Select '^' + LM.[MediaUrl] + '|' + LM.[Title] + '|' + LM.[MediaTypeCode] + '|' + CONVERT(nvarchar, LM.[ListingMediaId]) From [Listing].[ListingMedia] [LM] Where [Listing].ListingId = LM.ListingId AND LM.[Deleted] = 0 {(!string.IsNullOrEmpty(mediaCategoryCode) ? " AND LM.[MediaCategoryCode] = @MediaCategoryCode " : "")} ORDER By LM.[SortOrder]  FOR XML PATH('')),1,1,'') AS [MediaSet] ";
                if (!string.IsNullOrEmpty(mediaCategoryCode))
                {
                    queryParameters.Add($"MediaCategoryCode", mediaCategoryCode);
                }
            }

            if(_utils.TenantId != null)
            {
                queryParameters.Add("TenantId", _utils.TenantId);
            } else if(tenantId != null)
            {
                queryParameters.Add("TenantId", tenantId);
            }

            string sql = @$"
                SELECT [Listing].[ListingId], 
                [Listing].[ReferenceNo], 
                [Listing].[Subject], 
                [Listing].[StatusCode], 
                [Listing].[ProfileListingMediaId], 
                [Listing].[ListingTypeId], 
                [Listing].[VisibilityId],
                [Listing].[Description],
                [ListingMedia].[MediaUrl] as ProfileListingMediaUrl,
                [Listing].[ParentEntityId],
                [Listing].[ParentEntityIntId],
                [Listing].[ParentEntityType],    
                [Listing].[ParentEntityId2],
                [Listing].[ParentEntityType2], 
                [Listing].[ParentListingId],
                [Listing].[Icon],
                [Listing].[SortOrder],
                [Listing].[FromDate], 
                [Listing].[ToDate],
                [Listing].[CreatedOn],
                [Listing].[CreatedByName],
                [Listing].[GpsLocation].Lat AS Latitude,
                [Listing].[GpsLocation].Long AS Longitude,
                [Listing].[ModifiedOn],
                [Listing].[ModifiedByName],
                [Listing].[Icon],
                [ListingStatus].[Label] AS ListingStatusLabel,
                [Visibility].[Label] AS VisibilityLabel,
                [ListingType].[Label] AS ListingTypeLabel,
                [ParentListing].[Subject] AS ParentListingSubject,
                CASE
                     {(ConfigBase.Schemas.ContainsKey("users") ? "WHEN [Listing].[ParentEntityType] = 'User' THEN [ListingUser].[FullName]" : "")}
                     {(ConfigBase.Schemas.ContainsKey("crm") ? "WHEN [Listing].[ParentEntityType] = 'Party' THEN [ListingParty].[DisplayName]" : "")}
                     {(ConfigBase.Schemas.ContainsKey("order") ? "WHEN [Listing].[ParentEntityType] = 'Order' THEN [ListingOrder].[OrderReference]" : "")}
                     ELSE [Listing].[CreatedByName]
                END AS [ParentEntityName],
                CASE
                     {(ConfigBase.Schemas.ContainsKey("users") ? "WHEN [Listing].[ParentEntityType2] = 'User' THEN [ListingUser2].[FullName]" : "")}
                     {(ConfigBase.Schemas.ContainsKey("crm") ? "WHEN [Listing].[ParentEntityType2] = 'Party' THEN [ListingParty2].[DisplayName]" : "")}
                     {(ConfigBase.Schemas.ContainsKey("order") ? "WHEN [Listing].[ParentEntityType2] = 'Order' THEN [ListingOrder2].[OrderReference]" : "")}
                     ELSE [Listing].[CreatedByName]
                END AS [ParentEntityName2]
                {extraCols}
                FROM [listing].[Listing] [Listing]
                Inner Join [listing].[ListingType] ON [ListingType].[ListingTypeId] = [Listing].[ListingTypeId]
                                                           AND ([ListingType].[IsTenantBased] = 0 OR [Listing].[TenantId] = @TenantId )
                LEFT JOIN [listing].[ListingMedia] ON [Listing].[ProfileListingMediaId] = [ListingMedia].[ListingMediaId]
                LEFT JOIN [listing].[ListingStatus] ON [Listing].[StatusCode] = [ListingStatus].[StatusCode]
                LEFT JOIN [listing].[Visibility] ON [Listing].[VisibilityId] = [Visibility].[VisibilityId]
                LEFT JOIN [listing].[Listing] [ParentListing] ON [Listing].[ParentListingId] = [ParentListing].[ListingId]
                {(ConfigBase.Schemas.ContainsKey("crm") ? "LEFT JOIN [crm].[Party] [ListingParty] ON [ListingParty].[PartyId] = [Listing].[ParentEntityId] AND [Listing].[ParentEntityType] = 'Party'" : "")}
                {(ConfigBase.Schemas.ContainsKey("users") ? "LEFT JOIN [users].[User] [ListingUser] ON [ListingUser].[UserId] = [Listing].[ParentEntityId] AND [Listing].[ParentEntityType] = 'User'" : "")} 
                {(ConfigBase.Schemas.ContainsKey("order") ? "LEFT JOIN [order].[Order] [ListingOrder] ON [ListingOrder].[OrderId] = [Listing].[ParentEntityId] AND [Listing].[ParentEntityType] = 'Order'" : "")}
                {(ConfigBase.Schemas.ContainsKey("crm") ? "LEFT JOIN [crm].[Party] [ListingParty2] ON [ListingParty2].[PartyId] = [Listing].[ParentEntityId2] AND [Listing].[ParentEntityType2] = 'Party'" : "")}
                {(ConfigBase.Schemas.ContainsKey("users") ? "LEFT JOIN [users].[User] [ListingUser2] ON [ListingUser2].[UserId] = [Listing].[ParentEntityId2] AND [Listing].[ParentEntityType2] = 'User'" : "")} 
                {(ConfigBase.Schemas.ContainsKey("order") ? "LEFT JOIN [order].[Order] [ListingOrder2] ON [ListingOrder2].[OrderId] = [Listing].[ParentEntityId2] AND [Listing].[ParentEntityType2] = 'Order'" : "")}
                {sqlJoin}";
            sqlWhere = " WHERE [Listing].[ListingTypeId] = @ListingTypeId ";

            if (!parentEntityId.GuidIsNullOrEmpty() && parentEntityId != null)
            {
                sqlWhere += $" AND [Listing].[ParentEntityId] = @ParentEntityId ";
                queryParameters.Add($"ParentEntityId", (Guid)parentEntityId);
            }
            if (parentEntityIntId != null)
            {
                sqlWhere += $" AND [Listing].[ParentEntityIntId] = @ParentEntityIntId ";
                queryParameters.Add($"ParentEntityIntId", (int)parentEntityIntId);
            }
            if (statusCode != null)
            {
                sqlWhere += $" AND [Listing].[StatusCode] = @StatusCode ";
                queryParameters.Add($"StatusCode", statusCode);
            }
            if (query != null)
            {
                sqlWhere += $" AND ([Listing].[Description] LIKE '%' + @Query + '%' ";
                queryParameters.Add($"Query", query);
                var index = returnAttributeCodes.ToList().IndexOf("OEMPartNumber");
                if (index >= 0)
                {
                    // index starts at 1 for some reason
                    sqlWhere += $" OR [AttrOEMPartNumber].[ValueString] LIKE '%' + @Query + '%') ";
                }
                else
                {
                    sqlWhere += $" ) ";
                }
            }
            if (!string.IsNullOrEmpty(subject))
            {
                sqlWhere += " AND [Listing].[Subject] like '%' + @Subject + '%'";
                queryParameters.Add("Subject", subject);
            }
            if (category != null && category.Length > 0)
            {
                var index = returnAttributeCodes.ToList().IndexOf("Category");
                if (index >= 0)
                {
                    sqlWhere += $" AND [AttrCategory].[ValueString] IN ( ";
                    for (int i = 0, ilen = category.Length; i < ilen; i++)
                    {
                        // index starts at 1 for some reason
                        sqlWhere += $" @Category_{i} ";
                        if (i < ilen - 1) { sqlWhere += $" , "; }
                        queryParameters.Add($"Category_{i}", category[i]);
                    }
                    sqlWhere += $" ) ";
                }
            }
            //else
            //{
            //    if (parentEntityId2.GuidIsNullOrEmpty())
            //    {
            //        sqlWhere += $" AND [Listing].[ParentEntityId2] is null ";
            //    }
            //}
            if (!parentEntityId2.GuidIsNullOrEmpty() && parentEntityId2 != null)
            {
                sqlWhere += $" AND [Listing].[ParentEntityId2] = @ParentEntityId2 ";
                queryParameters.Add($"ParentEntityId2", (Guid)parentEntityId2);
            }

            if (parentListingId != null)
            {
                sqlWhere += $" AND [Listing].[ParentListingId] = @ParentListingId ";
                queryParameters.Add($"ParentListingId", (Int64)parentListingId);
            }

            sqlWhere += $" AND [Listing].[Deleted] = {(!standardListParameters.IsDeleted.HasValue || (bool)standardListParameters.IsDeleted == false ? "0" : "1")} ";

            sql += sqlWhere;

            var validSortOptionsDb = await GetSortOptions();
            foreach (var sr in validSortOptionsDb)
            {
                canBeSortedBy.Add(sr.SortOptionId.ToString(), sr.SqlSortStmt);
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "-CreatedOn");

            sql += $" OFFSET @offset ROWS";
            sql += $" FETCH NEXT @limit ROWS ONLY";

            queryParameters.Add($"ListingTypeId", listingTypeId);

            var result = new ListResponseDto<ManageListingWithDataAndMediaCDto>();
            result.List = new List<ManageListingWithDataAndMediaCDto>();

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                if (queryParameters.Count > 0)
                {
                    foreach (var qp in queryParameters)
                    {
                        command.AddArgument(qp.Key, qp.Value);
                    }
                }
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);

                using (var reader = await command.SelectRaw())
                {
                    while (reader.Read())
                    {
                        ManageListingWithDataAndMediaCDto rec = new ManageListingWithDataAndMediaCDto();
                        rec.ListingId = (reader.GetInt64(reader.GetOrdinal("ListingId")));
                        rec.ReferenceNo = (reader.GetString(reader.GetOrdinal("ReferenceNo")));
                        rec.Subject = (reader.GetString(reader.GetOrdinal("Subject")));
                        rec.ListingTypeId = (reader.GetByte(reader.GetOrdinal("ListingTypeId")));
                        rec.VisibilityId = (reader.GetByte(reader.GetOrdinal("VisibilityId")));
                        rec.Description = (reader.GetString(reader.GetOrdinal("Description")));
                        rec.StatusCode = (reader.GetString(reader.GetOrdinal("StatusCode")));
                        rec.ProfileListingMediaId = !reader.IsDBNull(reader.GetOrdinal("ProfileListingMediaId")) ? (reader.GetInt64(reader.GetOrdinal("ProfileListingMediaId"))) : null;
                        rec.ProfileListingMediaUrl = !reader.IsDBNull(reader.GetOrdinal("ProfileListingMediaUrl")) ? (reader.GetString(reader.GetOrdinal("ProfileListingMediaUrl"))) : null;
                        rec.ParentEntityIntId = !reader.IsDBNull(reader.GetOrdinal("ParentEntityIntId")) ? (reader.GetInt64(reader.GetOrdinal("ParentEntityIntId"))) : null;
                        rec.ParentEntityId = !reader.IsDBNull(reader.GetOrdinal("ParentEntityId")) ? (reader.GetGuid(reader.GetOrdinal("ParentEntityId"))) : null;
                        rec.ParentEntityType = !reader.IsDBNull(reader.GetOrdinal("ParentEntityType")) ? (reader.GetString(reader.GetOrdinal("ParentEntityType"))) : null;
                        rec.ParentEntityId2 = !reader.IsDBNull(reader.GetOrdinal("ParentEntityId2")) ? (reader.GetGuid(reader.GetOrdinal("ParentEntityId2"))) : null; ;
                        rec.ParentEntityType2 = !reader.IsDBNull(reader.GetOrdinal("ParentEntityType2")) ? (reader.GetString(reader.GetOrdinal("ParentEntityType2"))) : null; 
                        rec.SortOrder = !reader.IsDBNull(reader.GetOrdinal("SortOrder")) ? (reader.GetInt32(reader.GetOrdinal("SortOrder"))) : null;
                        rec.ParentListingId = !reader.IsDBNull(reader.GetOrdinal("ParentListingId")) ? (reader.GetInt64(reader.GetOrdinal("ParentListingId"))) : null;
                        rec.Icon = !reader.IsDBNull(reader.GetOrdinal("Icon")) ? (reader.GetString(reader.GetOrdinal("Icon"))) : null;
                        rec.FromDate = (reader.GetDateTimeOffset(reader.GetOrdinal("FromDate")));
                        rec.ToDate = !reader.IsDBNull(reader.GetOrdinal("ToDate")) ? (reader.GetDateTimeOffset(reader.GetOrdinal("ToDate"))) : null;
                        rec.CreatedOn = (reader.GetDateTimeOffset(reader.GetOrdinal("CreatedOn")));
                        rec.CreatedByName = !reader.IsDBNull(reader.GetOrdinal("CreatedByName")) ? (reader.GetString(reader.GetOrdinal("CreatedByName"))) : null;
                        rec.ModifiedOn = !reader.IsDBNull(reader.GetOrdinal("ModifiedOn")) ? (reader.GetDateTimeOffset(reader.GetOrdinal("ModifiedOn"))) : null;
                        rec.ModifiedByName = !reader.IsDBNull(reader.GetOrdinal("ModifiedByName")) ? (reader.GetString(reader.GetOrdinal("ModifiedByName"))) : null;
                        rec.ListingStatusLabel = !reader.IsDBNull(reader.GetOrdinal("ListingStatusLabel")) ? (reader.GetString(reader.GetOrdinal("ListingStatusLabel"))) : null; 
                        rec.VisibilityLabel = !reader.IsDBNull(reader.GetOrdinal("VisibilityLabel")) ? (reader.GetString(reader.GetOrdinal("VisibilityLabel"))) : null; 
                        rec.ParentEntityName = !reader.IsDBNull(reader.GetOrdinal("ParentEntityName")) ? (reader.GetString(reader.GetOrdinal("ParentEntityName"))) : null; 
                        rec.ParentEntityName2 = !reader.IsDBNull(reader.GetOrdinal("ParentEntityName2")) ? (reader.GetString(reader.GetOrdinal("ParentEntityName2"))) : null;
                        rec.ListingTypeLabel = !reader.IsDBNull(reader.GetOrdinal("ListingTypeLabel")) ? (reader.GetString(reader.GetOrdinal("ListingTypeLabel"))) : null;
                        rec.ParentListingSubject = !reader.IsDBNull(reader.GetOrdinal("ParentListingSubject")) ? (reader.GetString(reader.GetOrdinal("ParentListingSubject"))) : null;
                        rec.Icon = !reader.IsDBNull(reader.GetOrdinal("Icon")) ? (reader.GetString(reader.GetOrdinal("Icon"))) : null;

                        if (!reader.IsDBNull(reader.GetOrdinal("Latitude")) && !reader.IsDBNull(reader.GetOrdinal("Longitude")))
                        {
                            rec.GpsLocation = new GpslocationCDto()
                            {
                                Latitude = (decimal)reader.GetDouble(reader.GetOrdinal("Latitude"))
                                                                     ,
                                Longitude = (decimal)reader.GetDouble(reader.GetOrdinal("Longitude"))
                            };
                        }

                        if (includeMedia)
                        {
                            // ListingMedia was included in the select concatenated into a single result per Listing Row. Split out to rows in result.
                            rec.Media = new List<ManageListingMediaCDto>();
                            string? mediaSet = !reader.IsDBNull(reader.GetOrdinal("MediaSet")) ? (reader.GetString(reader.GetOrdinal("MediaSet"))) : null;
                            var mediaRecs = new string[] { };
                            if (mediaSet != null)
                            {
                                mediaRecs = mediaSet.Split('^');
                                foreach (var m in mediaRecs)
                                {
                                    var recSplit = m.Split('|');
                                    rec.Media.Add(new ManageListingMediaCDto() { Title = recSplit[1], MediaUrl = recSplit[0], MediaTypeCode = recSplit[2], ListingMediaId = Int64.Parse(recSplit[3]) });
                                }
                            }


                        }

                        // Attributes were included in the main listing select with each attribute read as its own column.
                        rec.Attributes = new List<ManageListingAttributeCDto>();
                        int i = 0;
                        foreach (var attCode in returnAttributeCodes)
                        {
                            i++;
                            var attrDef = await GetAttribute(attCode);
                            ManageListingAttributeCDto theAttrRec = new ManageListingAttributeCDto()
                            {
                                AttributeCode = attrDef.AttributeCode,
                                AttributeValueTypeCode = attrDef.AttributeValueTypeCode,
                                InputTypeCode = attrDef.InputTypeCode,
                                Label = attrDef.Label,
                                AttributeGroupCode = attrDef.AttributeGroupCode,
                                IsManyAllowed = attrDef.IsManyAllowed,
                                NumericDecimalPlaces = attrDef.NumericDecimalPlaces,
                                IsRequired = attrDef.IsRequired,
                                NumericMinValue = attrDef.NumericMinValue,
                                NumericMaxValue = attrDef.NumericMaxValue,
                                TextMinCharacters = attrDef.TextMinCharacters,
                                TextMaxCharacters = attrDef.TextMaxCharacters,
                                MinDateDaysFromToday = attrDef.MinDateDaysFromToday,
                                MaxDateDaysFromToday = attrDef.MaxDateDaysFromToday,
                                MinSelectionsRequired = attrDef.MinSelectionsRequired,
                                MaxSelectionsAllowed = attrDef.MaxSelectionsAllowed,
                                MaxStars = attrDef.MaxStars,
                                HelpText = attrDef.HelpText
                            };

                            if (attrDef != null)
                            {
                                switch (attrDef.AttributeValueTypeCode)
                                {
                                    case "ValueString":
                                        theAttrRec.ValueString = !reader.IsDBNull(reader.GetOrdinal($"Attr{attrDef.AttributeCode}Value")) ? (reader.GetString(reader.GetOrdinal($"Attr{attrDef.AttributeCode}Value"))) : null;
                                        break;
                                    case "ValueStringMax":
                                        theAttrRec.ValueStringMax = !reader.IsDBNull(reader.GetOrdinal($"Attr{attrDef.AttributeCode}Value")) ? (reader.GetString(reader.GetOrdinal($"Attr{attrDef.AttributeCode}Value"))) : null;
                                        break;
                                    case "ValueNumeric":
                                        var stringNumeric = !reader.IsDBNull(reader.GetOrdinal($"Attr{attrDef.AttributeCode}Value")) ? (reader.GetString(reader.GetOrdinal($"Attr{attrDef.AttributeCode}Value"))) : null;
                                        if (!string.IsNullOrEmpty(stringNumeric))
                                        {
                                            if (Decimal.TryParse(stringNumeric, out decimal numeric))
                                            {
                                                theAttrRec.ValueNumeric = Extensions.DecimalTrimPlaces(numeric, theAttrRec.NumericDecimalPlaces);
                                            }
                                        }
                                        theAttrRec.ValueNumeric = theAttrRec.ValueNumeric ?? 0;
                                        break;
                                    case "ValueDateTime":
                                        theAttrRec.ValueDateTime = !reader.IsDBNull(reader.GetOrdinal($"Attr{attrDef.AttributeCode}Value")) ? (reader.GetDateTimeOffset(reader.GetOrdinal($"Attr{attrDef.AttributeCode}Value"))) : null;
                                        break;
                                    case "ValueGeography":
                                        if (!reader.IsDBNull(reader.GetOrdinal($"Attr{attrDef.AttributeCode}ValueLat")) && !reader.IsDBNull(reader.GetOrdinal($"Attr{attrDef.AttributeCode}ValueLong")))
                                        {
                                            theAttrRec.ValueGeography = new GpslocationCDto()
                                            {
                                                Latitude = (decimal)reader.GetDouble(reader.GetOrdinal($"Attr{attrDef.AttributeCode}ValueLat"))
                                                                                               ,
                                                Longitude = (decimal)reader.GetDouble(reader.GetOrdinal($"Attr{attrDef.AttributeCode}ValueLong"))
                                            };
                                        }
                                        break;
                                    default:
                                        break;
                                }
                                rec.Attributes.Add(theAttrRec);
                            }
                        }

                        if (flattenToFields == true)
                        {
                            FlattenToFieldsInsteadOfAttributes(rec);
                            rec.Attributes = null;
                        }

                        result.List.Add(rec);
                    }
                }
            }

            sql = @$"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [listing].[Listing] [Listing]
                Inner Join [listing].[ListingType] ON [ListingType].[ListingTypeId] = [Listing].[ListingTypeId]
                                                           AND ([ListingType].[IsTenantBased] = 0 OR [Listing].[TenantId] = @TenantId )
                LEFT JOIN [listing].[ListingMedia] ON [Listing].[ProfileListingMediaId] = [ListingMedia].[ListingMediaId]
                {sqlJoin}
                {sqlWhere}";
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                if (queryParameters.Count > 0)
                {
                    foreach (var qp in queryParameters)
                    {
                        command.AddArgument(qp.Key, qp.Value);
                    }
                }

                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }
            return result;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="referenceNo"></param>
        /// <param name="isNext"></param>
        /// <param name="includeMedia"></param>
        /// <param name="includeAttributes"></param>
        /// <param name="displayContainerCodesArray"></param>
        /// <param name="excludeAttributesWithNoData"></param>
        /// <param name="displayGroupCode"></param>
        /// <param name="includeTagsInResponse"></param>
        /// <returns></returns>
        internal async Task<ManageListingWithDisplayGroupedDataAndMediaCDto?> GetNextListingByRefAsync(string referenceNo, bool isNext = true, bool includeMedia = false, bool includeAttributes = false, string[]? displayContainerCodesArray = null, bool? excludeAttributesWithNoData = true, string? displayGroupCode = "", bool includeTagsInResponse = true)
        {
            var sql = @$"
            DECLARE @currentCreatedOn DATETIMEOFFSET;
            DECLARE @sortOrder INT;
            DECLARE @listingTypeId INT; 
            DECLARE @listingId BIGINT;

            SELECT TOP 1
                @currentCreatedOn = [Listing].[CreatedOn], 
                @sortOrder = [Listing].[SortOrder], 
                @listingTypeId = [Listing].[ListingTypeId]
            FROM [listing].[Listing] [Listing]
            WHERE [Listing].[ReferenceNo] = @referenceNo AND (@tenantId IS NULL OR [Listing].[TenantId] = @tenantId)

            SELECT TOP 1 @listingId = [Listing].[ListingId]
            FROM [listing].[Listing] [Listing]
            WHERE [Listing].[CreatedOn] {(isNext ? ">" : "<")} @currentCreatedOn AND [Listing].[SortOrder] {(isNext ? ">=" : "<=")} @sortOrder 
            AND [Listing].[Deleted] = 0
            AND [Listing].[ListingTypeId] = @listingTypeId 
            AND (@tenantId IS NULL OR [Listing].[TenantId] = @tenantId)
            ORDER BY {(isNext ? "[Listing].[CreatedOn], [Listing].[SortOrder]" : "[Listing].[CreatedOn] DESC, [Listing].[SortOrder] DESC")}

            {(isNext ? (
            @"IF @@ROWCOUNT = 0
            BEGIN
                SELECT TOP 1 @listingId = [Listing].[ListingId]
                FROM [listing].[Listing] [Listing]             
				WHERE [Listing].[ListingTypeId] = @listingTypeId 
                AND [Listing].[Deleted] = 0
                AND (@tenantId IS NULL OR [Listing].[TenantId] = @tenantId)
            ORDER BY [Listing].[CreatedOn], [Listing].[SortOrder]
            END

            SELECT @listingId AS ListingId
            ") : (
            @"IF @@ROWCOUNT = 0
            BEGIN
                SELECT TOP 1 @listingId = [Listing].[ListingId]
                FROM [listing].[Listing][Listing]

                WHERE [Listing].[ListingTypeId] = @listingTypeId
                AND [Listing].[Deleted] = 0
                AND(@tenantId IS NULL OR[Listing].[TenantId] = @tenantId)
            ORDER BY[Listing].[CreatedOn] DESC, [Listing].[SortOrder] DESC
            END

            SELECT @listingId AS ListingId
            "))}
";

            Int64? listingId = null;
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("referenceNo", referenceNo);
                command.AddArgument("tenantId", _utils.TenantId);
                using (var reader = await command.SelectRaw())
                {
                    while (reader.Read())
                    {
                        listingId = !reader.IsDBNull(reader.GetOrdinal("ListingId")) ? (reader.GetInt64(reader.GetOrdinal("ListingId"))) : null;
                    }
                }
            }
            if (listingId == null) { return null; }
            var dto = await GetListingByListingId((Int64)listingId, includeMedia, includeAttributes, displayContainerCodesArray, excludeAttributesWithNoData, displayGroupCode, includeTagsInResponse: includeTagsInResponse);
            return dto;

        }

        internal async Task<List<SortOptionsDto>> GetSortOptions()
        {
            string cacheKey = "SortOptions";

            if (_memoryCache.TryGetValue(cacheKey, out List<SortOptionsDto>? cacheValue) && cacheValue != null)
            {
                var cacheResp = new List<SortOptionsDto>();
                foreach (var rec in cacheValue)
                {
                    cacheResp.Add(rec.DeepClone());
                }
                return cacheResp;
            }

            string sql = @$"
                        SELECT 
                        [SortOptions].[SortOptionId], 
                        [SortOptions].[Label],                         
                        [SortOptions].[DisplayContainerCode],
                        [SortOptions].[SqlSortStmt],
                        [SortOptions].[SortOrder],
                        [SortOptions].[IsEnabled]
                        FROM [listing].[SortOptions] [SortOptions] 
                        WHERE [SortOptions].[IsEnabled] = 1 
                        ORDER By [SortOptions].[SortOrder] ";
            var result = new List<SortOptionsDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                result = await command.SelectMany<SortOptionsDto>();
            }

            _memoryCache.Set(cacheKey, result.DeepClone(), CacheOptions());

            return result;
        }

        /// <summary>
        /// Create a new Listing Record
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="supportOfflineReferenceCreate">
        /// Default false. When true this allows the front-end to generate a unqiue referenceNo that is used to indentify the listing.\
        /// The primary purpose is for offline use when a listing is created by a front-end that can operate offline
        /// </param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        internal async Task<Int64> CreateNewListing(ManageListingWithDisplayGroupedDataAndMediaCDto dto, bool supportOfflineReferenceCreate = false)
        {
            if (dto.StatusCode == null)
            {
                dto.StatusCode = "Draft";
            }
            if (dto.ListingId != null)
            {
                var exists = await GetListingByListingId((long)dto.ListingId, includeTagsInResponse: false);
                if (exists != null)
                {
                    await UpdateListingWithDisplayContainers(dto);
                    return (long)dto.ListingId;
                }
            }
            else if (supportOfflineReferenceCreate && !string.IsNullOrEmpty(dto.ReferenceNo))
            {
                // Try to find listing by its ReferenceNo
                var x = await GetListingId(dto.ReferenceNo, true);
                if (x != null)
                {
                    var exists = await GetListingByListingId((Int64)x, includeTagsInResponse: false);
                    if (exists != null)
                    {
                        dto.ListingId = (Int64)x;
                        await UpdateListingWithDisplayContainers(dto);
                        return (long)dto.ListingId;
                    }
                }
            }
            else if (!string.IsNullOrEmpty(dto.ReferenceNo))
            {
                throw new HttpRequestException($"ReferenceNo '{dto.ReferenceNo}' cannot be specified when creating a listing (unless supportOfflineReferenceCreate mode). This is generated by the system.", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            bool StatusCodeExist = await _statusCode().StatusCodeExist(dto.StatusCode);
            if (!StatusCodeExist)
            {
                throw new HttpRequestException($"The status code '{dto.StatusCode}' is not supported", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }
            dto.ListingTypeId = dto.ListingTypeId ?? 0;

            string sql = @$"INSERT INTO [listing].[Listing]
                        ([Listing].[Subject], 
                        [Listing].[ListingTypeId],
                        [Listing].[ReferenceNo], 
                        [Listing].[VisibilityId],
                        [Listing].[StatusCode], 
                        [Listing].[ProfileListingMediaId], 
                        [Listing].[ParentEntityIntId],
                        [Listing].[ParentEntityId],
                        [Listing].[ParentEntityType],   
                        [Listing].[ParentEntityId2],
                        [Listing].[ParentEntityType2],  
                        {(dto.GpsLocation?.Latitude != null ? "[Listing].[GpsLocation]," : "")}
                        [Listing].[ParentListingId],
                        [Listing].[Icon],
                        [Listing].[SortOrder],
                        [Listing].[FromDate], 
                        [Listing].[ToDate],
                        [Listing].[Description],
                        [Listing].[CreatedOn],
                        [Listing].[CreatedByName],
                        [Listing].[ModifiedOn],
                        [Listing].[ModifiedByName],
                        [Listing].[TenantId],
                        [Listing].[Icon],
                        [Listing].[Deleted])
                        VALUES
                        ( @Subject,
                         @ListingTypeId,
                         @ReferenceNo,
                         @VisibilityId,
                         @StatusCode,
                         @ProfileListingMediaId,
                         @ParentEntityIntId,
                         @ParentEntityId,
                         @ParentEntityType,
                         @ParentEntityId2,
                         @ParentEntityType2,
                         {(dto.GpsLocation?.Latitude != null ? "geography::STPointFromText('POINT(@gpslong @gpslat)', 4326)," : "")}
                         @ParentListingId,
                         @Icon,
                         @SortOrder,
                         @FromDate,
                         @ToDate,
                         @Description,
                         @CreatedOn,
                         @CreatedByName,
                         NULL,
                         NULL,
                         {(await _listingType().ListingTypeForTenant((short)dto.ListingTypeId) == true ? "@TenantId, " : "NULL, ")}
                         @Icon,
                         0)";
            DateTimeOffset now = DateTimeOffset.UtcNow;
            Int64 newListingId = -1;
            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                dto.CreatedOn = now;
                dto.CreatedByName = _utils.UserFullName;
                dto.ReferenceNo = string.IsNullOrEmpty(dto.ReferenceNo) ? "new" : dto.ReferenceNo;
                command.AddArguments(dto);
                command.AddArgument("gpslat", dto.GpsLocation?.Latitude);
                command.AddArgument("gpslong", dto.GpsLocation?.Longitude);
                command.AddArgument("TenantId", _utils.TenantId);
                newListingId = await command.ExecuteAndReturnIdentity();
            }

            dto.ListingId = newListingId;

            if (string.IsNullOrEmpty(dto.ReferenceNo) || dto.ReferenceNo == "new")
            {
                // Generate the new Reference Number based on the auto incremented Id.
                string referenceNo = GenerateReferenceNo.GenerateNewReferenceNo(newListingId);
                dto.ReferenceNo = referenceNo;

                string updSql = @"
                    UPDATE [listing].[Listing]
                    SET 
                        [ReferenceNo] = @ReferenceNo
                    WHERE [ListingId] = @ListingId";
                using (var command = await _unitOfWork.CmdFactory(CmdType.Update, updSql))
                {
                    command.AddArgument("ReferenceNo", referenceNo);
                    command.AddArgument("ListingId", newListingId);
                    await command.Execute();
                }
            }

            if (dto.IsFavourite == true)
            {
                await _favouriteFactory().AddAsync(dto.ReferenceNo, (Guid)_utils.PartyId!, "Party", 1);
            }

            await AddListingAuditRecord(newListingId, $"New listing created with status {dto.StatusCode}");

            return newListingId;
        }

        /// <summary>
        /// After creating a new listing add media and attributes (grouped by Display Container)
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="listingId"></param>
        /// <returns></returns>
        internal async Task CreateListingMediaAndDisplayGroupedAttributes(ManageListingWithDisplayGroupedDataAndMediaCDto dto, long listingId)
        {
            if (dto.Media != null)
            {
                foreach (var image in dto.Media)
                {

                    await _image().CreateAsync(image, listingId);
                }
            }
            if (dto.Attributes != null)
            {
                foreach (var dispContainer in dto.Attributes)
                {
                    if (dispContainer.Attributes != null)
                    {
                        foreach (var attribute in dispContainer.Attributes)
                        {
                            await _lAttribute().CreateAsync(attribute, listingId);
                        }
                    }
                }
            }
            // get supplier name
            //var supplierCDto = await _daprCRMServiceClient().GetRootPartyForTenant();
            //ManageListingAttributeCDto supplierName = new()
            //{
            //    AttributeCode = "SupplierName",
            //    ValueString = supplierCDto.DisplayName,
            //};
            //await _lAttribute().CreateAsync(supplierName, listingId);
        }

        /// <summary>
        /// After creating a new listing add media and attributes
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="listingId"></param>
        /// <returns></returns>
        internal async Task CreateListingMediaAndAttributes(ManageListingWithDataAndMediaCDto dto, long listingId)
        {
            if (dto.Media != null)
            {
                foreach (var image in dto.Media)
                {

                    await _image().CreateAsync(image, listingId);
                }
            }
            if (dto.Attributes != null)
            {
                foreach (var attribute in dto.Attributes)
                {
                    await _lAttribute().CreateAsync(attribute, listingId);
                }
            }
        }

        /// <summary>
        /// Add 1 or more Media records to a listing
        /// </summary>
        /// <param name="listingId"></param>
        /// <param name="media"></param>
        /// <returns></returns>
        /// <exception cref="HttpRequestException"></exception>
        internal async Task<ManageListingMediaCDto> AddMedia(long listingId, ManageListingMediaCDto media)
        {
            // Validate the ListingId exists
            var dto = await GetListingByListingId(listingId, includeTagsInResponse: false);
            if (dto == null)
            {
                throw new HttpRequestException($"Cannot find a match for this '{listingId}' ListingId", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }
            var mediaId = await _image().CreateAsync(media, listingId);
            var mediaDto = await _image().GetAsync(mediaId);
            await AddListingAuditRecord(listingId, $"Media added to listing.");
            return (mediaDto);
        }

        internal async Task DeleteMedia(long listingId, List<long> listOfImageIds)
        {
            var dto = await GetListingByListingId(listingId, includeTagsInResponse: false);
            if (dto == null)
            {
                throw new HttpRequestException("Cannot find a matching ListingId for '{ListingId}'", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }
            bool mediaDeleted = false;
            foreach (long listingMediaId in listOfImageIds)
            {
                var item = await _image().BelongAsync(listingMediaId, listingId);
                if (item == null)
                {
                    throw new HttpRequestException($"One of the Media '{listingMediaId}' does not exist", null, System.Net.HttpStatusCode.UnprocessableEntity);
                }
                else
                {
                    await _image().DeleteAsync(listingMediaId, listingId);
                    mediaDeleted = true;
                }
            }
            if (mediaDeleted)
            {
                await AddListingAuditRecord(listingId, $"Media removed from listing.");
            }
        }

        internal async Task<long> UpdateListing(ManageListingWithDataAndMediaCDto dto)
        {
            if (dto.ListingId == null)
            {
                ManageListingWithDisplayGroupedDataAndMediaCDto altDto = new ManageListingWithDisplayGroupedDataAndMediaCDto();
                altDto.Map(dto);
                return await CreateNewListing(altDto);
            }
            else
            {
                var test = await GetListingByListingId((long)dto.ListingId);
                if (test == null)
                {
                    throw new HttpRequestException($"Cannot find the requested {dto.ListingId} ListingId", null, System.Net.HttpStatusCode.UnprocessableEntity);
                }
                bool hasChangedListing = test.Subject != dto.Subject
                                       || test.ProfileListingMediaId != dto.ProfileListingMediaId
                                       || test.ParentEntityIntId != dto.ParentEntityIntId
                                       || test.ParentEntityId != dto.ParentEntityId
                                       || test.FromDate != dto.FromDate
                                       || test.ToDate != dto.ToDate
                                       || test.SortOrder != dto.SortOrder
                                       || test.Icon != dto.Icon
                                       || test.Description != dto.Description
                                       || test.GpsLocation?.Latitude != dto.GpsLocation?.Latitude
                                       || test.GpsLocation?.Longitude != dto.GpsLocation?.Longitude
                                       || test.ParentListingId != dto.ParentListingId
                                       || test.Icon != dto.Icon;
                if (hasChangedListing)
                {
                    string sql = @$"
                    UPDATE [listing].[Listing]
                    SET 
                        [Subject] = @Subject, 
                        [ProfileListingMediaId] = @ProfileListingMediaId, 
                        [ParentEntityIntId] = @ParentEntityIntId,
                        [ParentEntityId] = @ParentEntityId,
                        [ParentEntityType] = @ParentEntityType,   
                        [ParentListingId] = @ParentListingId,
                        [Icon] = @Icon,
                        [FromDate] = @FromDate, 
                        [ToDate] = @ToDate,
                        [SortOrder] = @SortOrder,
                        [Icon] = @Icon,
                        {(dto.GpsLocation?.Latitude != null ? "[GpsLocation] = geography::STPointFromText('POINT(@gpslong @gpslat)', 4326)" : "[GpsLocation] = NULL")}
                        [Description] = @Description,
                        [ModifiedOn] = @ModifiedOn,
                        [ModifiedByName] = @ModifiedByName
                    WHERE [ListingId] = @ListingId";
                    DateTimeOffset now = DateTimeOffset.UtcNow;
                    using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
                    {
                        dto.ModifiedOn = now;
                        dto.ModifiedByName = _utils.UserFullName;
                        command.AddArguments(dto);
                        command.AddArgument("gpslat", dto.GpsLocation?.Latitude);
                        command.AddArgument("gpslong", dto.GpsLocation?.Longitude);
                        await command.Execute();
                    }
                }
                bool mediaChanged = false;
                if (dto.Media != null)
                {
                    foreach (var image in dto.Media)
                    {
                        mediaChanged = await _image().UpdateAsync(image, (long)dto.ListingId);
                    }
                }
                bool attributesChanged = false;
                if (dto.Attributes != null)
                {
                    var existingDbAttrs = await _lAttribute().GetListingAttibutesbyId((long)dto.ListingId);
                    foreach (var attribute in dto.Attributes)
                    {
                        var existingRec = existingDbAttrs.FirstOrDefault(x => x.AttributeCode == attribute.AttributeCode);
                        if (existingRec == null
                            || existingRec.ValueDateTime != attribute.ValueDateTime
                            || existingRec.ValueNumeric != attribute.ValueNumeric
                            || existingRec.ValueString != attribute.ValueString
                            || existingRec.ValueStringMax != attribute.ValueStringMax
                            || existingRec.ValueGeography != attribute.ValueGeography)
                        {
                            await _lAttribute().UpdateAsync(attribute, (long)dto.ListingId);
                            attributesChanged = true;
                        }
                    }
                }

                if (hasChangedListing || mediaChanged || attributesChanged)
                {
                    await AddListingAuditRecord((long)dto.ListingId, $"Listing updated ({(mediaChanged ? "media changed; " : "")} {(attributesChanged ? "attributes changed; " : "")} {(hasChangedListing ? "base details changed" : "")}).");
                }

                return (long)dto.ListingId;
            }
        }

        internal async Task<long> UpdateListingWithDisplayContainers(ManageListingWithDisplayGroupedDataAndMediaCDto dto)
        {
            if (dto.ListingId == null)
            {
                return await CreateNewListing(dto);
            }
            else
            {
                var test = await GetListingByListingId((long)dto.ListingId);
                if (test == null)
                {
                    throw new HttpRequestException($"Cannot find the requested {dto.ListingId} ListingId", null, System.Net.HttpStatusCode.UnprocessableEntity);
                }
                bool hasChangedListing = test.Subject != dto.Subject
                                       || test.ProfileListingMediaId != dto.ProfileListingMediaId
                                       || test.ParentEntityIntId != dto.ParentEntityIntId
                                       || test.ParentEntityId != dto.ParentEntityId
                                       || test.ParentEntityId2 != dto.ParentEntityId2
                                       || test.FromDate != dto.FromDate
                                       || test.ToDate != dto.ToDate
                                       || test.SortOrder != dto.SortOrder
                                       || test.Icon != dto.Icon
                                       || test.Description != dto.Description
                                       || test.GpsLocation?.Latitude != dto.GpsLocation?.Latitude
                                       || test.GpsLocation?.Longitude != dto.GpsLocation?.Longitude
                                       || test.ParentListingId != dto.ParentListingId
                                       || test.Icon != dto.Icon;

                if (hasChangedListing)
                {
                    string sql = @$"
                    UPDATE [listing].[Listing]
                    SET 
                        [Subject] = @Subject, 
                        [ProfileListingMediaId] = @ProfileListingMediaId, 
                        [ParentEntityIntId] = @ParentEntityIntId,
                        [ParentEntityId] = @ParentEntityId,
                        [ParentEntityType] = @ParentEntityType,      
                        [ParentEntityId2] = @ParentEntityId2,
                        [ParentEntityType2] = @ParentEntityType2,  
                        [ParentListingId] = @ParentListingId,
                        [Icon] = @Icon,
                        [SortOrder] = @SortOrder,
                        [FromDate] = @FromDate, 
                        [ToDate] = @ToDate,
                        [Icon] = @Icon,
                        {(dto.GpsLocation?.Latitude != null ? "[GpsLocation] = geography::STPointFromText('POINT(@gpslong @gpslat)', 4326)," : "[GpsLocation] = NULL,")}
                        [Description] = @Description,
                        [ModifiedOn] = @ModifiedOn,
                        [ModifiedByName] = @ModifiedByName
                    WHERE [ListingId] = @ListingId";
                    DateTimeOffset now = DateTimeOffset.UtcNow;
                    using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
                    {
                        dto.ModifiedOn = now;
                        dto.ModifiedByName = _utils.UserFullName;
                        command.AddArguments(dto);
                        command.AddArgument("gpslat", dto.GpsLocation?.Latitude);
                        command.AddArgument("gpslong", dto.GpsLocation?.Longitude);
                        await command.Execute();
                    }
                }
                bool mediaChanged = false;
                if (dto.Media != null)
                {
                    foreach (var image in dto.Media)
                    {
                        mediaChanged = await _image().UpdateAsync(image, (long)dto.ListingId);
                    }
                }
                bool attributesChanged = false;
                if (dto.Attributes != null)
                {
                    var existingDbAttrs = await _lAttribute().GetListingAttibutesbyId((long)dto.ListingId);
                    foreach (var dispContainer in dto.Attributes)
                    {
                        if (dispContainer.Attributes != null)
                        {
                            foreach (var attribute in dispContainer.Attributes)
                            {
                                var existingRec = existingDbAttrs.FirstOrDefault(x => x.AttributeCode == attribute.AttributeCode);
                                if (existingRec == null
                                    || existingRec.ValueDateTime != attribute.ValueDateTime
                                    || existingRec.ValueNumeric != attribute.ValueNumeric
                                    || existingRec.ValueString != attribute.ValueString
                                    || existingRec.ValueStringMax != attribute.ValueStringMax
                                    || existingRec.ValueGeography?.Latitude != attribute.ValueGeography?.Latitude
                                    || existingRec.ValueGeography?.Longitude != attribute.ValueGeography?.Longitude)
                                {
                                    await _lAttribute().UpdateAsync(attribute, (long)dto.ListingId);
                                    attributesChanged = true;
                                }
                            }
                        }
                    }

                }

                if (hasChangedListing || mediaChanged || attributesChanged)
                {
                    await AddListingAuditRecord((long)dto.ListingId, $"Listing updated ({(mediaChanged ? "media changed; " : "")} {(attributesChanged ? "attributes changed; " : "")} {(hasChangedListing ? "base details changed" : "")}).");
                }

                return (long)dto.ListingId;
            }
        }

        internal async Task<List<ManageListingDisplayContainerCDto>> GetAttributeDataAsync(Int64 listingId, string[]? displayContainerCodesArray, bool? excludeAttributesWithNoData = true, string? displayGroupCode = "")
        {
            List<ManageListingDisplayContainerCDto> DisplayContainers = new List<ManageListingDisplayContainerCDto>();
            string displayContainerCodesString = displayContainerCodesArray != null ? string.Join(",", displayContainerCodesArray) : "";
            if (!string.IsNullOrEmpty(displayGroupCode) || displayContainerCodesArray == null || displayContainerCodesArray.Length == 0)
            {
                DisplayContainers = await GetDisplayContainers(displayGroupCode);
            }
            if (displayContainerCodesArray?.Length > 0)
            {
                foreach (var dcc in displayContainerCodesArray)
                {
                    var DisplayContainer = await GetDisplayContainer(dcc);
                    if (DisplayContainer.Enabled == true && !DisplayContainers.Exists(x => x.DisplayContainerCode == DisplayContainer.DisplayContainerCode))
                    {
                        DisplayContainers.Add(DisplayContainer);
                    }
                }
            }

            string sql = @$"
                        SELECT 
                        [ListingAttribute].[ListingAttributeId], 
                        [ListingAttribute].[AttributeCode], 
                        [ListingAttribute].[ValueString] AS ValueString,
                        [ListingAttribute].[ValueStringMax] AS ValueStringMax,
                        [ListingAttribute].[ValueNumeric] AS ValueNumeric,
                        [ListingAttribute].[ValueDateTime] AS ValueDateTime,
                        [ListingAttribute].[ValueGeography].[Lat] AS Latitude,
                        [ListingAttribute].[ValueGeography].[Long] AS Longitude,
                        [DisplayContainerAttribute].[DisplayContainerAttributeId],
                        [DisplayContainerAttribute].[DisplayContainerCode],
                        [DisplayContainerAttribute].[IsReadOnly],
                        [Attribute].[Label],
                        [Attribute].[SortOrder],
                        [Attribute].[AttributeGroupCode], 
                        [Attribute].[AttributeValueTypeCode],
                        [Attribute].[Icon],
                        [Attribute].[ReadAccessClaim],
                        [Attribute].[WriteAccessClaim],
                        [Attribute].[IsManyAllowed],
                        [Attribute].[NumericDecimalPlaces],
                        CASE 
                            WHEN [DisplayContainerAttribute].[InputTypeCode] IS NULL THEN [Attribute].[InputTypeCode]
                            ELSE [DisplayContainerAttribute].[InputTypeCode]
                        END as [InputTypeCode]
                        FROM [listing].[DisplayContainerAttribute] [DisplayContainerAttribute]
                        INNER JOIN [listing].[Attribute] ON [DisplayContainerAttribute].[AttributeCode] = [Attribute].[AttributeCode]
                        {(excludeAttributesWithNoData == true ? "INNER JOIN" : "LEFT JOIN")} 
                        [listing].[ListingAttribute] [ListingAttribute] ON [ListingAttribute].[AttributeCode] = [Attribute].[AttributeCode]
                        WHERE [DisplayContainerAttribute].[Deleted] = 0 AND [Attribute].[IsEnabled] = 1
                        AND [ListingAttribute].[ListingId] = @ListingId
                        {(displayContainerCodesArray?.Length > 0 ? "AND [DisplayContainerAttribute].[DisplayContainerCode] in (@DisplayContainerCodes)" : "")}
                        ORDER BY [DisplayContainerAttribute].[SortOrder], [DisplayContainerAttribute].[DisplayContainerCode], [Attribute].[SortOrder], [Attribute].[Label] ";


            var data = new List<ManageListingAttributeCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ListingId", listingId);
                if (displayContainerCodesArray?.Length > 0)
                {
                    command.AddArgument("DisplayContainerCodes", displayContainerCodesArray.ToList<string>());
                }
                using (var reader = await command.SelectRaw())
                {
                    while (reader.Read())
                    {
                        ManageListingAttributeCDto rec = new ManageListingAttributeCDto();
                        rec.ListingAttributeId = (reader.GetInt64(reader.GetOrdinal("ListingAttributeId")));
                        rec.AttributeCode = (reader.GetString(reader.GetOrdinal("AttributeCode")));
                        rec.ValueString = !reader.IsDBNull(reader.GetOrdinal("ValueString")) ? (reader.GetString(reader.GetOrdinal("ValueString"))) : null;
                        rec.ValueStringMax = !reader.IsDBNull(reader.GetOrdinal("ValueStringMax")) ? (reader.GetString(reader.GetOrdinal("ValueStringMax"))) : null;
                        rec.ValueNumeric = !reader.IsDBNull(reader.GetOrdinal("ValueNumeric")) ? (reader.GetDecimal(reader.GetOrdinal("ValueNumeric"))) : null;
                        rec.ValueDateTime = !reader.IsDBNull(reader.GetOrdinal("ValueDateTime")) ? (reader.GetDateTimeOffset(reader.GetOrdinal("ValueDateTime"))) : null;
                        rec.NumericDecimalPlaces = !reader.IsDBNull(reader.GetOrdinal("NumericDecimalPlaces")) ? (reader.GetByte(reader.GetOrdinal("NumericDecimalPlaces"))) : null;
                        rec.DisplayContainerAttributeId = (reader.GetInt32(reader.GetOrdinal("DisplayContainerAttributeId")));
                        rec.DisplayContainerCode = (reader.GetString(reader.GetOrdinal("DisplayContainerCode")));
                        rec.IsReadOnly = !reader.IsDBNull(reader.GetOrdinal("IsReadOnly")) ? (reader.GetBoolean(reader.GetOrdinal("IsReadOnly"))) : false;
                        rec.Label = !reader.IsDBNull(reader.GetOrdinal("Label")) ? (reader.GetString(reader.GetOrdinal("Label"))) : null;
                        rec.SortOrder = !reader.IsDBNull(reader.GetOrdinal("SortOrder")) ? (reader.GetInt16(reader.GetOrdinal("SortOrder"))) : null;
                        rec.AttributeGroupCode = !reader.IsDBNull(reader.GetOrdinal("AttributeGroupCode")) ? (reader.GetString(reader.GetOrdinal("AttributeGroupCode"))) : null;
                        rec.AttributeValueTypeCode = (reader.GetString(reader.GetOrdinal("AttributeValueTypeCode")));
                        rec.Icon = !reader.IsDBNull(reader.GetOrdinal("Icon")) ? (reader.GetString(reader.GetOrdinal("Icon"))) : null;
                        rec.InputTypeCode = !reader.IsDBNull(reader.GetOrdinal("InputTypeCode")) ? (reader.GetString(reader.GetOrdinal("InputTypeCode"))) : null;
                        rec.ReadAccessClaim = !reader.IsDBNull(reader.GetOrdinal("ReadAccessClaim")) ? (reader.GetString(reader.GetOrdinal("ReadAccessClaim"))) : null;
                        rec.WriteAccessClaim = !reader.IsDBNull(reader.GetOrdinal("WriteAccessClaim")) ? (reader.GetString(reader.GetOrdinal("WriteAccessClaim"))) : null;
                        rec.IsManyAllowed = !reader.IsDBNull(reader.GetOrdinal("IsManyAllowed")) ? (reader.GetBoolean(reader.GetOrdinal("IsManyAllowed"))) : false;
                        if (!reader.IsDBNull(reader.GetOrdinal("Latitude")) && !reader.IsDBNull(reader.GetOrdinal("Longitude")))
                        {
                            rec.ValueGeography = new GpslocationCDto()
                            {
                                Latitude = (decimal)reader.GetDouble(reader.GetOrdinal("Latitude"))
                                                                     ,
                                Longitude = (decimal)reader.GetDouble(reader.GetOrdinal("Longitude"))
                            };
                        }

                        rec.ValueNumeric = Extensions.DecimalTrimPlaces(rec.ValueNumeric, rec.NumericDecimalPlaces);

                        var attrDef = await GetAttribute(rec.AttributeCode);
                        {
                            rec.IsRequired = attrDef.IsRequired;
                            rec.NumericMinValue = attrDef.NumericMinValue;
                            rec.NumericMaxValue = attrDef.NumericMaxValue;
                            rec.TextMinCharacters = attrDef.TextMinCharacters;
                            rec.TextMaxCharacters = attrDef.TextMaxCharacters;
                            rec.MinDateDaysFromToday = attrDef.MinDateDaysFromToday;
                            rec.MaxDateDaysFromToday = attrDef.MaxDateDaysFromToday;
                            rec.MinSelectionsRequired = attrDef.MinSelectionsRequired;
                            rec.MaxSelectionsAllowed = attrDef.MaxSelectionsAllowed;
                            rec.MaxStars = attrDef.MaxStars;
                            rec.HelpText = attrDef.HelpText;
                        };
                        data.Add(rec);
                    }
                }

            }

            if (data == null)
            {
                return null;
            }

            foreach (var rec in data)
            {
                var DisplayContainer = DisplayContainers.FirstOrDefault(x => x.DisplayContainerCode == rec.DisplayContainerCode);
                if (DisplayContainer != null)
                {
                    if (DisplayContainer.Attributes == null)
                    {
                        DisplayContainer.Attributes = new List<ManageListingAttributeCDto>();
                    }
                    DisplayContainer.Attributes.Add(rec);
                }
            }

            // Remove Display Sets that do not have any attributes
            for (int i = DisplayContainers.Count - 1; i >= 0; i--)
            {
                if (DisplayContainers[i].Attributes == null || DisplayContainers[i].Attributes?.Count == 0)
                {
                    DisplayContainers.RemoveAt(i);
                }
            }

            return DisplayContainers;
        }

        private async Task<List<ManageListingDisplayContainerCDto>> GetDisplayContainers(string? displayGroupCode = "")
        {
            string cacheKey = "ManageListingDisplayContainers-" + displayGroupCode;

            if (_memoryCache.TryGetValue(cacheKey, out List<ManageListingDisplayContainerCDto>? cacheValue))
            {
                var cacheResp = new List<ManageListingDisplayContainerCDto>();
                if (cacheValue != null)
                {
                    foreach (var rec in cacheValue)
                    {
                        cacheResp.Add(rec.DeepClone());
                    }
                    return cacheResp;
                }
            }

            string sql = @$"
                        SELECT  
                        [DisplayContainer].[Title], 
                        [DisplayContainer].[DisplayContainerCode],                         
                        [DisplayContainer].[Enabled],
                        [DisplayContainer].[IsShowTitle],
                        [DisplayContainer].[Icon],
                        [DisplayContainer].[HelpText]
                        FROM [listing].[DisplayContainer] [DisplayContainer] 
                        {(!string.IsNullOrEmpty(displayGroupCode) ? "INNER JOIN [listing].[DisplayGroupDisplayContainer] ON [DisplayGroupDisplayContainer].[DisplayContainerCode] = [DisplayContainer].[DisplayContainerCode] " : "")}
                        {(!string.IsNullOrEmpty(displayGroupCode) ? "INNER JOIN [listing].[DisplayGroup] ON [DisplayGroup].[DisplayGroupCode] = [DisplayGroupDisplayContainer].[DisplayGroupCode] AND [DisplayGroup].[IsEnabled] = 1 " : "")}
                        WHERE [DisplayContainer].[Enabled] = 1 
                        {(!string.IsNullOrEmpty(displayGroupCode) ? " AND [DisplayGroupDisplayContainer].[DisplayGroupCode] = @displayGroupCode " : "")}
                        {(!string.IsNullOrEmpty(displayGroupCode) ? " ORDER BY [DisplayGroupDisplayContainer].[SortOrder] ASC " : "")}
                        ";
            var result = new List<ManageListingDisplayContainerCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("displayGroupCode", displayGroupCode);
                result = await command.SelectMany<ManageListingDisplayContainerCDto>();
            }

            _memoryCache.Set(cacheKey, result.DeepClone(), CacheOptions());

            return result;
        }

        private async Task<ManageListingDisplayContainerCDto> GetDisplayContainer(string DisplayContainerCode)
        {
            DisplayContainerCode = DisplayContainerCode.Trim();
            string cacheKey = "ManageListingDisplayContainer" + DisplayContainerCode;

            if (_memoryCache.TryGetValue(cacheKey, out ManageListingDisplayContainerCDto? cacheValue))
            {
                if (cacheValue != null)
                {
                    return cacheValue.DeepClone();
                }
            }

            string sql = @"
                        SELECT  
                        [DisplayContainer].[Title], 
                        [DisplayContainer].[DisplayContainerCode],                         
                        [DisplayContainer].[Enabled],
                        [DisplayContainer].[IsShowTitle],
                        [DisplayContainer].[Icon],
                        [DisplayContainer].[HelpText]
                        FROM [listing].[DisplayContainer] [DisplayContainer] 
                        WHERE [DisplayContainer].[DisplayContainerCode] = @DisplayContainerCode";
            var result = new ManageListingDisplayContainerCDto();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("DisplayContainerCode", DisplayContainerCode);
                result = await command.SelectSingle<ManageListingDisplayContainerCDto>();
            }
            if (result == null)
            {
                throw new HttpRequestException($"DisplayContainerCode '{DisplayContainerCode}' does not exist", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (result.Enabled == false)
            {
                throw new HttpRequestException($"DisplayContainerCode '{DisplayContainerCode}' is not enabled", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            _memoryCache.Set(cacheKey, result.DeepClone(), CacheOptions());

            return result;
        }

        internal async Task UpdateStatusAsync(long listingId, string newStatusCode)
        {
            var oldRec = await GetListingByListingId(listingId, includeTagsInResponse: false);
            if (oldRec == null)
            {
                throw new HttpRequestException($"Cannot find the requested {listingId} ListingId", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            var isValidStatusCode = await IsValidStatusCode(newStatusCode);
            if (!isValidStatusCode)
            {
                throw new HttpRequestException($"Cannot find a matching status code for '{newStatusCode}'", null, System.Net.HttpStatusCode.NotFound);
            }

            string sql = @"
                    UPDATE [listing].[Listing]
                    SET 
                        [StatusCode] = @NewStatusCode, 
                        [ModifiedOn] = @ModifiedOn,
                        [ModifiedByName] = @ModifiedByName
                    WHERE [ListingId] = @ListingId AND [StatusCode] != @NewStatusCode";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("NewStatusCode", newStatusCode);
                command.AddArgument("ModifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("ModifiedByName", _utils.UserFullName);
                command.AddArgument("ListingId", listingId);
                await command.Execute();
            }

            await AddListingAuditRecord(listingId, $"Status changed from {oldRec.StatusCode} tp {newStatusCode}");
        }

        internal async Task UpdateVisibilityAsync(long listingId, short newVisibilityId)
        {
            var oldRec = await GetListingByListingId(listingId, includeTagsInResponse: false);
            if (oldRec == null)
            {
                throw new HttpRequestException($"Cannot find the requested {listingId} ListingId", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (oldRec.VisibilityId == newVisibilityId)
            {
                // no change so exit.
                return;
            }

            string sql = @"
                    UPDATE [listing].[Listing]
                    SET 
                        [VisibilityId] = @newVisibilityId, 
                        [ModifiedOn] = @ModifiedOn,
                        [ModifiedByName] = @ModifiedByName
                    WHERE [ListingId] = @ListingId AND [VisibilityId] != @newVisibilityId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("newVisibilityId", newVisibilityId);
                command.AddArgument("ModifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("ModifiedByName", _utils.UserFullName);
                command.AddArgument("ListingId", listingId);
                await command.Execute();
            }

            await AddListingAuditRecord(listingId, $"Visibility changed from {await GetVisibility(oldRec.VisibilityId)} to {await GetVisibility(newVisibilityId)}");
        }

        private async Task<bool> IsValidStatusCode(string statusCode)
        {
            bool isValidStatusCode = false;

            string sql = @"SELECT [StatusCode]
                        FROM [common].[Status] [Status]
                        WHERE [Status].[StatusCode] = @StatusCode";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("StatusCode", statusCode);
                var result = await command.SelectSingle<string>("StatusCode");

                if (result != null)
                {
                    isValidStatusCode = true;
                }
            }

            return isValidStatusCode;
        }

        private async Task<string> GetVisibility(short visibilityId)
        {
            string sql = @"SELECT [Label]
                        FROM [common].[Visibility] [Visibility]
                        WHERE [Visibility].[Visibilityid] = @VisibilityId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("VisibilityId", visibilityId);
                var result = await command.SelectSingle<string>("Label");

                return result;
            }
        }

        internal async Task DeleteAsync(long listingId)
        {
            var dto = await GetListingByListingId(listingId, includeTagsInResponse: false);
            if (dto == null)
            {
                throw new HttpRequestException($"Cannot find a match for this '{listingId}' ListingId", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }
            string sql = @"
                            UPDATE [listing].[Listing]
                            SET
	                               [ModifiedOn] = @ModifiedOn,
                                   [ModifiedByName] = @ModifiedByName,
	                               [Deleted] = 1
                            WHERE [ListingId] = @ListingId AND [Deleted] = 0
                            ";
            DateTimeOffset now = DateTimeOffset.UtcNow;
            using (var command = await _unitOfWork.CmdFactory(CmdType.Delete, sql))
            {
                dto.ModifiedOn = now;
                dto.ModifiedByName = _utils.UserFullName;
                command.AddArguments(dto);
                await command.Execute();
            }

            await AddListingAuditRecord(listingId, $"Listing deleted");

        }

        private async Task AddListingAuditRecord(Int64 listingId, string description)
        {
            string sql = @"INSERT INTO [listing].[ListingAudit]
                        ([ListingAudit].[ListingId], 
                        [ListingAudit].[Description], 
                        [ListingAudit].[CreatedOn], 
                        [ListingAudit].[CreatedByName])
                        VALUES
                        (@ListingId,
                         @Description,
                         @CreatedOn,
                         @CreatedByName)";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArgument("ListingId", listingId);
                command.AddArgument("Description", description);
                command.AddArgument("CreatedOn", DateTimeOffset.UtcNow);
                command.AddArgument("CreatedByName", _utils.UserFullName);
                await command.Execute();
            }
        }

        internal async Task<List<MicroserviceContract.Dtos.ListingAuditCDto>> GetListingAuditRecords(Int64 listingId, string? Description, DateTimeOffset? beforeDate, DateTimeOffset? afterDate, bool bypassCache = false)
        {
            string cacheKey = "GetListingAuditRecords" + listingId
                                                + (Description != null ? $"D{Description}" : "")
                                                + (beforeDate != null ? $"bd{beforeDate}" : "")
                                                + (afterDate != null ? $"ad{afterDate}" : "");

            if (_memoryCache.TryGetValue(cacheKey, out List<MicroserviceContract.Dtos.ListingAuditCDto>? cacheValue) && !bypassCache && cacheValue != null)
            {
                return cacheValue;
            }
            string sql = @"
     SELECT  [listingAudit].[ListingAuditId]
            ,[listingAudit].[Description]
            ,[listingAudit].[ListingId]
            ,[listingAudit].[CreatedOn]
            ,[listingAudit].[CreatedByName]
            FROM [listing].[ListingAudit] [listingAudit] WHERE [ListingId] = @ListingId";

            if (!string.IsNullOrEmpty(Description))
            {
                sql += $" AND [Description] LIKE '%' + @Description + '%'";
            }
            if (beforeDate != null)
            {
                sql += $" AND [CreatedOn] <= @beforeDate";
            }
            if (afterDate != null)
            {
                sql += $" AND [CreatedOn] >= @afterDate";
            }

            var list = new List<MicroserviceContract.Dtos.ListingAuditCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ListingId", listingId);
                command.AddArgument("Description", Description);
                command.AddArgument("beforeDate", beforeDate);
                command.AddArgument("afterDate", afterDate);


                list = await command.SelectMany<MicroserviceContract.Dtos.ListingAuditCDto>();
            }

            _memoryCache.Set(cacheKey, list, CacheOptions());
            return list;

        }

        private async Task<AttributeDto> GetAttribute(string attributeCode)
        {
            string cacheKey = "AllAttributes";

            if (_memoryCache.TryGetValue(cacheKey, out List<AttributeDto>? cacheValue))
            {
                if (cacheValue != null)
                {
                    var rec = cacheValue.FirstOrDefault(x => x.AttributeCode == attributeCode);
                    if (rec != null)
                    {
                        return rec;
                    }
                }
            }

            string sql = @$"
                        SELECT *
                        FROM [listing].[Attribute]";
            var result = new List<AttributeDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                result = await command.SelectMany<AttributeDto>();
            }

            if (result != null && result.Count > 0)
            {
                _memoryCache.Set(cacheKey, result.DeepClone(), CacheOptions());

                var crec = result.FirstOrDefault(x => x.AttributeCode == attributeCode);
                if (crec != null)
                {
                    return result.FirstOrDefault(x => x.AttributeCode == attributeCode);
                }
            }

            return null;
        }

        public async Task UploadFile(IFormFile file, string cloudDirectory, string cloudFilename, bool isPublic, bool createThumbnail, string uploadFolderName)
        {
            try
            {
                string fileName = file.FileName;
                string folderName = Path.Join(Directory.GetCurrentDirectory(), uploadFolderName);
                string uniqueFileName = cloudFilename.Split("/")[0];

                if (!Directory.Exists(folderName))
                {
                    Directory.CreateDirectory(folderName);
                }

                uniqueFileName = EnsureUniqueDestination(folderName, fileName);
                string path = Path.Join(folderName, uniqueFileName);

                using (FileStream fileStream = System.IO.File.Create(path))
                {
                    file.CopyTo(fileStream);
                }
                var cloudFilePath = (string.IsNullOrWhiteSpace(cloudFilename) ? "{0}" : Path.GetFileNameWithoutExtension(cloudFilename)) + "-thumbnail";
#pragma warning disable CS8602 // Dereference of a possibly null reference.
                await cloud.UploadFileAsync(path, cloudDirectory, cloudFilePath, isPublic, true);
#pragma warning restore CS8602 // Dereference of a possibly null reference.
            }
            catch
            {
                throw;
            }
        }

        //simply adds a digit on to the filname if it already exists. I.e if myfile.jpg exists then this will return myfile #2.jpg
        private string EnsureUniqueDestination(string folder, string fileName)
        {
            string path = Path.Join(folder, fileName);
            if (!System.IO.File.Exists(path))
            {
                return fileName;
            }
            else
            {
                int count = 2;
                string[] splitName = fileName.Split('.');
                //make sure file extension is tehre
                if (splitName.Length > 1)
                {
                    string newName = "";
                    for (int ii = 0; ii < splitName.Length - 2; ii++)
                    {
                        newName += splitName[ii];
                        newName += ".";
                    }
                    newName += splitName[splitName.Length - 2];
                    newName += " #" + count + "." + splitName[splitName.Length - 1];
                    while (System.IO.File.Exists(folder + newName))
                    {
                        count++;
                        newName = "";
                        for (int ii = 0; ii < splitName.Length - 2; ii++)
                        {
                            newName += splitName[ii];
                            newName += ".";
                        }
                        newName += splitName[splitName.Length - 2];
                        newName += " #" + count + "." + splitName[splitName.Length - 1];
                    }
                    return newName;
                }
                else
                {
                    //in case no file extension
                    while (System.IO.File.Exists(folder + fileName + " #" + count))
                    {
                        count++;
                    }
                    return fileName + " #" + count;
                }
            }
        }
    }
}
