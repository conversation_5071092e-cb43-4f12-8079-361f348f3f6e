﻿using Sql;
namespace MicroserviceContract.Dtos.Editor
{
    
    /// <summary>
    /// A Display Container details along with its Attributes
    /// </summary>
    [Mappable(nameof(DisplayContainerCode))]
    public class EditorDisplayContainerCDto
    {
        public string? DisplayContainerCode { set; get; }
        public string? Title { set; get; }
        public bool? IsShowTitle { set; get; }
        public string? Icon { set; get; }
        public bool? Enabled { set; get; }
        public string? HelpText { set; get; }

        [IgnoreDbMapping]
        public List<EditorDisplayContainerAttributeCDto>? Attributes { set; get; }

    }

    /// <summary>
    /// Attribute details returned as part of a Display Container
    /// </summary>
    [Mappable(nameof(DisplayContainerAttributeId))]
    public class EditorDisplayContainerAttributeCDto
    {
        public string? DisplayContainerAttributeId { get; set; }
        /// <summary>
        /// Attribute Code that uniquely identifies a type of Attribute across all listings.
        /// </summary>
        public string? AttributeCode { get; set; }

        /// <summary>
        /// Display Container Code that uniquely identifies a Listing Display Container
        /// </summary>
        public string? DisplayContainerCode { get; set; }

        /// <summary>
        /// Label to display on screens
        /// </summary>
        public string? Label { get; set; }

        public string? AttributeGroupCode { get; set; }

        /// <summary>
        /// The Attribute Value Type Code defines the type of data stored by an Attribute in the Value field.
        /// Types can include: ValueString - string up to 500chars, ValueStringMax - string of any size, ValueDateTime - date time with timezone, ValueNumeric - decimal or integer, ValueGeography - lat and long.
        /// </summary>
        public string? AttributeValueTypeCode { get; set; }

        /// <summary>
        /// Defines what type of input field is required for the Filter Attribute (Text, Number, Dropdown, etc)
        /// </summary>
        public string? InputTypeCode { get; set; }

        /// <summary>
        /// When true this indicates many of these values can be selected.
        /// </summary>
        public bool? IsManyAllowed { get; set; }

        /// <summary>
        /// SortOrder indicates the order Attributes should be sorted in
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// True when Attribute is enabled for use/display
        /// </summary>
        public bool IsEnabled { get; set; }
        /// <summary>
        /// True when an Attribute cannot be edited - Is Read Only
        /// </summary>
        public bool IsReadOnly { get; set; }
        /// <summary>
        /// The Icon to be displayed by the side of an Attribute Label or Value
        /// </summary>
        public string? Icon { get; set; }
        /// <summary>
        /// Security Claim required to be able to read/see the Attribute value
        /// No value means there is no restriction.
        /// </summary>
        public string? ReadAccessClaim { get; set; }
        /// <summary>
        /// Security Claim required to be able to edit the attribute
        /// </summary>
        public string? WriteAccessClaim { get; set; }

        public short? NumericDecimalPlaces { get; set; }

        /// <summary>
        /// When true the Attribute is mandatory
        /// </summary>
        public bool? IsRequired { set; get; }
        /// <summary>
        /// For a integer or decimal this is the minimum allowed value (if set)
        /// </summary>
        public decimal? NumericMinValue { set; get; }
        /// <summary>
        /// For a integer or decimal this is the maximum allowed value (if set)
        /// </summary>
        public decimal? NumericMaxValue { set; get; }
        /// <summary>
        /// For a text input this is the minimum required characters (if set)
        /// </summary>
        public int? TextMinCharacters { set; get; }
        /// <summary>
        /// For a text input this is the maximum allowed characters (if set)
        /// </summary>
        public int? TextMaxCharacters { set; get; }
        /// <summary>
        /// For a Date input this is the min date allowed (Today + plus MinDateDaysFromToday) 
        /// Make negative to allow past dates
        /// </summary>
        public int? MinDateDaysFromToday { set; get; }
        /// <summary>
        /// For a Date input this is the max date allowed (Today + plus MinDateDaysFromToday) 
        /// Make negative to force past dates
        /// </summary>
        public int? MaxDateDaysFromToday { get; set; }
        /// <summary>
        /// For a multi select this is the minimum selections required
        /// </summary>
        public int? MinSelectionsRequired { set; get; }
        /// <summary>
        /// For a multi select this is the maximum selections allowed
        /// </summary>
        public int? MaxSelectionsAllowed { set; get; }
        /// <summary>
        /// For a stars input this is the number of stars
        /// </summary>
        public int? MaxStars { set; get; }
        /// <summary>
        /// Optional help text
        /// </summary>
        public string? HelpText { set; get; }

        /// <summary>
        /// Default text value when attribute does not yet exist for a listing
        /// </summary>
        public string? DefaultTextValue { set; get; }
        /// <summary>
        /// Default decimal/integer value when attribute does not yet exist for a listing
        /// </summary>
        public decimal? DefaultNumericValue { set; get; }
        /// <summary>
        /// Default date (add days to today)
        /// Use negative values for past days
        /// </summary>
        public int? DefaultDateAddDays { set; get; }
        /// <summary>
        /// Default date (add months to today).
        /// Use negative values for past months
        /// </summary>
        public int? DefaultDateAddMonths { set; get; }
        /// <summary>
        /// Default time (add minutes to today)
        /// </summary>
        public int? DefaultTimeAddMinutes { set; get; }

        /// <summary>
        /// List of available selection values for a dropdown or radio buttons.
        /// </summary>
        [IgnoreDbMapping]
        public List<EditorSelectionValues>? SelectionValues { get; set; }

    }

    /// <summary>
    /// An Attribute Selection Value (Dropdown)
    /// </summary>
    [Mappable(nameof(Value))]
    public class EditorSelectionValues
    {
        public string? Value { set; get; }

        public short SortOrder { set; get; }

        public string? DisplayValue { set; get; }
    }
}
