﻿using Sql;
namespace MicroserviceContract.Dtos.Listing
{
    /// <summary>
    /// This is the Listing Filters Response CDto.
    /// It returns available filters grouped by Display Container.
    /// Each returned filter attribute includes a list of available attribute values along with a count of how many listing exist with that value.
    /// </summary>
    public class ListingFiltersResponseCDto
    {
        /// <summary>
        /// Total count of listings found based on filter/search criteria
        /// </summary>
        public int? CountOfListingsFound { set; get; }

        /// <summary>
        /// List of Display Containers which includes attributes and their values
        /// </summary>
        [IgnoreDbMapping]
        public List<ListingFiltersResponseDisplayContainerCDto>? DisplayContainers { set; get; }
    }

    public class ListingFiltersFlattenedResponseCDto
    {
        /// <summary>
        /// Total count of listings found based on filter/search criteria
        /// </summary>
        public int? CountOfListingsFound { set; get; }

        /// <summary>
        /// Dictionary of Attributes and there available values (and optionally counts)
        /// </summary>
        [IgnoreDbMapping]
        public Dictionary<string,object?>? Fields { set; get; }
    }

    /// <summary>
    /// A Display Container details along with its Attributes
    /// </summary>
    [Mappable(nameof(DisplayContainerCode))]
    public class ListingFiltersResponseDisplayContainerCDto
    {
        public string? DisplayContainerCode { set; get; }
        public string? Title { set; get; }
        public bool? IsShowTitle { set; get; }
        public string? Icon { set; get; }
        public bool? Enabled { set; get; }
        public string? HelpText { set; get; }

        [IgnoreDbMapping]
        public List<ListingDisplayContainerAttributesCDto>? Attributes { set; get; }

        
    }

    /// <summary>
    /// Attribute details returned as part of a Display Container
    /// </summary>
    [Mappable(nameof(DisplayContainerAttributeId))]
    public class ListingDisplayContainerAttributesCDto
    {
        public string? DisplayContainerAttributeId { get; set; }
        /// <summary>
        /// Attribute Code that uniquely identifies a type of Attribute across all listings.
        /// </summary>
        public string? AttributeCode { get; set; }

        /// <summary>
        /// Display Container Code that uniquely identifies a Listing Display Container
        /// </summary>
        public string? DisplayContainerCode { get; set; }

        /// <summary>
        /// Label to display on screens
        /// </summary>
        public string? Label { get; set; }

        public string? AttributeGroupCode { get; set; }

        /// <summary>
        /// The Attribute Value Type Code defines the type of data stored by an Attribute in the Value field.
        /// Types can include: ValueString - string up to 500chars, ValueStringMax - string of any size, ValueDateTime - date time with timezone, ValueNumeric - decimal or integer, ValueGeography - lat and long.
        /// </summary>
        public string? AttributeValueTypeCode { get; set; }

        /// <summary>
        /// Defines what type of input field is required for the Filter Attribute (Text, Number, Dropdown, etc)
        /// </summary>
        public string? InputTypeCode { get; set; }

        /// <summary>
        /// When true this indicates many of these values can be selected.
        /// </summary>
        public bool? IsManyAllowed { get; set; }

        /// <summary>
        /// SortOrder indicates the order Attributes should be sorted in
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// True when Attribute is enabled for use/display
        /// </summary>
        public bool IsEnabled { get; set; }
        /// <summary>
        /// True when an Attribute cannot be edited - Is Read Only
        /// </summary>
        public bool IsReadOnly { get; set; }
        /// <summary>
        /// The Icon to be displayed by the side of an Attribute Label or Value
        /// </summary>
        public string? Icon { get; set; }
        /// <summary>
        /// Security Claim required to be able to read/see the Attribute value
        /// No value means there is no restriction.
        /// </summary>
        public string? ReadAccessClaim { get; set; }
        /// <summary>
        /// Security Claim required to be able to edit the attribute
        /// </summary>
        public string? WriteAccessClaim { get; set; }

        public short? NumericDecimalPlaces { get; set; }

        /// <summary>
        /// None - Do not return values for filter call, Distinct - return each distinct value, MinMax - return min and max values
        /// </summary>
        public string? FilterGetModeCode { get; set; }

        /// <summary>
        /// List of available Attribute Values that exist for a filter Attribute
        /// </summary>
        [IgnoreDbMapping]
        public List<AvailableValuesWithStats>? AvailableValues { get; set; }

    }

    /// <summary>
    /// An Attribute Value along with a count of Listing that have that value
    /// </summary>
    public class AvailableValuesWithStats
    {
        /// <summary>
        /// An Attribute value
        /// </summary>
        public object? Value { get; set; }
        /// <summary>
        /// Number of filtered records with this Attribute Value
        /// </summary>
        public int Count { get; set; }
    }
}
