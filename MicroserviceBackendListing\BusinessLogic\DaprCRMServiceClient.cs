﻿using MicroserviceContract.Dtos.CRM;
using Redi.Prime3.MicroService.BaseLib;
using System.Net.Http.Headers;

namespace MicroserviceBackendListing.BusinessLogic
{
    public class DaprCRMServiceClient : HttpClientBase
    {
        private readonly UtilityFunctions _utils;
        private readonly ILogger _logger;
        public DaprCRMServiceClient(HttpClient httpClient, ILogger logger, UtilityFunctions utils)
        {
            _httpClient = httpClient;
            _logger = logger;
            _utils = utils;
        }

        public async Task<ExtendedPartyCDto> GetRootPartyForTenant(bool getPartyAttributes = false, string? attributeGroupCodes = null, string? parentRelationFields = null, string? childRelationFields = null, string? attributeFields = null, string? contactMethodFields = "email_1,phone_1", string? addressFields = "address_1")
        {
            try
            {
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _utils.AccessToken);
                var result = await _httpClient.GetAsync($"api/Party/GetRootForTenant?getPartyAttributes={getPartyAttributes}&attributeGroupCodes={attributeGroupCodes}&parentRelationFields={parentRelationFields}&childRelationFields={childRelationFields}&attributeFields={attributeFields}&contactMethodFields={contactMethodFields}&addressFields={addressFields}");
                var response = result.EnsureSuccessStatusCode();
                if (response != null && response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<ExtendedPartyCDto>();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message, ex);
            }
            return null;
        }
    }
}
