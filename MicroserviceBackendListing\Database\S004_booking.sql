/************************************************************************************************
Booking Feature Tables
Note! Only added if FeatureBooking is enabled
**************************************************************************************************/
BEGIN
    IF EXISTS (SELECT [SystemSettingCode] FROM [common].[SystemSetting] WHERE [SystemSettingCode] = 'FeatureBooking' AND [Value] = 'Enabled') 
    BEGIN

        IF NOT EXISTS (SELECT * FROM sys.schemas s WHERE s.name='booking')
        BEGIN
            BEGIN
                /* Create Booking Tables and Schema */
                EXEC ('CREATE SCHEMA [booking]');

                -- ************************************** [booking].[BookingRecurranceFrequency]
                CREATE TABLE [booking].[BookingRecurranceFrequency]
                (
                 [BookingRecurranceFrequencyId] tinyint NOT NULL ,
                 [Label]                        nvarchar(60) NOT NULL ,
                 [SortOrder]                    tinyint NULL ,
                 [IsEnabled]                    bit NOT NULL CONSTRAINT [DF_BookingRecurranceFrequency_IsEnabled] DEFAULT 1 ,

                 CONSTRAINT [PK_BookingRecurranceFrequency] PRIMARY KEY CLUSTERED ([BookingRecurranceFrequencyId] ASC)
                );

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Booking Recurrence Frequency (pattern). 1 - Daily,2 - Weekly, 3 - Monthly, 4 - Yearly.', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecurranceFrequency';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Booking Recurrence Frequency (pattern). 1 - Daily,2 - Weekly, 3 - Monthly, 4 - Yearly.', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecurranceFrequency', @level2type=N'COLUMN', @level2name=N'BookingRecurranceFrequencyId';

                -- ************************************** [booking].[BookingRecurOnPosition]
                CREATE TABLE [booking].[BookingRecurOnPosition]
                (
                 [BookingRecurOnPositionId] tinyint NOT NULL ,
                 [Label]                    nvarchar(60) NOT NULL ,
                 [SortOrder]                tinyint NULL ,
                 [IsEnabled]                bit NOT NULL CONSTRAINT [DF_BookingRecurOnPosition_IsEnabled] DEFAULT 1 ,

                 CONSTRAINT [PK_BookingRecurOnPosition] PRIMARY KEY CLUSTERED ([BookingRecurOnPositionId] ASC)
                );

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'When a booking needs to occur on the 1st, 2nd, etc Tuesday (weekday) of a month.
                1 - First, 2 - Second, 3 - Third, 4 - Fourth, 9 - Last', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecurOnPosition';


                -- ************************************** [booking].[BookingRecurOn]
                CREATE TABLE [booking].[BookingRecurOn]
                (
                 [BookingRecurOnId] tinyint NOT NULL ,
                 [Label]            nvarchar(60) NOT NULL ,
                 [SortOrder]        tinyint NULL ,
                 [IsEnabled]        bit NOT NULL CONSTRAINT [DF_BookingRecurOn_IsEnabled] DEFAULT 1 ,

                 CONSTRAINT [PK_BookingRecurOn] PRIMARY KEY CLUSTERED ([BookingRecurOnId] ASC)
                );

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'When a booking will recur on.
                1 - Day of Month, 2 -  Week Day, 3 - Weekend Day, 11 - Monday, 12 - Tuesday, 13 - Wednesday, 14 - Thursday, 15 - Friday, 16 - Saturday, 17 - Sunday.', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecurOn';

                CREATE TABLE [booking].[BookingRecordStatus]
                (
                 [BookingRecordStatusId] tinyint NOT NULL ,
                 [Label]                 nvarchar(100) NOT NULL ,
                 [SortOrder]             tinyint NOT NULL ,
                 [IsEnabled]             bit NOT NULL CONSTRAINT [DF_BookingRecordStatus_IsEnabled] DEFAULT 1 ,
                 [Icon]                  varchar(60) NULL ,
                 [Colour]                varchar(10) NULL ,

                 CONSTRAINT [PK_BookingRecordStatus] PRIMARY KEY CLUSTERED ([BookingRecordStatusId] ASC)
                );

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Defines the possible status a Booking Record can have. Draft, Booked, Open, Cancelled, Closed,', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecordStatus';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Defines the possible status a Booking Record can have. Draft, Booked, Open, Cancelled, Closed,', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecordStatus', @level2type=N'COLUMN', @level2name=N'BookingRecordStatusId';

                -- ************************************** [booking].[AvailabilityMode]
                CREATE TABLE [booking].[AvailabilityMode]
                (
                 [AvailabilityModeId] tinyint NOT NULL ,
                 [Label]              nvarchar(60) NOT NULL ,

                 CONSTRAINT [PK_AvailabilityMode] PRIMARY KEY CLUSTERED ([AvailabilityModeId] ASC)
                );

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Identifies the type of Booking Item Availability Record. 0 Is NOT Available. 1 Is Available.', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'AvailabilityMode';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Identifies the type of Availability Record for a booking item.
                It can be 
                1 - Is Available
                0 - Is NOT Available', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'AvailabilityMode', @level2type=N'COLUMN', @level2name=N'AvailabilityModeId';

                -- ************************************** [booking].[BookingRecurring]
                CREATE TABLE [booking].[BookingRecurring]
                (
                 [BookingRecurringId]            int IDENTITY (1, 1) NOT NULL ,
                 [StartsFrom]                    datetimeoffset(7) NOT NULL ,
                 [EndsOn]                        datetimeoffset(7) NULL ,
                 [FromTime]                      time NULL ,
                 [ToTime]                        time NULL ,
                 [IsAllDay]                      bit NOT NULL CONSTRAINT [DF_BookingRecurring_IsAllDay] DEFAULT 0 ,
                 [NumberOfFullDays]              int NOT NULL CONSTRAINT [DF_BookingRecurring_NumberOfFullDays] DEFAULT 0 ,
                 [BookingRecurranceFrequencyId]  tinyint NOT NULL ,
                 [RecurEveryX]                   smallint NULL ,
                 [RecurOnDayOfMonth]             tinyint NULL ,
                 [RecurOnMonth]                  tinyint NULL ,
                 [BookingRecurOnPositionId]      tinyint NOT NULL ,
                 [BookingRecurOnId]              tinyint NOT NULL ,
                 [IncludePublicHolidays]         bit NOT NULL CONSTRAINT [DF_BookingRecurring_IncludePublicHolidays] DEFAULT 1 ,
                 [IncludeMonday]                 bit NOT NULL CONSTRAINT [DF_BookingRecurring_IncludeMonday] DEFAULT 0 ,
                 [IncludeTuesday]                bit NOT NULL CONSTRAINT [DF_BookingRecurring_IncludeTuesday] DEFAULT 0 ,
                 [IncludeWednesday]              bit NOT NULL CONSTRAINT [DF_BookingRecurring_IncludeWednesday] DEFAULT 0 ,
                 [IncludeThursday]               bit NOT NULL CONSTRAINT [DF_BookingRecurring_IncludeThursday] DEFAULT 0 ,
                 [IncludeFriday]                 bit NOT NULL CONSTRAINT [DF_BookingRecurring_IncludeFriday] DEFAULT 0 ,
                 [IncludeSaturday]               bit NOT NULL CONSTRAINT [DF_BookingRecurring_IncludeSaturday] DEFAULT 0 ,
                 [IncludeSunday]                 bit NOT NULL CONSTRAINT [DF_BookingRecurring_IncludeSunday] DEFAULT 0 ,
                 [Note]                          nvarchar(2000) NULL ,
                 [CreatedOn]                     datetimeoffset(7) NOT NULL , -- From template: "CreatedAndModified"
                 [CreatedByName]                 nvarchar(60) NULL , -- From template: "CreatedAndModified"
                 [ModifiedOn]                    datetimeoffset(7) NULL , -- From template: "CreatedAndModified"
                 [ModifiedByName]                nvarchar(60) NULL , -- From template: "CreatedAndModified"
                 [Deleted]                       bit NOT NULL , -- From template: "CreatedAndModified"
                 [TenantId]                      int NULL ,
                 [BookingOwnerParentEntityType]  varchar(60) NOT NULL ,
                 [BookingOwnerParentEntityId]    uniqueidentifier NULL ,
                 [BookingOwnerParentEntityIntId] bigint NULL ,
                 [ForwardRecordsCreatedToDate]   datetimeoffset(7) NULL ,
                 [PickupFromHoldingLocationId]   int NULL,
                 [ReturnToHoldingLocationId]     int NULL,

                 CONSTRAINT [PK_BookingRecurring] PRIMARY KEY CLUSTERED ([BookingRecurringId] ASC),
                 CONSTRAINT [FK_BookingRecurring_BookingRecurranceFrequency] FOREIGN KEY ([BookingRecurranceFrequencyId])  REFERENCES [booking].[BookingRecurranceFrequency]([BookingRecurranceFrequencyId]),
                 CONSTRAINT [FK_BookingRecurring_BookingRecurOnPosition] FOREIGN KEY ([BookingRecurOnPositionId])  REFERENCES [booking].[BookingRecurOnPosition]([BookingRecurOnPositionId]),
                 CONSTRAINT [FK_BookingRecurring_BookingRecurOn] FOREIGN KEY ([BookingRecurOnId])  REFERENCES [booking].[BookingRecurOn]([BookingRecurOnId])
                );

                CREATE NONCLUSTERED INDEX [IX_BookingRecurring_DelOwnerIdTypeStartsFromEndsOn] ON [booking].[BookingRecurring] 
                 (
                  [Deleted] ASC, 
                  [BookingOwnerParentEntityId] ASC, 
                  [BookingOwnerParentEntityType] ASC, 
                  [StartsFrom] ASC, 
                  [EndsOn] ASC
                 )

                CREATE NONCLUSTERED INDEX [IX_BookingRecurring_DelTenantStartsFromEndsOn] ON [booking].[BookingRecurring] 
                 (
                  [Deleted] ASC, 
                  [TenantId] ASC, 
                  [StartsFrom] ASC, 
                  [EndsOn] ASC
                 )

                CREATE NONCLUSTERED INDEX [IX_IX_BookingRecurring_DelOwnerIntIdTypeStartsFromEndsOn] ON [booking].[BookingRecurring] 
                 (
                  [Deleted] ASC, 
                  [BookingOwnerParentEntityIntId] ASC, 
                  [BookingOwnerParentEntityType] ASC, 
                  [StartsFrom] ASC, 
                  [EndsOn] ASC
                 )

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'This is the master record for a recurring booking. This is used to generate the actual BookingRecord''s.', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecurring';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The date from which the recurring booking commenced', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecurring', @level2type=N'COLUMN', @level2name=N'StartsFrom';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Optional end date when the recurring booking will stop. If empty the recurring booking will continue forever', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecurring', @level2type=N'COLUMN', @level2name=N'EndsOn';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The time the booking commcences', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecurring', @level2type=N'COLUMN', @level2name=N'FromTime';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The Time the booking ends', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecurring', @level2type=N'COLUMN', @level2name=N'ToTime';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'When true indicates this is a full day booking', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecurring', @level2type=N'COLUMN', @level2name=N'IsAllDay';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'For a booking that has a duration longer than 1 day this tracks the total number of full days.', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecurring', @level2type=N'COLUMN', @level2name=N'NumberOfFullDays';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Booking Recurrence Frequency (pattern). 1 - Daily,2 - Weekly, 3 - Monthly, 4 - Yearly.', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecurring', @level2type=N'COLUMN', @level2name=N'BookingRecurranceFrequencyId';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'If a frequency requires a count. Eg. Every X Days, or Every X Weeks. This count is the X value.', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecurring', @level2type=N'COLUMN', @level2name=N'RecurEveryX';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The Day of the month that a Monthly or Yearly frequency recurring booking is to occur on.', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecurring', @level2type=N'COLUMN', @level2name=N'RecurOnDayOfMonth';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The Month (1 to 12) that a Yearly recurring booking is to occur on', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecurring', @level2type=N'COLUMN', @level2name=N'RecurOnMonth';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'When a booking needs to occur on the 1st, 2nd, etc Tuesday (weekday) of a month.
                1 - First, 2 - Second, 3 - Third, 4 - Fourth, 9 - Last', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecurring', @level2type=N'COLUMN', @level2name=N'BookingRecurOnPositionId';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'When a booking will recur on.
                1 - Day of Month, 2 -  Week Day, 3 - Weekend Day, 11 - Monday, 12 - Tuesday, 13 - Wednesday, 14 - Thursday, 15 - Friday, 16 - Saturday, 17 - Sunday.

                Works with BookingRecurOnPositionId to support 3rd Tuesday of Month, 1st Weekday of Month, Last Friday of Month, Last Day of Month.', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecurring', @level2type=N'COLUMN', @level2name=N'BookingRecurOnId';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'When true indicates this booking is to include public holidays.', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecurring', @level2type=N'COLUMN', @level2name=N'IncludePublicHolidays';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'BookingOwnerParentEntity... links to the party/user/thing that owns the recurring booking.', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecurring', @level2type=N'COLUMN', @level2name=N'BookingOwnerParentEntityType';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'This is the datetime of the most future record that has been created for the recurring booking in the BookingRecord table.', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecurring', @level2type=N'COLUMN', @level2name=N'ForwardRecordsCreatedToDate';



                CREATE TABLE [booking].[HoldingLocation]
                (
                 [HoldingLocationId]         int NOT NULL ,
                 [LocationParentEntityType]  varchar(60) NOT NULL ,
                 [LocationParentEntityId]    uniqueidentifier NULL ,
                 [LocationParentEntityIntId] bigint NULL ,
                 [TenantId]                  int NULL,
                 [Deleted]                   bit NOT NULL,

                 CONSTRAINT [PK_HoldingLocation] PRIMARY KEY CLUSTERED ([HoldingLocationId] ASC)
                );

                CREATE NONCLUSTERED INDEX [IX_HoldingLocation_ParentId] ON [booking].[HoldingLocation] 
                 (
                  [LocationParentEntityId] ASC, 
                  [LocationParentEntityType] ASC
                 )

                CREATE NONCLUSTERED INDEX [IX_HoldingLocation_ParentIntId] ON [booking].[HoldingLocation] 
                 (
                  [LocationParentEntityIntId] ASC, 
                  [LocationParentEntityType] ASC
                 )

                CREATE NONCLUSTERED INDEX [IX_HoldingLocation_TenantId] ON [booking].[HoldingLocation] 
                 (
                  [TenantId] ASC
                 )

                -- ************************************** [booking].[BookingItem]
                CREATE TABLE [booking].[BookingItem]
                (
                 [BookingItemId]                    int IDENTITY (1, 1) NOT NULL ,
                 [ParentEntityType]                 varchar(60) NOT NULL ,
                 [ParentEntityId]                   uniqueidentifier NULL ,
                 [ParentEntityIntId]                bigint NULL ,
                 [TimeZoneIanaId]                   varchar(60) NULL ,
                 [MinimumGapBetweenBookingsMinutes] smallint NOT NULL CONSTRAINT [DF_BookingItem_MinimumGapBetweenBookingsMinutes] DEFAULT 0 ,
                 [MinimumBookingTimeInMinutes]      smallint NOT NULL CONSTRAINT [DF_BookingItem_MinimumBookingTimeInMinutes] DEFAULT 0 ,
                 [MaximumBookingTimeInMinutes]      smallint NOT NULL CONSTRAINT [DF_BookingItem_MaximumBookingTimeInMinutes] DEFAULT 0 ,
                 [MaximumBookingIntoFutureDays]     smallint NOT NULL CONSTRAINT [DF_BookingItem_MaximumBookingIntoFutureDays] DEFAULT 0 ,
                 [IsRecurringBookingEnabled]        bit NOT NULL CONSTRAINT [DF_BookingItem_IsRecurringBookingEnabled] DEFAULT 1 ,
                 [IsEnabled]                        bit NOT NULL CONSTRAINT [DF_BookingItem_IsEnabled] DEFAULT 1 ,
                 [Note]                             nvarchar(2000) NULL ,
                 [PublicHolidaySetId]               int NULL ,
                 [CreatedOn]                        datetimeoffset(7) NOT NULL , -- From template: "CreatedAndModified"
                 [CreatedByName]                    nvarchar(60) NULL , -- From template: "CreatedAndModified"
                 [ModifiedOn]                       datetimeoffset(7) NULL , -- From template: "CreatedAndModified"
                 [ModifiedByName]                   nvarchar(60) NULL , -- From template: "CreatedAndModified"
                 [Deleted]                          bit NOT NULL , -- From template: "CreatedAndModified"
                 [TenantId]                         int NULL ,
                 [HomeHoldingLocationId]            int NULL,

                  CONSTRAINT [PK_BookingItem] PRIMARY KEY CLUSTERED ([BookingItemId] ASC),
                  CONSTRAINT [FK_BookingItem_HomeHoldingLocationId] FOREIGN KEY ([HomeHoldingLocationId])  REFERENCES [booking].[HoldingLocation]([HoldingLocationId])
                );

                CREATE NONCLUSTERED INDEX [IX_BookingItem_DelParentEntityIdTypeTenant] ON [booking].[BookingItem] 
                 (
                  [Deleted] ASC, 
                  [ParentEntityId] ASC, 
                  [ParentEntityType] ASC, 
                  [TenantId] ASC, 
                  [IsEnabled] ASC
                 )

                CREATE NONCLUSTERED INDEX [IX_BookingItem_DelParentEntityIntIdTypeTenant] ON [booking].[BookingItem] 
                 (
                  [Deleted] ASC, 
                  [ParentEntityIntId] ASC, 
                  [ParentEntityType] ASC, 
                  [TenantId] ASC, 
                  [IsEnabled] ASC
                 )

                CREATE NONCLUSTERED INDEX [IX_BookingItem_DelPublicHolidaySetId] ON [booking].[BookingItem] 
                 (
                  [Deleted] ASC, 
                  [PublicHolidaySetId] ASC, 
                  [TenantId] ASC
                 )

                CREATE NONCLUSTERED INDEX [IX_BookingItem_DelTenantIdIsEnabled] ON [booking].[BookingItem] 
                 (
                  [Deleted] ASC, 
                  [TenantId] ASC, 
                  [IsEnabled] ASC
                 )
                CREATE NONCLUSTERED INDEX [IX_BookingItem_DelHomeHoldingLocationTenantEnabled] ON [booking].[BookingItem] 
                 (
                  [Deleted] ASC, 
                  [HomeHoldingLocationId] ASC, 
                  [TenantId] ASC,
                  [IsEnabled] ASC
                 )

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'A BookingItem represents a thing, person, place, or group that can be booked. The ParentEntity*** is used to link this Item to the actual Item (Listing, Party, Order, Product, etc)', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingItem';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The ParentEntity... links to the thing/person/group, etc that is the Booking Item', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingItem', @level2type=N'COLUMN', @level2name=N'ParentEntityType';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'This is the minimum time in minutes that must be allowed between bookings. 0 indicates a new booking can start as soon as the previous booking ends', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingItem', @level2type=N'COLUMN', @level2name=N'MinimumGapBetweenBookingsMinutes';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The minimum booking duration allowed in minutes', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingItem', @level2type=N'COLUMN', @level2name=N'MinimumBookingTimeInMinutes';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The maximum booking duration allowed in minutes', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingItem', @level2type=N'COLUMN', @level2name=N'MaximumBookingTimeInMinutes';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The maximum days into the future that a booking can be made for this item', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingItem', @level2type=N'COLUMN', @level2name=N'MaximumBookingIntoFutureDays';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'When true indicates recurring bookings are allowed. When false a recurring booking cannot be made for this item', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingItem', @level2type=N'COLUMN', @level2name=N'IsRecurringBookingEnabled';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The Public Holiday''s set that is applicable to this item', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingItem', @level2type=N'COLUMN', @level2name=N'PublicHolidaySetId';

                -- ************************************** [booking].[BookingRecurringItem]
                CREATE TABLE [booking].[BookingRecurringItem]
                (
                 [BookingRecurringItemId] bigint IDENTITY (1, 1) NOT NULL ,
                 [BookingItemId]          int NOT NULL ,
                 [BookingRecurringId]     int NOT NULL ,
                 [CreatedOn]              datetimeoffset(7) NOT NULL , -- From template: "CreatedAndModified"
                 [CreatedByName]          nvarchar(60) NULL , -- From template: "CreatedAndModified"
                 [ModifiedOn]             datetimeoffset(7) NULL , -- From template: "CreatedAndModified"
                 [ModifiedByName]         nvarchar(60) NULL , -- From template: "CreatedAndModified"
                 [Deleted]                bit NOT NULL,  -- From template: "CreatedAndModified"

                 CONSTRAINT [PK_BookingRecurringItem] PRIMARY KEY CLUSTERED ([BookingRecurringItemId] ASC),
                 CONSTRAINT [FK_BookingRecurringItem_BookingRecurring] FOREIGN KEY ([BookingRecurringId])  REFERENCES [booking].[BookingRecurring]([BookingRecurringId]),
                 CONSTRAINT [FK_BookingRecurringItem_BookingItem] FOREIGN KEY ([BookingItemId])  REFERENCES [booking].[BookingItem]([BookingItemId])
                );

                CREATE NONCLUSTERED INDEX [IX_BookingRecurringItem_DelBookingItemId] ON [booking].[BookingRecurringItem] 
                 (
                  [Deleted] ASC, 
                  [BookingItemId] ASC
                 )
                 INCLUDE (
                  [BookingRecurringId]
                 )


                CREATE NONCLUSTERED INDEX [IX_BookingRecurringItem_DelBookingRecurringId] ON [booking].[BookingRecurringItem] 
                 (
                  [Deleted] ASC, 
                  [BookingRecurringId] ASC
                 )
                 INCLUDE (
                  [BookingItemId]
                 )


                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'A BookingRecurringItem represents the items that are part of a recurring booking. It supports many items been included as part of a recurring booking. An item could be a Product, Listing, Party (person/org/team), Order (job).', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecurringItem';

                -- ************************************** [booking].[BookingRecord]
                CREATE TABLE [booking].[BookingRecord]
                (
                 [BookingRecordId]               bigint IDENTITY (1, 1) NOT NULL ,
                 [BookingRecordReference]        varchar(60) NULL,
                 [BookingRecurringId]            int NULL ,
                 [IsRecurringBookingCustomised]  bit NOT NULL CONSTRAINT [DF_BookingRecord_IsRecurringBookingCustomised] DEFAULT 0 ,
                 [FromDateTime]                  datetimeoffset(7) NOT NULL ,
                 [ToDateTime]                    datetimeoffset(7) NOT NULL ,
                 [BookingRecordStatusId]         tinyint NOT NULL ,
                 [BookingOwnerParentEntityId]    uniqueidentifier NULL ,
                 [BookingOwnerParentEntityIntId] bigint NULL ,
                 [BookingOwnerParentEntityType]  varchar(60) NOT NULL ,
                 [CreatedOn]                     datetimeoffset(7) NOT NULL , -- From template: "CreatedAndModified"
                 [CreatedByName]                 nvarchar(60) NULL , -- From template: "CreatedAndModified"
                 [ModifiedOn]                    datetimeoffset(7) NULL , -- From template: "CreatedAndModified"
                 [ModifiedByName]                nvarchar(60) NULL , -- From template: "CreatedAndModified"
                 [Deleted]                       bit NOT NULL , -- From template: "CreatedAndModified"
                 [Note]                          nvarchar(2000) NULL ,
                 [TenantId]                      int NULL ,
                 [PickupFromHoldingLocationId]   int NULL,
                 [ReturnToHoldingLocationId]     int NULL,

                 CONSTRAINT [PK_BookingRecord] PRIMARY KEY CLUSTERED ([BookingRecordId] ASC),
                 CONSTRAINT [FK_BookingRecord_BookingRecurring] FOREIGN KEY ([BookingRecurringId])  REFERENCES [booking].[BookingRecurring]([BookingRecurringId]),
                 CONSTRAINT [FK_BookingRecord_BookingRecordStatus] FOREIGN KEY ([BookingRecordStatusId])  REFERENCES [booking].[BookingRecordStatus]([BookingRecordStatusId]),
                 CONSTRAINT [FK_BookingRecord_PickupFromHoldingLocation] FOREIGN KEY ([PickupFromHoldingLocationId])  REFERENCES [booking].[HoldingLocation]([HoldingLocationId]),
                 CONSTRAINT [FK_BookingRecord_ReturnToHoldingLocation] FOREIGN KEY ([ReturnToHoldingLocationId])  REFERENCES [booking].[HoldingLocation]([HoldingLocationId])
                );

                CREATE NONCLUSTERED INDEX [IX_BookingRecord_BookingRecurringDelFromDate] ON [booking].[BookingRecord] 
                 (
                  [BookingRecurringId] ASC, 
                  [Deleted] ASC, 
                  [FromDateTime] ASC, 
                  [IsRecurringBookingCustomised] ASC, 
                  [BookingRecordStatusId] ASC
                 )
                 INCLUDE (
                  [BookingRecordId]
                 )

                CREATE NONCLUSTERED INDEX [IX_BookingRecord_DelBookingOwnerIdFromDate] ON [booking].[BookingRecord] 
                 (
                  [Deleted] ASC, 
                  [BookingOwnerParentEntityId] ASC, 
                  [BookingOwnerParentEntityType] ASC, 
                  [FromDateTime] ASC, 
                  [BookingRecordStatusId] ASC
                 )

                CREATE NONCLUSTERED INDEX [IX_BookingRecord_TenantDelStatusFromDate] ON [booking].[BookingRecord] 
                 (
                  [TenantId] ASC, 
                  [Deleted] ASC, 
                  [BookingRecordStatusId] ASC, 
                  [FromDateTime] ASC
                 )

                CREATE NONCLUSTERED INDEX [IX_BookingRecord_TenantIdDelFromDate] ON [booking].[BookingRecord] 
                 (
                  [TenantId] ASC, 
                  [Deleted] ASC, 
                  [FromDateTime] ASC, 
                  [ToDateTime] ASC, 
                  [BookingRecordStatusId] ASC
                 )
                 INCLUDE (
                  [BookingRecordId]
                 )

                CREATE NONCLUSTERED INDEX [IX_IX_BookingRecord_DelBookingOwnerIntIdFromDate] ON [booking].[BookingRecord] 
                 (
                  [Deleted] ASC, 
                  [BookingOwnerParentEntityIntId] ASC, 
                  [BookingOwnerParentEntityType] ASC, 
                  [FromDateTime] ASC, 
                  [BookingRecordStatusId] ASC
                 )

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'A Booking Record tracks a booking of something for a continuous period (ie. from minutes, to days, weeks or even years).
                The items that have been booked are in the BookingRecordItem table (thus allowing a single booking to include many items).', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecord';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'When true indicates this recurring booking record has been customised.', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecord', @level2type=N'COLUMN', @level2name=N'IsRecurringBookingCustomised';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The datetime the booking starts from', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecord', @level2type=N'COLUMN', @level2name=N'FromDateTime';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The datetime when the booking ends', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecord', @level2type=N'COLUMN', @level2name=N'ToDateTime';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Defines the possible status a Booking Record can have. Draft, Booked, Open, Cancelled, Closed,', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecord', @level2type=N'COLUMN', @level2name=N'BookingRecordStatusId';

                -- ************************************** [booking].[BookingItemHoldingLocation]
                CREATE TABLE [booking].[BookingItemHoldingLocation]
                (
                 [BookingItemHoldingLocationId] int IDENTITY (1, 1) NOT NULL ,
                 [BookingItemId]                int NOT NULL ,
                 [FromDateTime]                 datetimeoffset(7) NOT NULL ,
                 [ToDateTime]                   datetimeoffset(7) NULL ,
                 [CreatedOn]                    datetimeoffset(7) NOT NULL , -- From template: "CreatedAndModified"
                 [CreatedByName]                nvarchar(60) NULL , -- From template: "CreatedAndModified"
                 [ModifiedOn]                   datetimeoffset(7) NULL , -- From template: "CreatedAndModified"
                 [ModifiedByName]               nvarchar(60) NULL , -- From template: "CreatedAndModified"
                 [Deleted]                      bit NOT NULL , -- From template: "CreatedAndModified"
                 [HoldingLocationId]            int NULL ,

                 CONSTRAINT [PK_BookingItemHoldingLocation] PRIMARY KEY CLUSTERED ([BookingItemHoldingLocationId] ASC),
                 CONSTRAINT [FK_BookingItemHoldingLocation_BookingItem] FOREIGN KEY ([BookingItemId])  REFERENCES [booking].[BookingItem]([BookingItemId]),
                 CONSTRAINT [FK_BookingItemHoldingLocation_HoldingLocation] FOREIGN KEY ([HoldingLocationId])  REFERENCES [booking].[HoldingLocation]([HoldingLocationId])
                );

                CREATE NONCLUSTERED INDEX [IX_BookingItemHoldingLocation_BookingItemIdFromDateToDate] ON [booking].[BookingItemHoldingLocation] 
                 (
                  [Deleted] ASC, 
                  [BookingItemId] ASC, 
                  [FromDateTime] ASC, 
                  [ToDateTime] ASC
                 )

                CREATE NONCLUSTERED INDEX [IX_BookingItemHoldingLocation_DelHoldingLocationFromDateToDate] ON [booking].[BookingItemHoldingLocation] 
                 (
                  [Deleted] ASC, 
                  HoldingLocationId ASC,
                  [FromDateTime] ASC, 
                  [ToDateTime] ASC
                 )
                 INCLUDE (
                  [BookingItemId]
                 )


                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The BookingItemHoldingLocation tracks where a Booking Item is located when not in use. This may change over time (either temporarily or permanently). 
                For example a Loan Car may normally be at a City Head Office, But may also be driven to another city and then be made available there.', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingItemHoldingLocation';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The ParentEntity... represents the location of the item.', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingItemHoldingLocation', @level2type=N'COLUMN', @level2name=N'LocationParentEntityType';

                -- ************************************** [booking].[BookingItemAvailability]
                CREATE TABLE [booking].[BookingItemAvailability]
                (
                 [BookingItemAvailabilityId] int IDENTITY (1, 1) NOT NULL ,
                 [BookingItemId]             int NOT NULL ,
                 [AvailabilityModeId]        tinyint NOT NULL ,
                 [FromDate]                  datetimeoffset(7) NULL ,
                 [ToDate]                    datetimeoffset(7) NULL ,
                 [FromTime]                  time NULL ,
                 [ToTime]                    time NULL ,
                 [IncludeMonday]             bit NOT NULL CONSTRAINT [DF_BookingItemAvailability_IncludeMonday] DEFAULT 1 ,
                 [IncludeTuesday]            bit NOT NULL CONSTRAINT [DF_BookingItemAvailability_IncludeTuesday] DEFAULT 1 ,
                 [IncludeWednesday]          bit NOT NULL CONSTRAINT [DF_BookingItemAvailability_IncludeWednesday] DEFAULT 1 ,
                 [IncludeThursday]           bit NOT NULL CONSTRAINT [DF_BookingItemAvailability_IncludeThursday] DEFAULT 1 ,
                 [IncludeFriday]             bit NOT NULL CONSTRAINT [DF_BookingItemAvailability_IncludeFriday] DEFAULT 1 ,
                 [IncludeSaturday]           bit NOT NULL CONSTRAINT [DF_BookingItemAvailability_IncludeSaturday] DEFAULT 1 ,
                 [IncludeSunday]             bit NOT NULL CONSTRAINT [DF_BookingItemAvailability_IncludeSunday] DEFAULT 1 ,
                 [IncludePublicHolidays]     bit NOT NULL CONSTRAINT [DF_BookingItemAvailability_IncludePublicHolidays] DEFAULT 1 ,
                 [CreatedOn]                 datetimeoffset(7) NOT NULL , -- From template: "CreatedAndModified"
                 [CreatedByName]             nvarchar(60) NULL , -- From template: "CreatedAndModified"
                 [ModifiedOn]                datetimeoffset(7) NULL , -- From template: "CreatedAndModified"
                 [ModifiedByName]            nvarchar(60) NULL , -- From template: "CreatedAndModified"
                 [Deleted]                   bit NOT NULL  -- From template: "CreatedAndModified"

                 CONSTRAINT [PK_BookingItemAvailability] PRIMARY KEY CLUSTERED ([BookingItemAvailabilityId] ASC),
                 CONSTRAINT [FK_BookingItemAvailability_BookingItem] FOREIGN KEY ([BookingItemId])  REFERENCES [booking].[BookingItem]([BookingItemId]),
                 CONSTRAINT [FK_BookingItemAvailability_AvailabilityMode] FOREIGN KEY ([AvailabilityModeId])  REFERENCES [booking].[AvailabilityMode]([AvailabilityModeId])
                );

                CREATE NONCLUSTERED INDEX [IX_BookingItemAvailability_DelBookingItemIdFromDateToDateMode] ON [booking].[BookingItemAvailability] 
                 (
                  [Deleted] ASC, 
                  [BookingItemId] ASC, 
                  [FromDate] ASC, 
                  [ToDate] ASC, 
                  [AvailabilityModeId] ASC
                 )

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'BookingItemAvailability defines the availability of a booking item. If a booking item has no rows in here it is assumed it is always available.
                These rows may represent when an item is available or Is NOT available.', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingItemAvailability';

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Identifies the type of Availability Record for a booking item.
                It can be 
                1 - Is Available
                0 - Is NOT Available', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingItemAvailability', @level2type=N'COLUMN', @level2name=N'AvailabilityModeId';

                -- ************************************** [booking].[BookingRecordItem]
                CREATE TABLE [booking].[BookingRecordItem]
                (
                 [BookingRecordItemId] bigint IDENTITY (1, 1) NOT NULL ,
                 [BookingItemId]       int NOT NULL ,
                 [CreatedOn]           datetimeoffset(7) NOT NULL , -- From template: "CreatedAndModified"
                 [CreatedByName]       nvarchar(60) NULL , -- From template: "CreatedAndModified"
                 [ModifiedOn]          datetimeoffset(7) NULL , -- From template: "CreatedAndModified"
                 [ModifiedByName]      nvarchar(60) NULL , -- From template: "CreatedAndModified"
                 [Deleted]             bit NOT NULL , -- From template: "CreatedAndModified"
                 [BookingRecordId]     bigint NOT NULL ,

                 CONSTRAINT [PK_BookingRecordItem] PRIMARY KEY CLUSTERED ([BookingRecordItemId] ASC),
                 CONSTRAINT [FK_BookingRecordItem_BookingRecord] FOREIGN KEY ([BookingRecordId])  REFERENCES [booking].[BookingRecord]([BookingRecordId]),
                 CONSTRAINT [FK_BookingRecordItem_BookingItem] FOREIGN KEY ([BookingItemId])  REFERENCES [booking].[BookingItem]([BookingItemId])
                );

                CREATE NONCLUSTERED INDEX [IX_BookingRecordItem_BookingRecordIdDel] ON [booking].[BookingRecordItem] 
                 (
                  [Deleted] ASC, 
                  [BookingRecordId] ASC
                 )
                 INCLUDE (
                  [BookingItemId]
                 )

                CREATE NONCLUSTERED INDEX [IX_BookingRecordItem_DelBookingItemId] ON [booking].[BookingRecordItem] 
                 (
                  [Deleted] ASC, 
                  [BookingItemId] ASC
                 )
                 INCLUDE (
                  [BookingRecordId]
                 )

                EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'A BookingRecordItem represents the items that are part of a booking. It supports many items been included as part of a booking. An item could be a Product, Listing, Party (person/org/team), Order (job).', @level0type = N'SCHEMA', @level0name = N'booking', @level1type = N'TABLE', @level1name = N'BookingRecordItem';
            END -- Create Booking Tables

            BEGIN
                /* Add base booking data */
                INSERT INTO [booking].[BookingRecurranceFrequency]
                ([BookingRecurranceFrequencyId], [Label], [SortOrder], [IsEnabled])
                VALUES
                (1, 'Daily', 1, 1),
                (2, 'Weekly', 2, 1),
                (3, 'Monthly', 3, 1),
                (4, 'Yearly', 4, 1);

                INSERT INTO [booking].[BookingRecurOnPosition]
                ([BookingRecurOnPositionId], [Label], [SortOrder], [IsEnabled])
                VALUES
                (1, 'First', 1, 1),
                (2, 'Second', 2, 1),
                (3, 'Third', 3, 1),
                (4, 'Fourth', 4, 1),
                (9, 'Last', 5, 1),
                (10, 'Second Last', 6, 1),
                (11, 'Third Last', 7, 1);

                INSERT INTO [booking].[BookingRecurOn]
                ([BookingRecurOnId], [Label], [SortOrder], [IsEnabled])
                VALUES
                (1, 'Day of month', 1, 1),
                (2, 'Week day', 2, 1),
                (3, 'Weekend day', 3, 1),
                (11, 'Monday', 11, 1),
                (12, 'Tuesday', 12, 1),
                (13, 'Wednesday', 13, 1),
                (14, 'Thursday', 14, 1),
                (15, 'Friday', 15, 1),
                (16, 'Saturday', 16, 1),
                (17, 'Sunday', 17, 1);

                INSERT INTO [booking].[BookingRecordStatus] 
                ([BookingRecordStatusId], [Label], [SortOrder],[IsEnabled])
                VALUES
                (1, 'Draft', 1,1),
                (2, 'Booked', 2, 1),
                (3, 'Open', 3, 1),
                (4, 'Closed', 4, 1),
                (5, 'Cancelled', 5, 1);

                INSERT INTO [booking].[AvailabilityMode]
                (AvailabilityModeId, [Label])
                VALUES
                (1, 'Is Available'),
                (0, 'Is NOT Available');

                Insert Into [listing].[Listingtype]
                (ListingtypeId, Label)
                Values
                (81,'Holding Location');

            END -- Booking Data
        END -- Create Booking Schema and Base Data
    END -- Booking Feature Enabled
END -- ALL
GO
