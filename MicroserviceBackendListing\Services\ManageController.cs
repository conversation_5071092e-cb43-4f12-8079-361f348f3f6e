﻿using Amazon.Runtime;
using Castle.Core.Internal;
using MicroserviceBackendListing.BusinessLogic;
using MicroserviceBackendListing.BusinessLogic.Base;
using MicroserviceContract.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using MicroserviceContract.Dtos.ListingManagement;
using Microsoft.AspNetCore.Mvc;
using Sql;
using MicroserviceBackendListing.Policies;
using Microsoft.AspNetCore.Authorization;

namespace MicroserviceBackendListing.Services
{
    /// <summary>
    /// Allows Creation, Fetching, and Management of Listings.
    /// For general searching and public facing requests use the Listing endpoint.
    /// </summary>
    [Route("api/Manage")]
    public class ManageController : AppController
    {
        private readonly ManageListing _manageListing;
        private readonly ListingMedia _listingMedia;
        public ManageController(ManageListing manageListing, IUnitOfWork unitOfWork, ListingMedia listingMedia)
        {
            _manageListing = manageListing;
            _unitOfWork = unitOfWork;
            _listingMedia = listingMedia;
        }

        /// <summary>
        /// Get a single listing by ListingId
        /// </summary>
        /// <param name="listingId"></param>
        /// <param name="includeMedia">Set to false to not return the media array. Default is true</param>
        /// <param name="includeAttributes">Set to true to return Attributes with the response. Default is false</param>
        /// <param name="displayContainerCodes">Optional comma separated list of Display Container Codes to restrict the attributes returned.</param>
        /// <param name="displayGroupCode">Optional Display Group code. If set returns all display containers that are part of the group.</param>
        /// <param name="excludeAttributesWithNoData">Set to false to include all attributes available. Default is true, thus only attributes that have a value for the Listing are returned.</param>
        /// <param name="flattenToFields">Default false. When true returns a flattened dictionary of Fields instead of the nested Display Container Attributes list</param>
        /// <param name="includeTagsInResponse"></param>
        /// <response code="200">Listing returned, or empty listing if not found</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("GetListing")]
        [Authorize(Policy = PolicyType.ViewManageListingPolicy)]
        [ProducesResponseType(typeof(ManageListingWithDisplayGroupedDataAndMediaCDto), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetListingAsync(long listingId, bool includeMedia = true, bool includeAttributes = true, string? displayContainerCodes = "", bool? excludeAttributesWithNoData = true, string? displayGroupCode = ""
                                                         ,bool flattenToFields = false, bool includeTagsInResponse = true)
        {
            string[] displayContainerCodesArray = string.IsNullOrEmpty(displayContainerCodes) ? Array.Empty<string>() : displayContainerCodes.Split(',');
            var list = _manageListing;
            var result = await list.GetListingByListingId(listingId, includeMedia, includeAttributes, displayContainerCodesArray, excludeAttributesWithNoData, displayGroupCode, flattenToFields: flattenToFields, includeTagsInResponse: includeTagsInResponse);
            return Ok(result);
        }

        /// <summary>
        /// Get a single listing by ReferenceNo
        /// </summary>
        /// <param name="referenceNo"></param>
        /// <param name="includeMedia">Set to false to not return the media array. Default is true</param>
        /// <param name="includeAttributes">Set to true to return Attributes with the response. Default is false</param>
        /// <param name="displayContainerCodes">Optional comma separated list of Display Container Codes to restrict the attributes returned.</param>
        /// <param name="displayGroupCode">Optional Display Group code. If set returns all display containers that are part of the group.</param>
        /// <param name="excludeAttributesWithNoData">Set to false to include all attributes available. Default is true, thus only attributes that have a value for the Listing are returned.</param>
        /// <param name="flattenToFields">Default false. When true returns a flattened dictionary of Fields instead of the nested Display Container Attributes list</param>
        /// <param name="includeTagsInResponse"></param>
        /// <response code="200">Listing returned, or empty listing if not found</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("GetListingByRef")]
        [Authorize(Policy = PolicyType.ViewManageListingPolicy)]
        [ProducesResponseType(typeof(ManageListingWithDisplayGroupedDataAndMediaCDto), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetListingByReferenceNoAsync(string referenceNo, bool includeMedia = true, bool includeAttributes = true, string? displayContainerCodes = "", bool? excludeAttributesWithNoData = true, string? displayGroupCode = ""
                                                                      ,bool flattenToFields = false, bool includeTagsInResponse = true)
        {
            string[] displayContainerCodesArray = string.IsNullOrEmpty(displayContainerCodes) ? Array.Empty<string>() : displayContainerCodes.Split(',');
            var list = _manageListing;
            Int64 listingId = (Int64) await list.GetListingId(referenceNo);
            var result = await list.GetListingByListingId(listingId, includeMedia, includeAttributes, displayContainerCodesArray, excludeAttributesWithNoData, displayGroupCode, flattenToFields: flattenToFields, includeTagsInResponse: includeTagsInResponse);
            return Ok(result);
        }

        /// <summary>
        /// Get all attributes (data) associated with a listing
        /// </summary>
        /// <param name="ListingId"></param>
        /// <response code="200">Listing attributes returned, or empty listing if not found</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("GetListingData")]
        [Authorize(Policy = PolicyType.ViewManageListingPolicy)]
        [ProducesResponseType(typeof(List<ManageListingAttributeCDto>), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetListingDataAsync(long ListingId)
        {
            var list = _manageListing;
            var result = await list.GetListingDataAsync(ListingId);
            return Ok(result);
        }

        /// <summary>
        /// Get all attributes (data) associated with a listing. By listing Reference No.
        /// </summary>
        /// <param name="referenceNo"></param>
        /// <response code="200">List of Listing lttributes returned, or empty list if not found</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("GetListingDataByRef")]
        [Authorize(Policy = PolicyType.ViewManageListingPolicy)]
        [ProducesResponseType(typeof(List<ManageListingAttributeCDto>), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetListingDataByReferenceNoAsync(string referenceNo)
        {
            var list = _manageListing;
            Int64 listingId = (Int64) await list.GetListingId(referenceNo);
            var result = await list.GetListingDataAsync(listingId);
            return Ok(result);
        }

        /// <summary>
        /// Get all attributes (data) associated with a listing flattened as a dictionary
        /// </summary>
        /// <param name="ListingId"></param>
        /// <response code="200">Listing attributes returned, or empty listing if not found</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("GetListingDataFlattened")]
        [Authorize(Policy = PolicyType.ViewManageListingPolicy)]
        [ProducesResponseType(typeof(Dictionary<string, object?>), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetListingDataFlattenedAsync(long ListingId)
        {
            var list = _manageListing;
            var tempResult = await list.GetListingDataAsync(ListingId);
            var result = list.FlattenListingData(tempResult);
            return Ok(result);
        }

        /// <summary>
        /// Get all attributes (data) associated with a listing. By listing Reference No.
        /// </summary>
        /// <param name="referenceNo"></param>
        /// <response code="200">List of Listing lttributes returned, or empty list if not found</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("GetListingDataFlattenedByRef")]
        [Authorize(Policy = PolicyType.ViewManageListingPolicy)]
        [ProducesResponseType(typeof(Dictionary<string, object?>), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetListingDataFlattenedByReferenceNoAsync(string referenceNo)
        {
            var list = _manageListing;
            Int64 listingId = (Int64)await list.GetListingId(referenceNo);
            var tempResult = await list.GetListingDataAsync(listingId);
            var result = list.FlattenListingData(tempResult);
            return Ok(result);
        }

        /// <summary>
        /// Get one or more Display Containers for a listing and returns those with associated Attributes
        /// </summary>
        /// <param name="listingId"></param>
        /// <param name="displayContainerCodes">Optional comma separated list of Display Container Codes to restrict the attributes returned.</param>
        /// <param name="displayGroupCode">Optional Display Group code. If set returns all display containers that are part of the group.</param>
        /// <param name="excludeAttributesWithNoData">Set to false to include all attributes available. Default is true, thus only attributes that have a value for the Listing are returned.</param>
        /// <response code="200">Listing attributes returned, or empty listing if not found</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("GetListingDataForDisplayContainers")]
        [Authorize(Policy = PolicyType.ViewManageListingPolicy)]
        [ProducesResponseType(typeof(List<ManageListingDisplayContainerCDto>), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetListingDataForDisplayContainersAsync(long listingId, string? displayContainerCodes = "", bool? excludeAttributesWithNoData = true, string displayGroupCode = "")
        {
            string[] displayContainerCodesArray = string.IsNullOrEmpty(displayContainerCodes) ? Array.Empty<string>() : displayContainerCodes.Split(',');
            var list = _manageListing;
            var result = await list.GetAttributeDataAsync(listingId, displayContainerCodesArray, excludeAttributesWithNoData, displayGroupCode);
            return Ok(result);
        }

        /// <summary>
        /// Get one or more Display Containers for a listing and returns those with associated Attributes
        /// </summary>
        /// <param name="referenceNo"></param>
        /// <param name="displayContainerCodes">Optional comma separated list of Display Container Codes to restrict the attributes returned.</param>
        /// <param name="displayGroupCode">Optional Display Group code. If set returns all display containers that are part of the group.</param>
        /// <param name="excludeAttributesWithNoData">Set to false to include all attributes available. Default is true, thus only attributes that have a value for the Listing are returned.</param>
        /// <response code="200">List of Listing lttributes returned, or empty list if not found</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("GetListingDataForDisplayContainersByRef")]
        [Authorize(Policy = PolicyType.ViewManageListingPolicy)]
        [ProducesResponseType(typeof(List<ManageListingDisplayContainerCDto>), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetListingDataForDisplayContainersByReferenceNoAsync(string referenceNo, string? displayContainerCodes = "", bool? excludeAttributesWithNoData = true, string displayGroupCode = "")
        {
            string[] displayContainerCodesArray = string.IsNullOrEmpty(displayContainerCodes) ? Array.Empty<string>() : displayContainerCodes.Split(',');
            var list = _manageListing;
            Int64 listingId = (Int64)await list.GetListingId(referenceNo);
            var result = await list.GetAttributeDataAsync(listingId, displayContainerCodesArray, excludeAttributesWithNoData, displayGroupCode);
            return Ok(result);
        }

        //[HttpGet]
        //[Route("GetListingNote")]
        //public async Task<IActionResult> GetListingNoteAsync(long ListingId, bool? showDeletedImage = false)
        //{
        //    var list = _manageListing;
        //    var result = await list.GetAsync(ListingId, showDeletedImage);
        //    return Ok(result);
        //}

        //[HttpPost]
        //[Route("AddListingNote")]
        //public async Task<IActionResult> AddListingNoteAsync([FromBody] ManageListingCDto dto)
        //{
        //    var logic = _manageListing;
        //    await logic.CreateAsync(dto);
        //    await logic.PopulateListAsync(dto);
        //    _unitOfWork.Commit();
        //    ManageListingCDto result = await logic.GetAsync(dto.ListingId);
        //    return Ok(result);
        //}

        //[HttpPost]
        //[Route("UpdateListingNote")]
        //public async Task<IActionResult> UpdateListingNoteAsync([FromBody] ManageListingCDto dto)
        //{
        //    var logic = _manageListing;
        //    await logic.CreateAsync(dto);
        //    await logic.PopulateListAsync(dto);
        //    _unitOfWork.Commit();
        //    ManageListingCDto result = await logic.GetAsync(dto.ListingId);
        //    return Ok(result);
        //}

        //[HttpPost]
        //[Route("DeleteListingNote")]
        //public async Task<IActionResult> DeleteListingNoteAsync([FromQuery] long ListingId)
        //{
        //    var list = _manageListing;
        //    await list.DeleteAsync(ListingId);
        //    _unitOfWork.Commit();
        //    return Ok();
        //}

        /// <summary>
        /// Create a new listing (with no attributes or media)
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="supportOfflineReferenceCreate">Default false. When true it allows an offline client to create listings with a specified ReferenceNo.</param>
        /// <response code="200">New listing created</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("CreateListingBase")]
        [Authorize(Policy = PolicyType.CreateManageListingPolicy)]
        [ProducesResponseType(typeof(long), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> CreateListingBaseAsync([FromBody] ManageListingCDto dto, [FromQuery] bool? supportOfflineReferenceCreate = false)
        {
            ManageListingWithDisplayGroupedDataAndMediaCDto altDto = new ManageListingWithDisplayGroupedDataAndMediaCDto();
            altDto.Map(dto);
            var logic = _manageListing;
            long newListingId = await logic.CreateNewListing(altDto, supportOfflineReferenceCreate ?? false);
            _unitOfWork.Commit(); ;
            return Ok(newListingId);
        }

        /// <summary>
        /// Create a new listing with an optional list of Attributes and Media.
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="supportOfflineReferenceCreate">Default false. When true it allows an offline client to create listings with a specified ReferenceNo.</param>
        /// <response code="200">New listing created</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("CreateListingWithAttributes")]
        [Authorize(Policy = PolicyType.CreateManageListingPolicy)]
        [ProducesResponseType(typeof(long), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> CreateListingWithAttributesAsync([FromBody] ManageListingWithDataAndMediaCDto dto, [FromQuery] bool? supportOfflineReferenceCreate = false)
        {
            ManageListingWithDisplayGroupedDataAndMediaCDto altDto = new ManageListingWithDisplayGroupedDataAndMediaCDto();
            altDto.Map(dto);
            var logic = _manageListing;
            long newListingId = await logic.CreateNewListing(altDto, supportOfflineReferenceCreate ?? false); 
            await logic.CreateListingMediaAndAttributes(dto, newListingId);
            _unitOfWork.Commit();;
            return Ok(await logic.GetListingByListingId(newListingId, includeAttributes: true));
        }

        /// <summary>
        /// Create a new listing with Attributes grouped into Display Containers
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="supportOfflineReferenceCreate">Default false. When true it allows an offline client to create listings with a specified ReferenceNo.</param>
        /// <response code="200">New listing created</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("CreateListingWithDisplayContainers")]
        [Authorize(Policy = PolicyType.CreateManageListingPolicy)]
        [ProducesResponseType(typeof(long), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> CreateListingWithDisplayGroupedAttributesAsync([FromBody] ManageListingWithDisplayGroupedDataAndMediaCDto dto, [FromQuery] bool? supportOfflineReferenceCreate = false)
        {
            var logic = _manageListing;
            long newListingId = await logic.CreateNewListing(dto, supportOfflineReferenceCreate ?? false);
            await logic.CreateListingMediaAndDisplayGroupedAttributes(dto, newListingId);
            _unitOfWork.Commit();
            if (dto.ProfileListingMediaUrl != null)
            {
                var profileMediaListing = await _listingMedia.GetListingMediaFromUrl(dto.ProfileListingMediaUrl);
                dto.ProfileListingMediaId = profileMediaListing.ListingMediaId;
                await logic.UpdateListingWithDisplayContainers(dto);
            }
            _unitOfWork.Commit();
            string[]? displayContainerCodes = dto?.Attributes?.Select(x => x.DisplayContainerCode!)?.ToArray();

            return Ok(await logic.GetListingByListingId(newListingId, includeAttributes: displayContainerCodes != null, displayContainerCodesArray: displayContainerCodes ?? Array.Empty<string>()));
        }

        /// <summary>
        /// Update a listing with attributes and media
        /// Note! StatusCode cannot be updated by this call. Use UpdateListingStatus to update status
        /// </summary>
        /// <param name="dto"></param>
        /// <response code="200">Listing updated</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("UpdateListing")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> UpdateListingAsync([FromBody] ManageListingWithDataAndMediaCDto dto)
        {
            var list = _manageListing;
            long listingId = await list.UpdateListing(dto);
            _unitOfWork.Commit();
            return Ok();
        }

        /// <summary>
        /// Update a listing with attributes grouped by display container
        /// Note! StatusCode cannot be updated by this call. Use UpdateListingStatus to update status
        /// </summary>
        /// <param name="dto"></param>
        /// <response code="200">Listing updated</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("UpdateListingWithDisplayContainers")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> UpdateListingWithDisplayContainersAsync([FromBody] ManageListingWithDisplayGroupedDataAndMediaCDto dto)
        {
            var list = _manageListing;
            if (dto.ProfileListingMediaUrl != null)
            {
                var profileMediaListing = await _listingMedia.GetListingMediaFromUrl(dto.ProfileListingMediaUrl);
                dto.ProfileListingMediaId = profileMediaListing.ListingMediaId;
            }
            long listingId = await list.UpdateListingWithDisplayContainers(dto);
            _unitOfWork.Commit();
            return Ok();
        }

        /// <summary>
        /// Update status of listing
        /// </summary>
        /// <param name="listingId">The ListingId to update. Eg. Draft, Pending, Active, Paused, Expired </param>
        /// <param name="newStatusCode">New Listing Status Code</param>
        /// <response code="200">Operation successful</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("UpdateListingStatus")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> UpdateListingStatusAsync( Int64 listingId, string newStatusCode)
        {
            var list = _manageListing;
            await list.UpdateStatusAsync(listingId, newStatusCode);
            _unitOfWork.Commit();
            return Ok();
        }

        /// <summary>
        /// Update visibility of listing
        /// </summary>
        /// <param name="listingId">The ListingId to update. Eg. Draft, Pending, Active, Paused, Expired </param>
        /// <param name="newVisibilityId">0 - Public, 1 - Private, 2 - Members</param>
        /// <response code="200">Operation successful</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("UpdateListingVisibility")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> UpdateListingVisibilityAsync([FromBody] Int64 listingId, short newVisibilityId)
        {
            var list = _manageListing;
            await list.UpdateVisibilityAsync(listingId, newVisibilityId);
            _unitOfWork.Commit();
            return Ok();
        }

        /// <summary>
        /// Add one or more media to a listing
        /// </summary>
        /// <param name="ListingId">The ListingId to add media to</param>
        /// <param name="media">List of media to add</param>
        /// <response code="200">Operation successful</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("AddMedia")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> AddMediaAsync(long ListingId, [FromBody] List<ManageListingMediaCDto> media)
        {
            var list = _manageListing;
            foreach (var item in media)
            {
                await list.AddMedia(ListingId, item);
            }
            _unitOfWork.Commit();
            return Ok();
        }

        /// <summary>
        /// Delete a listing media record
        /// </summary>
        /// <param name="ListingId">ListingId which has image to delete</param>
        /// <param name="mediaIdList">Comma separated list of Media Id's to delete</param>
        /// <response code="200">Operation successful</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("DeleteMedia")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> DeleteMediaAsync(long ListingId, [FromBody] List<long> mediaIdList)
        {
            var list = _manageListing;
            await list.DeleteMedia(ListingId, mediaIdList);
            _unitOfWork.Commit();
            return Ok();
        }

        /// <summary>
        /// Delete a listing media record
        /// </summary>
        /// <param name="ListingId">ListingId which has image to delete</param>
        /// <param name="media"></param>
        /// <response code="200">Operation successful</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("UpdateMedia")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> UpdateMediaAsync(long ListingId, [FromBody] List<ManageListingMediaCDto> media)
        {
            var list = _manageListing;
            var existing = await _listingMedia.GetListingListMediaForListingId(ListingId);
            for (int i = 0,ilen =  existing.Count;i < ilen; i++)
            {
                if (existing[i].ListingMediaId != null && !media.Exists(x => x.ListingMediaId == existing[i].ListingMediaId))
                {
#pragma warning disable CS8629 // Nullable value type may be null.
                    await _listingMedia.DeleteAsync((long)existing[i].ListingMediaId, ListingId);
#pragma warning restore CS8629 // Nullable value type may be null.
                }
            }

            for (int i = 0, ilen = media.Count; i < ilen; i++)
            {
                await _listingMedia.UpdateAsync(media[i], ListingId);
            }
            _unitOfWork.Commit();
            return Ok();
        }

        /// <summary>
        /// Delete a listing.\
        /// Listing is logically deleted
        /// </summary>
        /// <param name="ListingId">The ListingId to be deleted.</param>
        /// <response code="200">Operation successful</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("DeleteListing")]
        [Authorize(Policy = PolicyType.DeleteManageListingPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> DeleteListingAsync([FromQuery] long ListingId)
        {
            var list = _manageListing;
            await list.DeleteAsync(ListingId);
            _unitOfWork.Commit();
            return Ok();
        }

        /// <summary>
        /// Get a list of Listing records
        /// </summary>
        /// <param name="standardListParameters"></param>
        /// <param name="query"></param>
        /// <param name="category"></param>
        /// <param name="parentListingId">Optional ParentListingId. Specify this to get all child listings for a listing</param>
        /// <param name="parentEntityIntId">Id Representing a listings owning entity. Optional. If not provided returns Listings with NO parentEntityId</param>
        /// <param name="parentEntityId">Id Representing a listings owning entity. Optional. If not provided returns Listings with NO parentEntityId</param>
        /// <param name="parentEntityId2"></param>
        /// <param name="mediaCategoryCode">Optionally specify the category of media to return. If not specified all are returned.</param>
        /// <param name="statusCode"></param>
        /// <param name="includeMedia">Default is true. When true all urls media associated with a listing are returned. MediaCategoryCode can be used to filter </param>
        /// <param name="subject">Search subject for text</param>
        /// <param name="returnAttributeCodes">A comma separated list of attribute codes that are to be returned with the listing</param>
        /// <param name="listingTypeId">The ListingTypeId. Default 0.</param>
        /// <param name="tenantId"></param>
        /// <param name="flattenToFields">Default false. When true returns a flattened dictionary of Fields instead of the nested Display Container Attributes list</param>
        /// <response code="200">Operation successful</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet] 
        [Route("GetList")]
        [Authorize(Policy = PolicyType.ViewManageListingListPolicy)]
        [ProducesResponseType(typeof(ListResponseDto<ManageListingWithDataAndMediaCDto>), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetListAsync([FromQuery] StandardListParameters standardListParameters, string? query = null, [FromQuery] string[]? category = null
                                                      , Int64? parentEntityIntId = null, Guid? parentEntityId = null, Guid? parentEntityId2 = null, Int64? parentListingId = null, string? returnAttributeCodes = null, string? subject = null
                                                      , bool includeMedia = false, string? mediaCategoryCode = null, string? statusCode = null, short listingTypeId = 0
                                                      , int? tenantId = null, bool flattenToFields = false)
        {
            string[] returnAttributeCodesArray = string.IsNullOrEmpty(returnAttributeCodes) ? Array.Empty<string>() : returnAttributeCodes.Split(',');
            var groupLogic = _manageListing;
            var result = await groupLogic.GetListAsync(standardListParameters, query, category, parentEntityIntId, parentEntityId, parentEntityId2, parentListingId, returnAttributeCodesArray, subject, includeMedia, statusCode, mediaCategoryCode, listingTypeId, tenantId, flattenToFields: flattenToFields);
            return Ok(result);
        }

        /// <summary>
        /// Fetch the next listing record based on the position of the Listing 'ReferenceNo' field passed in. 
        /// Will loop on the next Listing record if going backwards or forwards in a circular manner.
        /// </summary>
        /// <param name="referenceNo">'ReferenceNo' of Listing to be used as an anchor point to determine which listing record to fetch based on the 'isNext' flag; either next or previous.</param>
        /// <param name="isNext">If true, will fetch the next record . If false, will fetch the previous record.</param>
        /// <param name="includeMedia"></param>
        /// <param name="includeAttributes"></param>
        /// <param name="displayContainerCodes"></param>
        /// <param name="excludeAttributesWithNoData"></param>
        /// <param name="displayGroupCode"></param>
        /// <param name="includeTagsInResponse"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetNextListingByRef")]
        [Authorize(Policy = PolicyType.ViewManageListingListPolicy)]
        [ProducesResponseType(typeof(ManageListingWithDisplayGroupedDataAndMediaCDto), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetNextListingByRefAsync([FromQuery] string referenceNo, [FromQuery] bool isNext = true, [FromQuery] bool includeMedia = false, [FromQuery] bool includeAttributes = false, [FromQuery] string? displayContainerCodes = "", [FromQuery] bool? excludeAttributesWithNoData = true, [FromQuery] string? displayGroupCode = "", bool includeTagsInResponse = true)
        {
            string[] displayContainerCodesArray = string.IsNullOrEmpty(displayContainerCodes) ? Array.Empty<string>() : displayContainerCodes.Split(',');
            var result = await _manageListing.GetNextListingByRefAsync(referenceNo, isNext, includeMedia, includeAttributes, displayContainerCodesArray, excludeAttributesWithNoData, displayGroupCode, includeTagsInResponse: includeTagsInResponse);
            return Ok(result);
        }

        /// <summary>
        /// Get a list of Listing Audit records
        /// </summary>
        /// <param name="listingId">Id of listing to view audit for</param>
        /// <param name="Description"></param>
        /// <param name="beforeDate"></param>
        /// <param name="afterDate"></param>
        /// <response code="200">Operation successful</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("GetListingAuditRecords")]
        [Authorize(Policy = PolicyType.ViewManageListingPolicy)]
        [ProducesResponseType(typeof(List<ListingAuditCDto>), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetListingAuditRecords([FromQuery] long listingId, string? Description, DateTimeOffset? beforeDate, DateTimeOffset? afterDate)
        {
            var groupLogic = _manageListing;
            var result = await groupLogic.GetListingAuditRecords(listingId, Description, beforeDate, afterDate);
            return Ok(result);
        }
    }
}
