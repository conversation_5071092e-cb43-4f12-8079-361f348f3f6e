﻿using Castle.Core.Internal;
using MicroserviceBackendListing.BusinessLogic.Base;
using MicroserviceBackendListing.Dtos;
using MicroserviceContract.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using MicroserviceContract.Dtos.Editor;
using MicroserviceContract.Dtos.Listing;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sql;

namespace MicroserviceBackendListing.Services
{
    /// <summary>
    /// Allows fetching Display Containers with full details for editing attributes for a listing
    /// </summary>
    [Route("api/Editor")]
    public class EditorController : AppController
    {
        private readonly Editor _editor;
        public EditorController(Editor editor, IUnitOfWork unitOfWork)
        {
            _editor = editor;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get details for a display container including all attributes
        /// Each attribute includes details of the input field type and any selections
        /// </summary>
        /// <param name="displayContainerCodes">Optional, Comma separated list of Display Container Codes to return attributes for.</param>
        /// <param name="returnAttributeSelections">Default true. Return the available selections/dropdown values for each attribute</param>
        /// <response code="200">Display container returned</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("GetDisplayContainer")]
        [ProducesResponseType(typeof(List<EditorDisplayContainerCDto>), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetDisplayContainerAsync(string? displayContainerCodes, bool? returnAttributeSelections = true)
        {
            string[] displayContainerCodesArray = string.IsNullOrEmpty(displayContainerCodes) ? Array.Empty<string>() : displayContainerCodes.Split(',');

            var result = await _editor.GetDisplayContainer(displayContainerCodesArray, returnAttributeSelections);
            return Ok(result);
        }

        /// <summary>
        /// Get details for a display containers that start with requested string.
        /// Response including all attributes
        /// Each attribute includes details of the input field type and any selections
        /// </summary>
        /// <param name="displayContainerCodesStartWith">String the display container codes must start with</param>
        /// <param name="returnAttributeSelections">Default true. Return the available selections/dropdown values for each attribute</param>
        /// <response code="200">Display container returned</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("GetDisplayContainersStartingWith")]
        [ProducesResponseType(typeof(List<EditorDisplayContainerCDto>), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetDisplayContainersStartingWithAsync(string displayContainerCodesStartWith, bool? returnAttributeSelections = true)
        {
            var result = await _editor.GetDisplayContainersStartingWith(displayContainerCodesStartWith, returnAttributeSelections);
            return Ok(result);
        }
    }
}
