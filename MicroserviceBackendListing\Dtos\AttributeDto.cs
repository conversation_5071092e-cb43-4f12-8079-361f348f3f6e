﻿using Sql;
using MicroserviceBackendListing.BusinessLogic;

namespace MicroserviceBackendListing.Dtos
{
    [Mappable(nameof(AttributeCode))]
    public class AttributeDto
    {
        public string? AttributeCode { get; set; }
        public string? AttributeGroupCode { get; set; }

        public string? Label { get; set; }

        public string? InputTypeCode { get; set; }

        public string? Description { get; set; }
        
        public int SortOrder { get; set; }

        public bool? IsEnabled { get; set; }
        public string? AttributeValueTypeCode { get; set; }
        public short? NumericDecimalPlaces { get; set; }

        public bool IsManyAllowed { get; set; }
        public string? Icon { get; set; }
        public string? ReadAccessClaim { get; set; }
        public string? WriteAccessClaim { get; set; }

        public bool? IsRequired { set; get; }
        public decimal? NumericMinValue { set; get; }
        public decimal? NumericMaxValue { set; get; }
        public int? TextMinCharacters { set; get; }
        public int? TextMaxCharacters { set; get; }
        public int? MinDateDaysFromToday { set; get; }
        public int? MaxDateDaysFromToday { get; set; }
        public int? MinSelectionsRequired { set; get; }
        public int? MaxSelectionsAllowed { set; get; }
        public int? MaxStars { set; get; }
        public string? HelpText { set; get; }

    }
    /// <summary>
    /// 
    /// </summary>
    public class GetAttributeDto : AttributeDto {
        /// <summary>
        /// The Data Type Label
        /// </summary>
        public string? DataTypeLabel { get; set; }
        /// <summary>
        /// Dropdown Values for an Attribute
        /// </summary>
        [IgnoreDbMapping]
        public List<DropDownValueDto>? DropdownValues { get; set; }
    }

    /// <summary>
    /// GetListAttributesDto
    /// </summary>
    public class GetListAttributesDto : GetAttributeDto
    {
        /// <summary>
        /// True when Attribute Group is enabled
        /// </summary>
        public bool AttributeGroupIsEnabled { get; set; }
        /// <summary>
        /// True when Input Type code is enabled
        /// </summary>
        public bool AttributeInputTypeIsEnabled { get; set; }
    }

    /// <summary>
    /// AttributeByGroupListDto
    /// </summary>
    [Mappable(nameof(AttributeGroupCode))]
    public class AttributeByGroupListDto {
        public string? AttributeGroupCode { get; set; }
        public string? GroupLabel { get; set; }
        public string? GroupDescription { get; set; }
        public bool GroupIsEnabled { get; set; }
        public short SortOrder { get; set; }
        [IgnoreDbMapping]
        public List<AttributeDto>? Attributes { get; set; }
    }

}