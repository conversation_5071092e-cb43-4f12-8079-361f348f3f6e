diff a/local-deploy/docker-compose.override.yml b/local-deploy/docker-compose.override.yml	(rejected hunks)
@@ -1,17 +1,17 @@
 version: '3.4'
 
 services:
-  redi-microservice-listing:
+  connectsource-microservice-listing:
     environment:
       - ASPNETCORE_ENVIRONMENT=Development
       - SECRETS_STORE_NAME=secretstore
       - DAPR_HTTP_PORT=3500
       - DAPR_GRPC_PORT=50001
       - DAPR_APP_PORT=80
-      - HOSTNAME=redi-microservice-listing.docker.localhost:8443
+      - HOSTNAME=connectsource-microservice-listing.docker.localhost:8443
       - SENTRY_TRACES_SAMPLE_RATE=1.0
       - SENTRY_DSN=
-      - SENTRY_LOG_LEVEL_DEFAULT=debug
+      - SENTRY_LOG_LEVEL_DEFAULT=Error
       - SENTRY_ENABLE_TRACING=true
       - SENTRY_ENABLE_DEBUG=true
 
