using Dapr.Client;
using Autofac;
using Redi.Prime3.MicroService.Logger;
using MicroserviceBackendListing.BusinessLogic;
using MicroserviceBackendListing.Constants;
using MicroserviceBackendListing.BusinessLogic.Azure;
using MicroserviceBackendListing.Interfaces;
using Redi.Prime3.MicroService.BaseLib;

namespace MicroserviceBackendListing.Startup
{
    public partial class Startup
    {
        public IContainer ApplicationContainer { get; private set; }

        public void ConfigureContainer(ContainerBuilder builder)
        {
            builder.RegisterType<AzureFiles>().As<ICloudStorage>().InstancePerDependency();

            AddCustomServices(builder);
        }

        private static void AddCustomServices(ContainerBuilder builder)
        {
            builder.Register(c =>
            {
                var logger = c.Resolve<ILogger>();
                var utils = c.Resolve<UtilityFunctions>();
                return new DaprCRMServiceClient(DaprClient.CreateInvokeHttpClient(MicroserviceConstants.CrmAppId), logger, utils);
            }).As<DaprCRMServiceClient>().SingleInstance();
            builder.Register(c =>
            {
                var logger = c.Resolve<ILogger>();
                var utils = c.Resolve<UtilityFunctions>();
                return new DaprCommonServiceClient(DaprClient.CreateInvokeHttpClient(MicroserviceConstants.CommonAppId), logger, utils);
            }).As<DaprCommonServiceClient>().SingleInstance();
        }
    }
}
