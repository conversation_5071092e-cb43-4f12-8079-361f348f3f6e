﻿using MicroserviceBackendListing.Dtos;
using MicroserviceContract.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;

namespace MicroserviceBackendListing.BusinessLogic
{
    public class DropdownValue : BusinessLogicBase
    {
        public string? Value { set; get; }

        public short SortOrder { set; get; }

        public string? DisplayValue { set; get; }

        public long AttributeDropdownValueId { set; get; }

        public DropdownValue()
        {
            
        }
        public DropdownValue(IUnitOfWork u)
        {
            _unitOfWork = u;
        }
        public DropdownValue(DropDownValueDto dto)
        {
            this.DisplayValue = dto.DisplayValue;
            this.SortOrder = dto.SortOrder;
            this.Value= dto.Value;
            this.AttributeDropdownValueId= dto.AttributeDropdownValueId;
        }

        internal async Task<DropDownValueDto> GetAsync(long AttributeDropdownValueId)
        {
            string sql = @"
                        SELECT [AttributeDropdownValue].[AttributeDropdownValueId], 
                        [AttributeDropdownValue].[AttributeCode], 
                        [AttributeDropdownValue].[Value], 
                        [AttributeDropdownValue].[SortOrder], 
                        [AttributeDropdownValue].[DisplayValue]
                        FROM [listing].[AttributeDropdownValue][AttributeDropdownValue]
                        WHERE [AttributeDropdownValueId] = @AttributeDropdownValueId";
            var query = new DropDownValueDto();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("AttributeDropdownValueId", AttributeDropdownValueId);
                query = await command.SelectSingle<DropDownValueDto>();
            }
            return query;
        }
        internal virtual async Task<List<DropDownValueDto>> GetListDropDownByAttributeCode(String AttributeCode)
        {
            string sql = @"
                        SELECT 
                        [AttributeDropdownValue].[AttributeDropdownValueId],  
                        [AttributeDropdownValue].[Value], 
                        [AttributeDropdownValue].[SortOrder],
                        [AttributeDropdownValue].[DisplayValue] 
                        FROM [listing].[AttributeDropdownValue][AttributeDropdownValue]
                        WHERE [AttributeCode] = @AttributeCode
                        ORDER BY [AttributeDropdownValue].[SortOrder]";
            var result = new List<DropDownValueDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("AttributeCode",AttributeCode);
                result = await command.SelectMany<DropDownValueDto>();
            }
            return result;
        }
        internal async Task CreateAsync(GetDropDownValueCDto dto)
        {
            var exists = await GetAsync(dto.AttributeDropdownValueId);
            if (exists != null)
            {
                await UpdateAsync(dto);
                return;
            }
            string sql2 = @"
                        INSERT INTO [listing].[AttributeDropdownValue]( 
                        [Value], 
                        [AttributeDropdownValueId], 
                        [AttributeCode],               
                        [SortOrder], 
                        [DisplayValue])
                        VALUES(
                        @Value, 
                        @AttributeDropdownValueId,
                        @AttributeCode,                        
                        @SortOrder, 
                        @DisplayValue)";
            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql2))
            {
                command.AddArguments(dto);
                await command.Execute();
            }
        }

        internal async Task UpdateAsync(GetDropDownValueCDto dto)
        {
            var exists = await GetAsync(dto.AttributeDropdownValueId);
            if (exists == null)
            {
                await CreateAsync(dto);
                return;
            }
            string sql2 = @"
                        UPDATE [listing].[AttributeDropdownValue]
                        SET
                        [Value] = @Value, 
                        [AttributeCode] =   @AttributeCode,     
                        [SortOrder] = @SortOrder, 
                        [DisplayValue] = @DisplayValue
                        WHERE [AttributeDropdownValueId] = @AttributeDropdownValueId";
            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql2))
            {
                command.AddArguments(dto);
                await command.Execute();
            }
        }
    }
}
