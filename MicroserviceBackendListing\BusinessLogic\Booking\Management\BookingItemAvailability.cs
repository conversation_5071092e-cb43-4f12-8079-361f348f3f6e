﻿using MicroserviceContract.Dtos.Booking;
using Redi.Prime3.MicroService.BaseLib;
using Microsoft.Extensions.Caching.Memory;
using Sql;
using System.Collections.Specialized;

namespace MicroserviceBackendListing.BusinessLogic.Booking.Management
{
    public partial class BookingItemAvailability : BusinessLogicBase
    {
        private UtilityFunctions _utils;
        private readonly string ENTITY_TYPE = "BookingItemAvailability";
        private Dictionary<string, object>? _queryParameters;
        //private readonly Func<Statistic> _statistic;
        private Notifications _writeNotificationEvent;
        //private Func<DaprCommonServiceClient> _daprCommonServiceClient;
        public BookingItemAvailability(IUnitOfWork u, IMemoryCache memoryCache, UtilityFunctions utils,
            //Func<Statistic> statistic, 
            Notifications writeNotificationEvent
            //Func<DaprCommonServiceClient> daprCommonServiceClient
            )
        {
            _utils = utils;
            _unitOfWork = u;
            //_statistic = statistic;
            _memoryCache = memoryCache;
            _writeNotificationEvent = writeNotificationEvent;
            //_daprCommonServiceClient = daprCommonServiceClient;
        }

        public async Task<BookingItemAvailableResponseCDto> GetBookingItemAvailabilityAsync(int bookingItemAvailabilityId)
        {
            if (bookingItemAvailabilityId <= 0) { return null; }
            string extraCols = "";
            string baseSqlJoins = "";
            string extraSqlJoin = "";
            string sql = "";
            _queryParameters = [];

            (sql, baseSqlJoins, extraSqlJoin, extraCols) = await BuildBaseBookingItemAvailabilityQuery();

            sql += "WHERE [BookingItemAvailability].[BookingItemAvailabilityId] = @bookingItemAvailabilityId ";

            _queryParameters.Add("bookingItemAvailabilityId", bookingItemAvailabilityId);

            // If user belongs to a Tenant then enusre they can only request there Tenant BookingItemId's
            //if (_utils.TenantId != null)
            //{
            //    sql += " AND [BookingItemAvailability].[TenantId] = @TenantId ";
            //    _queryParameters.Add("TenantId", _utils.TenantId);
            //}

            BookingItemAvailableResponseCDto? result = null;
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                if (_queryParameters.Count > 0)
                {
                    foreach (var qp in _queryParameters)
                    {
                        command.AddArgument(qp.Key, qp.Value);
                    }
                }
                using (var reader = await command.SelectRaw())
                {
                    if (reader.Read())
                    {
                        result = GetBookingItemAvailabilityRow(reader);
                    }
                }

            }

            return result;
        }

#pragma warning disable CS1998 // Async method lacks 'await' operators and will run synchronously
        internal async Task<(string, string, string, string)> BuildBaseBookingItemAvailabilityQuery(StringDictionary? canBeSortedBy = null, bool? forUpdate = false)
#pragma warning restore CS1998 // Async method lacks 'await' operators and will run synchronously
        {
            string extraCols = "";
            string extraSqlJoin = "";
            string baseSqlJoins = "";
            string sql = "";
            if (canBeSortedBy == null) { canBeSortedBy = []; }

            sql = @$"
            SELECT   
                    [BookingItemAvailability].[BookingItemAvailabilityId],
                    [BookingItemAvailability].[BookingItemId],
                    [BookingItemAvailability].[FromDate],
                    [BookingItemAvailability].[ToDate],
                    [BookingItemAvailability].[FromTime],
                    [BookingItemAvailability].[ToTime],
                    [BookingItemAvailability].[IncludeMonday],
                    [BookingItemAvailability].[IncludeTuesday],
                    [BookingItemAvailability].[IncludeWednesday],
                    [BookingItemAvailability].[IncludeThursday],
                    [BookingItemAvailability].[IncludeFriday],
                    [BookingItemAvailability].[IncludeSaturday],
                    [BookingItemAvailability].[IncludeSunday],
                    [BookingItemAvailability].[IncludePublicHolidays],
                    [BookingItemAvailability].[CreatedOn],
                    [BookingItemAvailability].[CreatedByName],
                    [BookingItemAvailability].[ModifiedOn],
                    [BookingItemAvailability].[ModifiedByName],
                    [BookingItemAvailability].[Deleted]

                    {extraCols} ";

            baseSqlJoins = $@"
            FROM    [booking].[BookingItemAvailability] {(forUpdate == true ? "WITH (UPDLOCK)" : "")}

";

            sql += baseSqlJoins;
            sql += extraSqlJoin;

            return (sql, baseSqlJoins, extraSqlJoin, extraCols);
        }

        internal BookingItemAvailableResponseCDto GetBookingItemAvailabilityRow(Microsoft.Data.SqlClient.SqlDataReader reader)
        {
            BookingItemAvailableResponseCDto rec = new BookingItemAvailableResponseCDto();
            rec.BookingItemAvailabilityId = !reader.IsDBNull(reader.GetOrdinal("BookingItemAvailabilityId")) ? (reader.GetInt32(reader.GetOrdinal("BookingItemAvailabilityId"))) : null;
            rec.BookingItemId = !reader.IsDBNull(reader.GetOrdinal("BookingItemId")) ? (reader.GetInt32(reader.GetOrdinal("BookingItemId"))) : null;
            rec.FromDate = !reader.IsDBNull(reader.GetOrdinal("FromDate")) ? DateOnly.FromDateTime(reader.GetDateTimeOffset(reader.GetOrdinal("FromDate")).DateTime) : null;
            rec.ToDate = !reader.IsDBNull(reader.GetOrdinal("ToDate")) ? DateOnly.FromDateTime(reader.GetDateTimeOffset(reader.GetOrdinal("ToDate")).DateTime) : null;
            rec.FromTime = !reader.IsDBNull(reader.GetOrdinal("FromTime")) ? TimeOnly.FromTimeSpan(reader.GetTimeSpan(reader.GetOrdinal("FromTime"))) : null;
            rec.ToTime = !reader.IsDBNull(reader.GetOrdinal("ToTime")) ? TimeOnly.FromTimeSpan(reader.GetTimeSpan(reader.GetOrdinal("ToTime"))) : null;
            rec.IncludeMonday = !reader.IsDBNull(reader.GetOrdinal("IncludeMonday")) ? (reader.GetBoolean(reader.GetOrdinal("IncludeMonday"))) : null;
            rec.IncludeTuesday = !reader.IsDBNull(reader.GetOrdinal("IncludeTuesday")) ? (reader.GetBoolean(reader.GetOrdinal("IncludeTuesday"))) : null;
            rec.IncludeWednesday = !reader.IsDBNull(reader.GetOrdinal("IncludeWednesday")) ? (reader.GetBoolean(reader.GetOrdinal("IncludeWednesday"))) : null;
            rec.IncludeThursday = !reader.IsDBNull(reader.GetOrdinal("IncludeThursday")) ? (reader.GetBoolean(reader.GetOrdinal("IncludeThursday"))) : null;
            rec.IncludeFriday = !reader.IsDBNull(reader.GetOrdinal("IncludeFriday")) ? (reader.GetBoolean(reader.GetOrdinal("IncludeFriday"))) : null;
            rec.IncludeSaturday = !reader.IsDBNull(reader.GetOrdinal("IncludeSaturday")) ? (reader.GetBoolean(reader.GetOrdinal("IncludeSaturday"))) : null;
            rec.IncludeSunday = !reader.IsDBNull(reader.GetOrdinal("IncludeSunday")) ? (reader.GetBoolean(reader.GetOrdinal("IncludeSunday"))) : null;
            rec.IncludePublicHolidays = !reader.IsDBNull(reader.GetOrdinal("IncludePublicHolidays")) ? (reader.GetBoolean(reader.GetOrdinal("IncludePublicHolidays"))) : null;

            //rec.NotesCount = !reader.IsDBNull(reader.GetOrdinal("NotesCount")) ? (reader.GetInt32(reader.GetOrdinal("NotesCount"))) : null;
            rec.ModifiedByName = !reader.IsDBNull(reader.GetOrdinal("ModifiedByName")) ? (reader.GetString(reader.GetOrdinal("ModifiedByName"))) : null;
            rec.ModifiedOn = !reader.IsDBNull(reader.GetOrdinal("ModifiedOn")) ? (reader.GetDateTimeOffset(reader.GetOrdinal("ModifiedOn"))) : null;
            rec.CreatedByName = !reader.IsDBNull(reader.GetOrdinal("CreatedByName")) ? (reader.GetString(reader.GetOrdinal("CreatedByName"))) : null;
            rec.CreatedOn = !reader.IsDBNull(reader.GetOrdinal("CreatedOn")) ? (reader.GetDateTimeOffset(reader.GetOrdinal("CreatedOn"))) : DateTime.UtcNow;
            rec.Deleted = !reader.IsDBNull(reader.GetOrdinal("Deleted")) ? (reader.GetBoolean(reader.GetOrdinal("Deleted"))) : false;

            return rec;

        }

        internal async Task<ListResponseDto<BookingItemAvailableResponseCDto>> ListBookingItemAvailabilityAsync(
            StandardListParameters standardListParameters,
            List<long>? bookingItemIds = null,
            DateOnly? fromDateMin = null,
            DateOnly? fromDateMax = null,
            DateOnly? toDateMin = null,
            DateOnly? toDateMax = null,
            TimeOnly? fromTimeMin = null,
            TimeOnly? fromTimeMax = null,
            TimeOnly? toTimeMin = null,
            TimeOnly? toTimeMax = null,
            bool? includeMonday = null,
            bool? includeTuesday = null,
            bool? includeWednesday = null,
            bool? includeThursday = null,
            bool? includeFriday = null,
            bool? includeSaturday = null,
            bool? includeSunday = null,
            bool? includePublicHolidays = null,
            DateTimeOffset? createdOnDateFrom = null,
            DateTimeOffset? createdOnDateTo = null,
            DateTimeOffset? modifiedOnDateFrom = null,
            DateTimeOffset? modifiedOnDateTo = null,
            string? createdByName = null,
            string? modifiedByName = null)
        {
            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "BookingItemAvailabilityId", "[BookingItemAvailability].[BookingItemAvailabilityId]" },
                { "BookingItemId", "[BookingItemAvailability].[BookingItemId]" },
                //{ "ParentEntityName", "[ParentEntityName]" },
                { "FromDate","[BookingItemAvailability].[FromDate]" },
                { "ToDate", "[BookingItemAvailability].[ToDate]" },
                { "FromTime", "[BookingItemAvailability].[FromTime]" },
                { "ToTime", "[BookingItemAvailability].[ToTime]" },
                { "IncludeMonday", "[BookingItemAvailability].[IncludeMonday]" },
                { "IncludeTuesday", "[BookingItemAvailability].[IncludeTuesday]" },
                { "IncludeWednesday", "[BookingItemAvailability].[IncludeWednesday]" },
                { "IncludeThursday", "[BookingItemAvailability].[IncludeThursday]" },
                { "IncludeFriday", "[BookingItemAvailability].[IncludeFriday]" },
                { "IncludeSaturday", "[BookingItemAvailability].[IncludeSaturday]" },
                { "IncludeSunday", "[BookingItemAvailability].[IncludeSunday]" },
                { "IncludePublicHolidays", "[BookingItemAvailability].[IncludePublicHolidays]" },
                { "CreatedOn","[BookingItemAvailability].[CreatedOn]" },
                { "CreatedByName","[BookingItemAvailability].[CreatedByName]" },
                { "ModifiedOn","[BookingItemAvailability].[ModifiedOn]" },
                { "ModifiedByName","[BookingItemAvailability].[ModifiedByName]" },
            };

            string extraCols = "";
            string extraSqlJoin = "";
            string sql = "";
            string baseSqlJoins = "";
            _queryParameters = [];

            (sql, baseSqlJoins, extraSqlJoin, extraCols) = await BuildBaseBookingItemAvailabilityQuery(canBeSortedBy);

            #region WHERE clause
            string whereClause = @"
            WHERE [BookingItemAvailability].[Deleted] = @deleted ";
            _queryParameters.Add("deleted", false);

            if (_utils.TenantId != null)
            {
                whereClause += @" AND [BookingItemAvailability].[TenantId] = @tenantId ";
                _queryParameters.Add("tenantId", _utils.TenantId);
            }

            if (bookingItemIds != null && bookingItemIds.Count() > 0)
            {
                whereClause += @" AND [BookingItemAvailability].[BookingItemId] IN (@bookingItemIds) ";
                _queryParameters.Add("bookingItemIds", bookingItemIds);
            }

            if(fromDateMin.HasValue)
            {
                whereClause += @" AND [BookingItemAvailability].[FromDate] >= @fromDateMin ";
                _queryParameters.Add("fromDateMin", fromDateMin);
            }
            if (fromDateMax.HasValue)
            {
                whereClause += @" AND [BookingItemAvailability].[FromDate] <= @fromDateMax ";
                _queryParameters.Add("fromDateMax", fromDateMax);
            }

            if (toDateMin.HasValue)
            {
                whereClause += @" AND [BookingItemAvailability].[ToDate] >= @toDateMin ";
                _queryParameters.Add("toDateMin", toDateMin);
            }
            if (toDateMax.HasValue)
            {
                whereClause += @" AND [BookingItemAvailability].[ToDate] <= @toDateMax ";
                _queryParameters.Add("toDateMax", toDateMax);
            }

            if (fromTimeMin.HasValue)
            {
                whereClause += @" AND [BookingItemAvailability].[FromTime] >= @fromTimeMin ";
                _queryParameters.Add("fromTimeMin", fromTimeMin);
            }
            if (fromTimeMax.HasValue)
            {
                whereClause += @" AND [BookingItemAvailability].[FromTime] <= @fromTimeMax ";
                _queryParameters.Add("fromTimeMax", fromTimeMax);
            }

            if (toTimeMin.HasValue)
            {
                whereClause += @" AND [BookingItemAvailability].[ToTime] >= @toTimeMin ";
                _queryParameters.Add("toTimeMin", toTimeMin);
            }
            if (toTimeMax.HasValue)
            {
                whereClause += @" AND [BookingItemAvailability].[ToTime] <= @toTimeMax ";
                _queryParameters.Add("toTimeMax", toTimeMax);
            }

            if(includeMonday.HasValue)
            {
                whereClause += @" AND [BookingItemAvailability].[IncludeMonday] = @includeMonday ";
                _queryParameters.Add("includeMonday", includeMonday);
            }
            if (includeTuesday.HasValue)
            {
                whereClause += @" AND [BookingItemAvailability].[IncludeTuesday] = @includeTuesday ";
                _queryParameters.Add("includeTuesday", includeTuesday);
            }
            if (includeWednesday.HasValue)
            {
                whereClause += @" AND [BookingItemAvailability].[IncludeWednesday] = @includeWednesday ";
                _queryParameters.Add("includeWednesday", includeWednesday);
            }
            if (includeThursday.HasValue)
            {
                whereClause += @" AND [BookingItemAvailability].[IncludeThursday] = @includeThursday ";
                _queryParameters.Add("includeThursday", includeThursday);
            }
            if (includeFriday.HasValue)
            {
                whereClause += @" AND [BookingItemAvailability].[IncludeFriday] = @includeFriday ";
                _queryParameters.Add("includeFriday", includeFriday);
            }
            if (includeSaturday.HasValue)
            {
                whereClause += @" AND [BookingItemAvailability].[IncludeSaturday] = @includeSaturday ";
                _queryParameters.Add("includeSaturday", includeSaturday);
            }
            if (includeSunday.HasValue)
            {
                whereClause += @" AND [BookingItemAvailability].[IncludeSunday] = @includeSunday ";
                _queryParameters.Add("includeSunday", includeSunday);
            }
            if (includePublicHolidays.HasValue)
            {
                whereClause += @" AND [BookingItemAvailability].[IncludePublicHolidays] = @includePublicHolidays ";
                _queryParameters.Add("includePublicHolidays", includePublicHolidays);
            }

            if (createdOnDateFrom.HasValue)
            {
                whereClause += @" AND [BookingItemAvailability].[CreatedOn] >= @createdOnDateFrom ";
                _queryParameters.Add("createdOnDateFrom", createdOnDateFrom);
            }

            if (createdOnDateTo.HasValue)
            {
                whereClause += @" AND [BookingItemAvailability].[CreatedOn] <= @createdOnDateTo ";
                _queryParameters.Add("createdOnDateTo", createdOnDateTo);
            }

            if (modifiedOnDateFrom.HasValue)
            {
                whereClause += @" AND [BookingItemAvailability].[ModifiedOn] >= @modifiedOnDateFrom ";
                _queryParameters.Add("modifiedOnDateFrom", modifiedOnDateFrom);
            }

            if (modifiedOnDateTo.HasValue)
            {
                whereClause += @" AND [BookingItemAvailability].[ModifiedOn] <= @modifiedOnDateTo ";
                _queryParameters.Add("modifiedOnDateTo", modifiedOnDateTo);
            }

            if (!createdByName.IsNullOrEmpty() && createdByName != null)
            {
                whereClause += @" AND [BookingItemAvailability].[CreatedByName] = @createdByName ";
                _queryParameters.Add("createdByName", createdByName);
            }

            if (!modifiedByName.IsNullOrEmpty() && modifiedByName != null)
            {
                whereClause += @" AND [BookingItemAvailability].[ModifiedByName] = @modifiedByName ";
                _queryParameters.Add("modifiedByName", modifiedByName);
            }
            #endregion

            sql += whereClause;

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "BookingItemAvailabilityId");

            sql += $"OFFSET @offset ROWS";
            sql += $" FETCH NEXT @limit ROWS ONLY";

            _queryParameters.Add("offset", standardListParameters.Offset ?? 0);
            _queryParameters.Add("limit", standardListParameters.Limit ?? 100);

            List<BookingItemAvailableResponseCDto> result = new List<BookingItemAvailableResponseCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {

                if (_queryParameters.Count > 0)
                {
                    foreach (var qp in _queryParameters)
                    {
                        command.AddArgument(qp.Key, qp.Value);
                    }
                }

                using (var reader = await command.SelectRaw())
                {
                    while (reader.Read())
                    {
                        var rec = GetBookingItemAvailabilityRow(reader);
                        result.Add(rec);
                    }
                }
            }

            sql = @$"
            SELECT COUNT(*) AS totalNumOfRows
            
            {baseSqlJoins}
            {extraSqlJoin}
            ";

            sql += whereClause;
            var response = new ListResponseDto<BookingItemAvailableResponseCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                if (_queryParameters.Count > 0)
                {
                    foreach (var qp in _queryParameters)
                    {
                        command.AddArgument(qp.Key, qp.Value);
                    }
                }

                response.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }
            response.List = result;
            return response;
        }
    }
}
