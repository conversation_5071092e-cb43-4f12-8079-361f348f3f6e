﻿using MicroserviceContract.Dtos;
using Microsoft.AspNetCore.Mvc;
using Redi.Prime3.MicroService.BaseLib;
using System.Net.Http.Headers;

namespace MicroserviceBackendListing.BusinessLogic
{
    /// <summary>
    /// Delete and replace with Microservice intended for service-to-service invocation
    /// </summary>
    public class DaprCommonServiceClient : HttpClientBase
    {
        private readonly UtilityFunctions _utils;
        private readonly ILogger _logger;
        public DaprCommonServiceClient(HttpClient httpClient, ILogger logger, UtilityFunctions utils)
        {
            _httpClient = httpClient;
            _logger = logger;
            _utils = utils;
        }

        public async Task<string> GetAuthenticatedUrl(string submissionId, string url)
        {
            try
            {
                string fileName = submissionId + "_" + url.Substring(url.LastIndexOf('/') + 1);

                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _utils.AccessToken);
                var filedto = await _httpClient.GetAsync($"api/File/GetByFileName?fileName={fileName}");
                if (filedto != null && filedto.IsSuccessStatusCode && filedto.Content.Headers.ContentLength > 0)
                {
                    var result = await _httpClient.GetAsync($"api/FileManager/GetAuthenticatedUrl?baseUrl={(await filedto.Content.ReadFromJsonAsync<FileCDto>())?.PathOrUrl}");
                    if (result != null && result.IsSuccessStatusCode && result.Content.Headers.ContentLength > 0)
                    {
                        return await result.Content.ReadFromJsonAsync<string>();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message, ex);
            }
            return null;
        }

        public async Task<string> UploadFile(IFormFile file, [FromQuery] string cloudFilename, [FromQuery] string fileCategoryCode, [FromQuery] Guid parentEntityId, [FromQuery] string parentEntityType, [FromQuery] bool isPublic, [FromQuery] bool createThumbnail = true, [FromQuery] int? sortOrder = null)
        {
            try
            {
                var result = await _httpClient.PostAsync($"api/FileManager/UploadFile", (HttpContent?)file);
                //var response = result.EnsureSuccessStatusCode();
                //if (response != null && response.IsSuccessStatusCode) {
                //    return await response.Content.ReadAsStringAsync();
                //}
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message, ex);
            }
            return null;
        }

        public async Task<string> GetNextNumber(string nextNumberTypeCode, string? entityId = null, string? formatString = null, bool prefixWithCurrentYear = true)
        {
            try
            {
                var response = await _httpClient.GetAsync($"api/NextNumber/GetNextNumber?nextNumberTypeCode={nextNumberTypeCode}&entityId={entityId}&formatString={formatString}&prefixWithCurrentYear={prefixWithCurrentYear}");
                var result = await response.EnsureSuccessStatusCode().Content.ReadAsStringAsync();
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message, ex);
            }
            return "NotAvail";

        }

        public async Task<string> CreateBookingRecordNote(string note, long bookingRecordId, bool isImportant = false)
        {
            try
            {
                BaseNoteCDto noteCDto = new BaseNoteCDto()
                {
                    BodyText = note,
                    Subject = note.Length > 100 ? note.Substring(0, 100) : note,
                    ParentEntityIntId = bookingRecordId,
                    ParentEntityType = "BookingRecord"
                };
                var response = await _httpClient.PostAsJsonAsync("api/Note/Create", noteCDto);
                var result = await response.EnsureSuccessStatusCode().Content.ReadFromJsonAsync<string>();
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message, ex);
            }
            return null;
        }

        public class BaseNoteCDto
        {
            public Guid NoteId { get; set; }
            public string? BodyText { get; set; }
            public string? Subject { get; set; }
            public Guid? ParentEntityId { get; set; }
            public long? ParentEntityIntId { get; set; }
            public string? ParentEntityType { get; set; }
            public bool IsImportant { get; set; }
            public int NoteCategoryId { get; set; }
        }
    }
}
