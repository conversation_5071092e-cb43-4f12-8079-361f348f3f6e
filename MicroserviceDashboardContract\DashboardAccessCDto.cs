﻿using Redi.Prime3.MicroService.BaseLib;
using Sql;

namespace MicroserviceDashboardContract.Dtos
{
    [Mappable(nameof(DashboardAccessId))]
    public class BaseDashboardAccessCDto : DtoBase
    {
        public int DashboardAccessId { get; set; }
        public int DashboardId { get; set; }
        public Guid UserId { get; set; }
        public int DashboardAccessRoleId { get; set; }
        public int TenantId { get; set; }
    }

    public class GetDashboardAccessCDto : BaseDashboardAccessCDto {  }
}
