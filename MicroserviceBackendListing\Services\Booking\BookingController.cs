﻿using MicroserviceBackendListing.BusinessLogic.Base;
using MicroserviceBackendListing.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using MicroserviceContract.Dtos.Booking;
using Microsoft.AspNetCore.Mvc;
using Sql;
using MicroserviceBackendListing.BusinessLogic.Booking;
using MicroserviceContract.Dtos;

using MicroserviceBackendListing.Enums;

namespace MicroserviceBackendListing.Services
{
    [Route("api/Booking")]
    public class BookingController : AppController
    {
        private readonly BookingRecord _bookingRecord;
        public BookingController(BookingRecord bookingRecord, IUnitOfWork unitOfWork)
        {
            _bookingRecord = bookingRecord;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a Single Booking Record by BookingRecordId
        /// </summary>
        /// <param name="bookingRecordId">The id of the booking record to be returned</param>
        /// <returns></returns>
        [HttpGet]
        [Route("Get")]
        [ProducesResponseType(typeof(BookingRecordResponseCDto), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetAsync(int bookingRecordId)
        {
            if (bookingRecordId <= 0) { throw new HttpRequestException("parameter bookingRecordId is required", null, System.Net.HttpStatusCode.BadRequest); }

            var result = await _bookingRecord.GetSingleBookingRecord(bookingRecordId);
            return Ok(result);
        }

        /// <summary>
        /// Return a list of Bookings
        /// </summary>
        /// <param name="standardListParameters"></param>
        /// <param name="query"></param>
        /// <param name="returnMyBookingsOnly">Only bookings related to the calling user will be returned</param>
        /// <param name="bookingRecordReference"></param>
        /// <param name="isRecurringBookingCustomised"></param>
        /// <param name="bookingRecordStatusIds"></param>
        /// <param name="bookingRecurringId"></param>
        /// <param name="bookingItemIds"></param>
        /// <param name="bookingOwnerParentEntityIds"></param>
        /// <param name="bookingOwnerParentEntityIntIds"></param>
        /// <param name="bookingOwnerParentEntityType"></param>
        /// <param name="bookingRecordDateFrom"></param>
        /// <param name="bookingRecordDateTo"></param>
        /// <param name="createdOnDateFrom"></param>
        /// <param name="createdOnDateTo"></param>
        /// <param name="modifiedOnDateFrom"></param>
        /// <param name="modifiedOnDateTo"></param>
        /// <param name="createdByName"></param>
        /// <param name="modifiedByName"></param>
        /// <param name="statisticFields"></param>
        /// <param name="tenantId"></param>
        /// <param name="returnNotesCount"></param>
        /// <param name="pickupFromHoldingLocationIds"></param>
        /// <param name="returnToHoldingLocationIds"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("ListBookings")]
        public async Task<IActionResult> ListAsync(
            [FromQuery] StandardListParameters standardListParameters,
            [FromQuery] string? query = null,
            [FromQuery] bool? returnMyBookingsOnly = null,
            [FromQuery] string? bookingRecordReference = null,
            [FromQuery] bool? isRecurringBookingCustomised = false,
            [FromQuery] string? bookingRecordStatusIds = null,
            [FromQuery] int? bookingRecurringId = null,
            [FromQuery] string? bookingItemIds = null,
            [FromQuery] string? bookingOwnerParentEntityIds = null,
            [FromQuery] string? bookingOwnerParentEntityIntIds = null,
            [FromQuery] string? bookingOwnerParentEntityType = null,
            [FromQuery] string? pickupFromHoldingLocationIds = null,
            [FromQuery] string? returnToHoldingLocationIds = null,
            [FromQuery] DateTimeOffset? bookingRecordDateFrom = null,
            [FromQuery] DateTimeOffset? bookingRecordDateTo = null,
            [FromQuery] DateTimeOffset? createdOnDateFrom = null,
            [FromQuery] DateTimeOffset? createdOnDateTo = null,
            [FromQuery] DateTimeOffset? modifiedOnDateFrom = null,
            [FromQuery] DateTimeOffset? modifiedOnDateTo = null,
            [FromQuery] string? createdByName = null,
            [FromQuery] string? modifiedByName = null,
            [FromQuery] string? statisticFields = null,
            [FromQuery] int? tenantId = null,
            [FromQuery] bool? returnNotesCount = false)
        {
            var logic = _bookingRecord;
            List<Guid>? bookingOwnerParentEntityIdsList = null;
            if (!string.IsNullOrEmpty(bookingOwnerParentEntityIds))
            {
                bookingOwnerParentEntityIdsList = bookingOwnerParentEntityIds.Split(',').Select(x => Guid.Parse(x)).ToList();
            }
            List<long>? bookingOwnerParentEntityIntIdsList = null;
            if (!string.IsNullOrEmpty(bookingOwnerParentEntityIntIds))
            {
                bookingOwnerParentEntityIntIdsList = bookingOwnerParentEntityIntIds.Split(',').Select(x => long.Parse(x)).ToList();
            }
            List<long>? bookingItemIdsList = null;
            if (!string.IsNullOrEmpty(bookingItemIds))
            {
                bookingItemIdsList = bookingItemIds.Split(',').Select(x => long.Parse(x)).ToList();
            }
            List<int>? pickupFromHoldingLocationIdsList = null;
            if (!string.IsNullOrEmpty(pickupFromHoldingLocationIds))
            {
                pickupFromHoldingLocationIdsList = pickupFromHoldingLocationIds.Split(',').Select(x => int.Parse(x)).ToList();
            }
            List<int>? returnToHoldingLocationIdsList = null;
            if (!string.IsNullOrEmpty(returnToHoldingLocationIds))
            {
                returnToHoldingLocationIdsList = returnToHoldingLocationIds.Split(',').Select(x => int.Parse(x)).ToList();
            }

            var result = await logic.ListBookingRecords(standardListParameters: standardListParameters,
                                                  query: query,
                                                  bookingRecurringId: bookingRecurringId, isRecurringBookingCustomised: isRecurringBookingCustomised,
                                                  bookingRecordStatusIds: bookingRecordStatusIds, bookingItemIds: bookingItemIdsList,
                                                  bookingRecordReference: bookingRecordReference, bookingOwnerParentEntityIds: bookingOwnerParentEntityIdsList,
                                                  bookingOwnerParentEntityIntIds: bookingOwnerParentEntityIntIdsList, bookingOwnerParentEntityType: bookingOwnerParentEntityType,
                                                  bookingRecordDateFrom: bookingRecordDateFrom, bookingRecordDateTo: bookingRecordDateTo,
                                                  createdOnDateFrom: createdOnDateFrom, createdOnDateTo: createdOnDateTo,
                                                  modifiedOnDateFrom: modifiedOnDateFrom, modifiedOnDateTo: modifiedOnDateTo,
                                                  createdByName: createdByName, modifiedByName: modifiedByName,
                                                  statisticFields: statisticFields, returnNotesCount: returnNotesCount, tenantId: tenantId, returnMyBookingsOnly: returnMyBookingsOnly,
                                                  pickupFromHoldingLocationIds: pickupFromHoldingLocationIdsList, returnToHoldingLocationIds: returnToHoldingLocationIdsList);

            return Ok(result);
        }



        /// <summary>
        /// Get the next booking date and time for the requesting user.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetNextBookingDateTimeAndCounts")]
        public async Task<IActionResult> GetNextBookingDateTimeAndCountsAsync()
        {
            var logic = _bookingRecord;
            var result = await logic.GetNextBookingDateTimeAndCountsAsync();
            return Ok(result);
        }

        [HttpPost]
        [Route("CreateDraftBooking")]
        public async Task<IActionResult> CreateDraftAsync([FromBody] RequestCDto<BookingRecordRequestCDto> dto)
        {
            if (dto == null || dto.Data == null)
            {
                throw new HttpRequestException("Invalid dto request. Missing Data", null, System.Net.HttpStatusCode.BadRequest);
            }

            var logic = _bookingRecord;
            var newBookingRecordId = await logic.CreateNewBookingRecord(dto, BookingRecordStatusEnum.Draft);
            _unitOfWork.Commit();

            return Ok(newBookingRecordId);
        }

        [HttpPost]
        [Route("CreateBookedBooking")]
        public async Task<IActionResult> CreateBookedAsync([FromBody] RequestCDto<BookingRecordRequestCDto> dto)
        {
            if (dto == null || dto.Data == null)
            {
                throw new HttpRequestException("Invalid dto request. Missing Data", null, System.Net.HttpStatusCode.BadRequest);
            }

            var logic = _bookingRecord;
            var newBookingRecordId = await logic.CreateNewBookingRecord(dto, BookingRecordStatusEnum.Booked);
            _unitOfWork.Commit();

            return Ok(newBookingRecordId);
        }

        [HttpPost]
        [Route("UpdateBooking")]
        public async Task<IActionResult> UpdateBooking(long? bookingRecordId, [FromBody] RequestCDto<BookingRecordRequestCDto> dto)
        {
            if (bookingRecordId == null || bookingRecordId <= 0) { throw new HttpRequestException("parameter bookingRecordId is required", null, System.Net.HttpStatusCode.BadRequest); }

            if (dto == null || dto.Data == null)
            {
                throw new HttpRequestException("Invalid dto request. Missing Data", null, System.Net.HttpStatusCode.BadRequest);
            }

            var logic = _bookingRecord;
            await logic.Update((long)bookingRecordId, dto);
            _unitOfWork.Commit();

            return Ok();
        }

        [HttpPost]
        [Route("ExtendBooking")]
        public async Task<IActionResult> ExtendBooking(long? bookingRecordId, DateTimeOffset? newEndTime = null, int? extendByMinutes = null)
        {
            if (bookingRecordId == null || bookingRecordId <= 0) { throw new HttpRequestException("parameter bookingRecordId is required", null, System.Net.HttpStatusCode.BadRequest); }
            if (newEndTime == null && (extendByMinutes == null || extendByMinutes == 0)) { throw new HttpRequestException("parameter newEndTime OR extendByMinutes is required", null, System.Net.HttpStatusCode.BadRequest); }

            var logic = _bookingRecord;
            await logic.ExtendBooking((long)bookingRecordId, newEndTime, extendByMinutes);
            _unitOfWork.Commit();

            return Ok();
        }

        [HttpPost]
        [Route("UpdateBookingStatus")]
        public async Task<IActionResult> UpdateBookingStatusAsync(long? bookingRecordId, Int16 newBookingRecordStatusId, string? note = null)
        {
            if (bookingRecordId == null || bookingRecordId <= 0) { throw new HttpRequestException("parameter bookingRecordId is required", null, System.Net.HttpStatusCode.BadRequest); }
            if (newBookingRecordStatusId <= 0) { throw new HttpRequestException("parameter newBookingRecordStatusId is required", null, System.Net.HttpStatusCode.BadRequest); }

            var logic = _bookingRecord;
            await logic.UpdateStatusAsync((long)bookingRecordId, newBookingRecordStatusId, note: note);
            _unitOfWork.Commit();

            return Ok();
        }

        [HttpPost]
        [Route("CancelBooking")]
        public async Task<IActionResult> CancelBooking(long? bookingRecordId, string? note = null)
        {
            if (bookingRecordId == null || bookingRecordId <= 0) { throw new HttpRequestException("parameter bookingRecordId is required", null, System.Net.HttpStatusCode.BadRequest); }

            var logic = _bookingRecord;
            await logic.UpdateStatusAsync((long)bookingRecordId, (short)BookingRecordStatusEnum.Cancelled, note: note);
            _unitOfWork.Commit();

            return Ok();
        }

        [HttpPost]
        [Route("BulkUpdateBookingStatus")]
        public async Task<IActionResult> BulkUpdateBookingStatus([FromBody] List<long> bookingRecordIds, Int16 newBookingRecordStatusId, string? note = null)
        {
            var logic = _bookingRecord;
            foreach (var bookingRecordId in bookingRecordIds)
            {
                await logic.UpdateStatusAsync(bookingRecordId, newBookingRecordStatusId, note: note);
            }

            _unitOfWork.Commit();
            return Ok();
        }

        [HttpPost]
        [Route("RemoveItemFromBooking")]
        public async Task<IActionResult> RemoveItemFromBooking(long? bookingRecordId, long bookingItemId)
        {
            if (bookingRecordId == null || bookingRecordId <= 0) { throw new HttpRequestException("parameter bookingRecordId is required", null, System.Net.HttpStatusCode.BadRequest); }
            if (bookingItemId <= 0) { throw new HttpRequestException("parameter bookingItemId is required", null, System.Net.HttpStatusCode.BadRequest); }

            var logic = _bookingRecord;
            await logic.RemoveItemFromBooking((long)bookingRecordId, bookingItemId);
            _unitOfWork.Commit();

            return Ok();
        }

        /// <summary>
        /// Add a BookingItem to a BookingRecord
        /// </summary>
        /// <param name="bookingRecordId"></param>
        /// <param name="bookingItemId"></param>
        /// <returns></returns>
        /// <exception cref="HttpRequestException"></exception>
        [HttpPost]
        [Route("AddItemToBooking")]
        public async Task<IActionResult> AddItemToBooking(long? bookingRecordId, long bookingItemId)
        {
            if (bookingRecordId == null || bookingRecordId <= 0) { throw new HttpRequestException("parameter bookingRecordId is required", null, System.Net.HttpStatusCode.BadRequest); }
            if (bookingItemId <= 0) { throw new HttpRequestException("parameter bookingItemId is required", null, System.Net.HttpStatusCode.BadRequest); }

            var logic = _bookingRecord;
            await logic.AddItemToBooking((long)bookingRecordId, bookingItemId);
            _unitOfWork.Commit();

            return Ok();
        }

        // /// <summary>
        // /// For each day between a requested from and to date, the system will return if there are available booking slots for each day.
        // /// This can be used for simply showing a calendar and blocking days that have no booking slots available.
        // /// </summary>
        // /// <param name="fromDate"></param>
        // /// <param name="toDate"></param>
        // /// <param name="minimumBookingSlotSizeRequiredInMinutes">Default 0 no minimum. Setting a value greater than 0 will result in only returning availability if a slot is at least this long in Minutes</param>
        // /// <returns>
        // /// Returns a dictionary of days with a flag to indicate if there are available items to book on that day.
        // /// True - available booking slots for the day
        // /// False - no available booking slots for the day
        // /// </returns>
        //[HttpGet]
        //[Route("GetDaysWithBookingAvailability")]
        //[ProducesResponseType(typeof(Dictionary<DateOnly, bool>), 200)]
        //[ProducesResponseType(422)]
        //[ProducesResponseType(500)]
        //public async Task<IActionResult> GetDaysWithBookingAvailability(DateTimeOffset fromDate, DateTimeOffset toDate, int? minimumBookingSlotSizeRequiredInMinutes = 0)
        //{
        //    if (minimumBookingSlotSizeRequiredInMinutes < 0) { throw new HttpRequestException("parameter minimumBookingSlotSizeRequiredInMinutes cannot be less than 0", null, System.Net.HttpStatusCode.BadRequest); }
        //    if (fromDate > toDate) { throw new HttpRequestException("parameter fromDate must be before toDate", null, System.Net.HttpStatusCode.BadRequest); }

        //    // ??? Need to Add Listing Filters so we can control what items we are checking ???
        //    var result = await _booking.GetDaysWithBookingAvailability();
        //    return Ok(result);
        //}

        // /// <summary>
        // /// For the requested Booking Item available booking slots within the requested time range are returned.
        // /// If the booking item has defined booking slots (eg. every 30 minutes during day) then each slot will be returned that is available.
        // /// If the booking item does not have defined booking slots then the available to and from times will be returned.
        // /// </summary>
        // /// <param name="bookingItemId"></param>
        // /// <param name="fromDate"></param>
        // /// <param name="toDate"></param>
        // /// <returns></returns>
        //[HttpGet]
        //[Route("GetAvailableBookingSlotsForBookingItem")]
        //[ProducesResponseType(typeof(Dictionary<DateOnly, bool>), 200)]
        //[ProducesResponseType(422)]
        //[ProducesResponseType(500)]
        //public async Task<IActionResult> GetAvailableBookingSlotsForBookingItem(int? bookingItemId, DateTimeOffset fromDate, DateTimeOffset toDate)
        //{
        //    if (bookingItemId <= 0) { throw new HttpRequestException("parameter bookingItemId is required", null, System.Net.HttpStatusCode.BadRequest); }
        //    if (fromDate  > toDate) { throw new HttpRequestException("parameter fromDate must be before toDate", null, System.Net.HttpStatusCode.BadRequest); }

        //    var result = await _booking.GetAvailableBookingSlotsForBookingItem();
        //    return Ok(result);
        //}


    }
}
