﻿using MicroserviceContract.Dtos.Common;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Text.Json.Serialization;

namespace MicroserviceContract.Dtos.Listing
{
    [Mappable(nameof(ListingId))]
    public class ListingGpsLocationCDto
    {
        public int ListingId;
        public GpslocationCDto? Geolocation;

        [JsonIgnore]
        public double? LocationLatitude
        {
            set
            {
                if (value != null)
                {

                    if (Geolocation == null) { Geolocation = new GpslocationCDto(); }
                    Geolocation.Latitude = (decimal)value;
                }
            }
        }

        [JsonIgnore]
        public double? LocationLongitude
        {
            set
            {
                if (value != null)
                {
                    if (Geolocation == null) { Geolocation = new GpslocationCDto(); }
                    Geolocation.Longitude = (decimal)value;
                }
            }
        }
    }
}
