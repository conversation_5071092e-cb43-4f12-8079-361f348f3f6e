﻿/************************************************************************************************
Dashboard Schema Tables
**************************************************************************************************/

IF NOT EXISTS (SELECT * FROM sys.schemas s WHERE s.name='dashboard')
BEGIN
	EXEC ('CREATE SCHEMA [dashboard]');
END

-- ************************************** [dashboard].[DashboardShareStatus]
IF NOT EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='dashboard' and t.name='DashboardShareStatus')
BEGIN
	CREATE TABLE [dashboard].[DashboardShareStatus]
	(
	 [DashboardShareStatusId] tinyint NOT NULL ,
	 [Label]                 nvarchar(100)  NOT NULL ,
	 CONSTRAINT [PK_DashboardShareStatus] PRIMARY KEY CLUSTERED ([DashboardShareStatusId] ASC)
	);

	EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'0 - invited
	1 - enabled
	2 - expired
	3 - revoked
	4 - pending re-enable', @level0type = N'SCHEMA', @level0name = N'dashboard', @level1type = N'TABLE', @level1name = N'DashboardShareStatus';

	EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'0 - invited
	1 - enabled
	2 - expired
	3 - revoked
	4 - pending re-enable', @level0type = N'SCHEMA', @level0name = N'dashboard', @level1type = N'TABLE', @level1name = N'DashboardShareStatus', @level2type=N'COLUMN', @level2name=N'DashboardShareStatusId';

	INSERT INTO [dashboard].[DashboardShareStatus] ([DashboardShareStatusId], [Label])
	Values
	(0, 'Invited'),
	(1, 'Enabled'),
	(2, 'Expired'),
	(3, 'Revoked'),
	(4, 'Pending Re-enable');

	CREATE TABLE [dashboard].[DashboardWidgetNote]
	(
	 [DashboardWidgetNoteId] int NOT NULL ,
	 [ListingId]             bigint NOT NULL ,
	 [WidgetId]              varchar(60) NULL ,
	 [FromDate]              datetimeoffset(7) NULL ,
	 [ToDate]                datetimeoffset(7) NULL ,
	 [Note]                  nvarchar(max) NULL ,
	 [PositionData]          nvarchar(500) NULL ,
	 [BackgroundColour]      varchar(30) NULL ,
	 [CreatedOn]             datetimeoffset(7) NOT NULL , -- From template: "CreatedAndModified"
	 [CreatedByName]         nvarchar(60) NULL , -- From template: "CreatedAndModified"
	 [ModifiedOn]            datetimeoffset(7) NULL , -- From template: "CreatedAndModified"
	 [ModifiedByName]        nvarchar(60) NULL , -- From template: "CreatedAndModified"
	 [Deleted]               bit NOT NULL  -- From template: "CreatedAndModified"
	 CONSTRAINT [PK_DashboardWidgetNote] PRIMARY KEY NONCLUSTERED ([DashboardWidgetNoteId] ASC),
	 CONSTRAINT [FK_327] FOREIGN KEY ([ListingId])  REFERENCES [listing].[Listing]
	);

	CREATE CLUSTERED INDEX [IX_DashboardWidgetNote_DashboardDateNote] ON [dashboard].[DashboardWidgetNote]
	 (
	  [ListingId] ASC,
	  [Deleted] ASC,
	  [FromDate] ASC,
	  [ToDate] ASC,
	  [WidgetId] ASC
	 )

	EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Notes that can be added to a dashboard widget. They can be for a specific day, or date range, or just general.', @level0type = N'SCHEMA', @level0name = N'dashboard', @level1type = N'TABLE', @level1name = N'DashboardWidgetNote';

	EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Unique Dashboard Id.', @level0type = N'SCHEMA', @level0name = N'dashboard', @level1type = N'TABLE', @level1name = N'DashboardWidgetNote', @level2type=N'COLUMN', @level2name=N'ListingId';

	EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Optional from date for when a note should show.
	if a widget is showing data for a date range that includes this date then the note would show.', @level0type = N'SCHEMA', @level0name = N'dashboard', @level1type = N'TABLE', @level1name = N'DashboardWidgetNote', @level2type=N'COLUMN', @level2name=N'FromDate';

	EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Optional to date for when a note should show.
	if a widget is showing data for a date range that includes this date then the note would show.', @level0type = N'SCHEMA', @level0name = N'dashboard', @level1type = N'TABLE', @level1name = N'DashboardWidgetNote', @level2type=N'COLUMN', @level2name=N'ToDate';

	EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Optional data that can be used to position a note on a graph.', @level0type = N'SCHEMA', @level0name = N'dashboard', @level1type = N'TABLE', @level1name = N'DashboardWidgetNote', @level2type=N'COLUMN', @level2name=N'PositionData';

	EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Optional background colour to use when displaying note.', @level0type = N'SCHEMA', @level0name = N'dashboard', @level1type = N'TABLE', @level1name = N'DashboardWidgetNote', @level2type=N'COLUMN', @level2name=N'BackgroundColour';

	CREATE TABLE [dashboard].[DashboardShare]
	(
	 [DashboardShareId]         int IDENTITY (1, 1) NOT NULL ,
	 [DashboardShareExternalid] uniqueidentifier NOT NULL ,
	 [ListingId]				bigint NOT NULL ,
	 [ShareLinkUrl]             nvarchar(2000) NOT NULL ,
	 [ShareLinkAcceptedAt]      datetimeoffset(7) NULL ,
	 [SharelinkExpiresAt]       datetimeoffset(7) NOT NULL ,
	 [ViewLinkUrl]              nvarchar(2000) NOT NULL ,
	 [ViewLinkExpiresAt]        datetimeoffset(7) NOT NULL ,
	 [SharedWithAddress]        nvarchar(400) NULL ,
	 [DashboardShareStatusId]   tinyint NOT NULL CONSTRAINT [DF_DashboardShare_DashboardShareStatusId] DEFAULT 0 ,
	 [CreatedOn]                datetimeoffset(7) NOT NULL , -- From template: "CreatedAndModNODel"
	 [CreatedByName]            nvarchar(60) NULL , -- From template: "CreatedAndModNODel"
	 [ModifiedOn]               datetimeoffset(7) NULL , -- From template: "CreatedAndModNODel"
	 [ModifiedByName]           nvarchar(60) NULL  -- From template: "CreatedAndModNODel"
	 CONSTRAINT [PK_DashboardShare] PRIMARY KEY CLUSTERED ([DashboardShareId] ASC),
	 CONSTRAINT [FK_319] FOREIGN KEY ([ListingId])  REFERENCES [listing].[Listing],
	 CONSTRAINT [FK_320] FOREIGN KEY ([DashboardShareStatusId])  REFERENCES [dashboard].DashboardShareStatus
	);

	CREATE NONCLUSTERED INDEX [IX_DashboardShare_Dashboard] ON [dashboard].[DashboardShare]
	 (
	  [ListingId] ASC,
	  [DashboardShareStatusId] ASC,
	  [SharedWithAddress] ASC
	 )

	CREATE NONCLUSTERED INDEX [IX_DashboardShare_ShareLinkUrl] ON [dashboard].[DashboardShare]
	 (
	  [ShareLinkUrl] ASC,
	  [DashboardShareStatusId] ASC
	 )
	 INCLUDE (
	  [SharelinkExpiresAt]
	 )

	CREATE NONCLUSTERED INDEX [IX_DashboardShare_ViewLink] ON [dashboard].[DashboardShare]
	 (
	  [ViewLinkUrl] ASC,
	  [DashboardShareStatusId] ASC,
	  [ViewLinkExpiresAt] ASC
	 )

	EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Dashboard Share controls sharing of dashboards via links (generally sent via an email). The links generally have a short life.', @level0type = N'SCHEMA', @level0name = N'dashboard', @level1type = N'TABLE', @level1name = N'DashboardShare';

	EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Internal database share id.', @level0type = N'SCHEMA', @level0name = N'dashboard', @level1type = N'TABLE', @level1name = N'DashboardShare', @level2type=N'COLUMN', @level2name=N'DashboardShareId';

	EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Guid generated uniquely for each share.
	Is used in external urls.', @level0type = N'SCHEMA', @level0name = N'dashboard', @level1type = N'TABLE', @level1name = N'DashboardShare', @level2type=N'COLUMN', @level2name=N'DashboardShareExternalid';

	EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Unique Dashboard Id.', @level0type = N'SCHEMA', @level0name = N'dashboard', @level1type = N'TABLE', @level1name = N'DashboardShare', @level2type=N'COLUMN', @level2name=N'ListingId';

	EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'The email or sms address the link was shared with.', @level0type = N'SCHEMA', @level0name = N'dashboard', @level1type = N'TABLE', @level1name = N'DashboardShare', @level2type=N'COLUMN', @level2name=N'SharedWithAddress';

	EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Identifies the status of this dashboard share.
	0 - invited
	1 - enabled
	2 - expired
	3 - revoked
	4 - pending re-enable', @level0type = N'SCHEMA', @level0name = N'dashboard', @level1type = N'TABLE', @level1name = N'DashboardShare', @level2type=N'COLUMN', @level2name=N'DashboardShareStatusId';

	CREATE TABLE [dashboard].[DashboardConfigHistory]
	(
	 [DashboardConfigHistoryId] int IDENTITY (1, 1) NOT NULL ,
	 [ListingId]              bigint NOT NULL ,
	 [ConfigJson]               nvarchar(max) NOT NULL ,
	 [CreatedOn]                datetimeoffset(7) NOT NULL ,
	 [CreatedByName]            nvarchar(60) NULL ,
	 [VersionNumber]            int NOT NULL ,
	 [Note]                     nvarchar(max) NULL ,
	 CONSTRAINT [PK_DashboardConfigHistory] PRIMARY KEY NONCLUSTERED ([DashboardConfigHistoryId] ASC),
	 CONSTRAINT [FK_317] FOREIGN KEY ([ListingId])  REFERENCES [listing].[Listing]
	);

	CREATE CLUSTERED INDEX [IX_DashboardConfigHistory_Dashboard] ON [dashboard].[DashboardConfigHistory]
	 (
	  [ListingId] ASC,
	  [VersionNumber] ASC
	 )

	EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Unique Dashboard Id.', @level0type = N'SCHEMA', @level0name = N'dashboard', @level1type = N'TABLE', @level1name = N'DashboardConfigHistory', @level2type=N'COLUMN', @level2name=N'ListingId';

	EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Identifies the version number of the dashboard config (each time a dashboard config changes this number is incremented.', @level0type = N'SCHEMA', @level0name = N'dashboard', @level1type = N'TABLE', @level1name = N'DashboardConfigHistory', @level2type=N'COLUMN', @level2name=N'VersionNumber';

	EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'An optional note that can be used to explain the changes that have been made.', @level0type = N'SCHEMA', @level0name = N'dashboard', @level1type = N'TABLE', @level1name = N'DashboardConfigHistory', @level2type=N'COLUMN', @level2name=N'Note';


	CREATE TABLE [dashboard].[DashboardPresentation]
	(
		 [DashboardPresentationId]	INT IDENTITY (1, 1) NOT NULL ,
		 [PartyId]					UNIQUEIDENTIFIER NULL,
		 [TenantId]					INT NOT NULL,
		 [Name]						NVARCHAR(100)  NOT NULL,
		 [DashboardCount]			INT NOT NULL,
		 [ConfigJson]				NVARCHAR(MAX) NOT NULL,
		 [SortOrder]				INT NOT NULL CONSTRAINT [DF_DashboardPresentation_SortOrder] DEFAULT 1 ,
		 [CreatedOn]                DATETIMEOFFSET(7) NOT NULL,
		 [CreatedByName]            NVARCHAR(60) NULL,
		 [ModifiedOn]               DATETIMEOFFSET(7) NULL,
		 [ModifiedByName]           NVARCHAR(60) NULL,
		 [Deleted]					BIT NOT NULL
		 CONSTRAINT [PK_DashboardPresentation] PRIMARY KEY CLUSTERED ([DashboardPresentationId] ASC)
	);
	
	CREATE NONCLUSTERED INDEX [IX_DashboardPresentation_ByPresentation] ON [dashboard].[DashboardPresentation]
	(
		 [DashboardPresentationId] ASC,
		 [PartyId] ASC,
		 [TenantId] ASC,
		 [Deleted] ASC
	)

	CREATE NONCLUSTERED INDEX [IX_DashboardPresentation_ByParty] ON [dashboard].[DashboardPresentation]
	(
		 [PartyId] ASC,
		 [TenantId] ASC,
		 [Deleted] ASC, 
		 [SortOrder] ASC,
		 [CreatedOn] ASC
	)
	
	CREATE NONCLUSTERED INDEX [IX_DashboardPresentation_ByName] ON [dashboard].[DashboardPresentation]
	(
		 [PartyId] ASC,
		 [TenantId] ASC,
		 [Name] ASC,
		 [Deleted] ASC, 
		 [SortOrder] ASC,
		 [CreatedOn] ASC
	)

END
GO

