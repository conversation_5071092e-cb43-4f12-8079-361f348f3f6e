﻿using MicroserviceBackendListing.BusinessLogic.Base;
using MicroserviceBackendListing.Dtos;

using MicroserviceContract.Dtos.ListingManagement;
using Microsoft.Extensions.Caching.Memory;
using Redi.Prime3.MicroService.BaseLib;
using Sql;

namespace MicroserviceBackendListing.BusinessLogic
{

    public class ListingMedia : BusinessLogicBase
    {
        private UtilityFunctions _utils;
        public ListingMedia(IUnitOfWork u, UtilityFunctions utils, IMemoryCache memoryCache)
        {
            _utils= utils;
            _unitOfWork = u;
            _memoryCache = memoryCache;
        }

        internal async Task<bool> UpdateAsync(ManageListingMediaCDto dto, long ListingId)
        {
            if (dto.Deleted != false)
            {
                throw new HttpRequestException("Cannot Update a deleted listing media entry", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }
            if (dto.MediaUrl == null)
            {
                throw new HttpRequestException("MediaUrl cannot be empty", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }
            ManageListingMediaCDto exists = await GetListingMediaFromUrl(dto.MediaUrl, ListingId);
            if (exists == null)
            {
                await CreateAsync(dto, ListingId);
                return true;
            }
            bool hasChangedListing = exists.Title != dto.Title
                                  || exists.MediaTypeCode != dto.MediaTypeCode
                                  || exists.MediaCategoryCode != dto.MediaCategoryCode
                                  || exists.SortOrder != dto.SortOrder;

            if (hasChangedListing)
            {
                string sql = @"
                        UPDATE [listing].[ListingMedia]
                        SET
                        [Title] =  @Title, 
                        [MediaTypeCode] =  @MediaTypeCode,
                        [MediaCategoryCode] =  @MediaCategoryCode,
                        [SortOrder] = @SortOrder,              
                        [ModifiedOn] = @ModifiedOn, 
                        [ModifiedByName] =  @ModifiedByName
                        WHERE [ListingMediaId] = @ListingMediaId AND [ListingId] = @ListingId";
                using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
                {
                    dto.ModifiedByName = _utils.UserFullName;
                    dto.ModifiedOn = DateTimeOffset.UtcNow;
                    command.AddArguments(dto);
                    //command.AddArgument("ListingId", ListingId);
                    await command.Execute();
                }
                return true;
            }

            return false;

        }

        internal async Task<ManageListingMediaCDto> GetAsync(long ListingMediaId)
            {
            string sql = @"
                        SELECT
                        [ListingMedia].[ListingMediaId],
                        [ListingMedia].[ListingId],
                        [ListingMedia].[MediaUrl], 
                        [ListingMedia].[Title], 
                        [ListingMedia].[SortOrder],
                        [ListingMedia].[CreatedOn],
                        [ListingMedia].[CreatedByName],                          
                        [ListingMedia].[ModifiedOn], 
                        [ListingMedia].[ModifiedByName],
                        [ListingMedia].[Deleted],
                        [ListingMedia].[MediaTypeCode], 
                        [ListingMedia].[MediaCategoryCode] 
                        FROM [listing].[ListingMedia] [ListingMedia]
                        WHERE [ListingMediaId] = @ListingMediaId";
            var query = new ManageListingMediaCDto();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ListingMediaId", ListingMediaId);
                query = await command.SelectSingle<ManageListingMediaCDto>();
            }
            return query;
        }

        /// <summary>
        /// Check Media Record exists and belongs to requested ListingId
        /// </summary>
        /// <param name="ListingMediaId"></param>
        /// <param name="ListingId"></param>
        /// <returns></returns>
        internal async Task<ManageListingMediaCDto> BelongAsync(long ListingMediaId, long ListingId)
        {
            string sql = @"
                        SELECT [ListingMedia].[ListingId], 
                        [ListingMedia].[ListingMediaId], 
                        [ListingMedia].[MediaUrl], 
                        [ListingMedia].[Title], 
                        [ListingMedia].[SortOrder],
                        [ListingMedia].[CreatedOn],
                        [ListingMedia].[CreatedByName],                          
                        [ListingMedia].[ModifiedOn], 
                        [ListingMedia].[ModifiedByName],
                        [ListingMedia].[Deleted]
                        FROM [listing].[ListingMedia][ListingMedia]
                        WHERE [ListingMediaId] = @ListingMediaId AND [ListingId] = @ListingId";
            var query = new ManageListingMediaCDto();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ListingMediaId", ListingMediaId);
                command.AddArgument("ListingId", ListingId);
                query = await command.SelectSingle<ManageListingMediaCDto>();
            }
            return query;
        }

        /// <summary>
        /// Logically delete Listing Media Record.
        /// </summary>
        /// <param name="listingMediaId"></param>
        /// <param name="listingId"></param>
        /// <returns></returns>
        internal async Task DeleteAsync(long listingMediaId, long listingId)
        {
               string sql = @"
                UPDATE [listing].[ListingMedia]
                SET
	                   [ModifiedOn] = @ModifiedOn,
                       [ModifiedByName] =  @ModifiedByName,
	                   [Deleted] = 1
                WHERE [ListingMediaId] = @ListingMediaId AND [ListingId] = @ListingId";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Delete, sql))
                {
                command.AddArgument("ListingMediaId", listingMediaId);
                command.AddArgument("ListingId", listingId);
                command.AddArgument("ModifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("ModifiedByName", _utils.UserFullName);
                await command.Execute();
                }
            
        }
        internal async Task<long> CreateAsync (ManageListingMediaCDto dto, long ListingId)
        {
            if (dto.Deleted != false)
            {
                throw new HttpRequestException("Entry cannot be created with deleted as true", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (dto.ListingMediaId != null || dto.ListingMediaId > 0)
            {
                var exists = await GetAsync((long)dto.ListingMediaId);
                if (exists != null)
                {
                    await UpdateAsync(dto, ListingId);
                    return (long)dto.ListingMediaId;
                }
            }
            string sql = @"
                        INSERT INTO [listing].[ListingMedia]
                        ([ListingId], 
                        [Title], 
                        [MediaUrl],
                        [SortOrder],
                        [MediaTypeCode],
                        [MediaCategoryCode],
                        [CreatedOn],
                        [CreatedByName],                          
                        [ModifiedOn], 
                        [ModifiedByName],
                        [Deleted])
                        VALUES (
                        @ListingId, 
                        @Title, 
                        @MediaUrl,
                        @SortOrder,
                        @MediaTypeCode,
                        @MediaCategoryCode,
                        @CreatedOn,
                        @CreatedByName,                          
                        NULL, 
                        NULL,
                        @Deleted
                        )";
            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                dto.ListingId = ListingId;
                dto.CreatedByName = _utils.UserFullName;
                dto.CreatedOn = DateTimeOffset.UtcNow;
                command.AddArguments(dto);
                return await command.ExecuteAndReturnIdentity();
            }
        }
        
        internal async Task<ManageListingMediaCDto> GetListingMediaFromUrl(string? url, long? listingId = null)
        {
            if (url == null)
            {
                throw new HttpRequestException("Media url cannot be empty", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }
            string sql = $@"
                        SELECT 
                        [ListingMedia].[ListingMediaId], 
                        [ListingMedia].[ListingId], 
                        [ListingMedia].[MediaUrl], 
                        [ListingMedia].[Title],
                        [ListingMedia].[SortOrder],
                        [ListingMedia].[CreatedOn], 
                        [ListingMedia].[CreatedByName], 
                        [ListingMedia].[ModifiedOn],
                        [ListingMedia].[ModifiedByName],
                        [ListingMedia].[Deleted],
                        [ListingMedia].[MediaTypeCode], 
                        [ListingMedia].[MediaCategoryCode] 
                        FROM [listing].[ListingMedia] [ListingMedia]
                        WHERE [MediaUrl] = @MediaUrl
                        {(listingId != null ? "AND [ListingMedia].[ListingId] = @listingId " : "")}";
            var result = new ManageListingMediaCDto();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("MediaUrl", url);
                command.AddArgument("listingId", listingId);
                result = await command.SelectSingle<ManageListingMediaCDto>();
            }
            return result;
        }

        internal async Task<List<ManageListingMediaCDto>> GetListingListMediaForListingId (long ListingId, bool? showDeletedImage = false)
        {
            string sql = @"
                        SELECT 
                        [ListingMedia].[ListingMediaId], 
                        [ListingMedia].[ListingId], 
                        [ListingMedia].[MediaUrl], 
                        [ListingMedia].[Title],
                        [ListingMedia].[SortOrder],
                        [ListingMedia].[CreatedOn], 
                        [ListingMedia].[CreatedByName], 
                        [ListingMedia].[ModifiedOn],
                        [ListingMedia].[ModifiedByName],
                        [ListingMedia].[Deleted],
                        [ListingMedia].[MediaTypeCode], 
                        [ListingMedia].[MediaCategoryCode] 
                        FROM [listing].[ListingMedia] [ListingMedia]
                        WHERE [ListingId] = @ListingId";
            var result = new List<ManageListingMediaCDto>();
            if (showDeletedImage.HasValue && (bool)!showDeletedImage)
            {
                sql += $" AND [ListingMedia].[Deleted] = 0";
            }
            sql += $" ORDER BY[ListingMedia].[SortOrder]";
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ListingId", ListingId);
                result = await command.SelectMany<ManageListingMediaCDto>();
            }
            return result;
        }

    }
}
