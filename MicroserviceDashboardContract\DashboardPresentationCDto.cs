﻿using Redi.Prime3.MicroService.BaseLib;
using Sql;

namespace MicroserviceDashboardContract.Dtos
{
    [Mappable(nameof(DashboardPresentationId))]
    public class DashboardPresentationCDto : DtoBase
    {
        public int DashboardPresentationId { get; set; }
        public Guid? PartyId { get; set; }
        public string? Name { get; set; }
        public int DashboardCount { get; set; } = 0;
        public string? ConfigJson { get; set; }
    }

    public class DashboardPresentationWithConfigCDto : DashboardPresentationCDto
    {
        [IgnoreDbMapping]
        public DashboardPresentationConfigCDto? Config { get; set; }
    }

    public class DashboardPresentationConfigCDto
    {
        public List<DashboardPresentationListItemConfigDto>? Dashboards { get; set; }
    }

    public class DashboardPresentationListItemConfigDto
    {
        public string? Id { get; set; }
        public int IntervalTimeSeconds { get; set; }
        public long ListingId { get; set; }
        public string? Subject { get; set; }
        public int SortOrder { get; set; }
        public string? ReferenceNo { get; set; }
    }
}
