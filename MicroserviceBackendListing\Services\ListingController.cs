﻿using MicroserviceBackendListing.BusinessLogic.Base;
using MicroserviceBackendListing.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using MicroserviceContract.Dtos.Listing;
using Microsoft.AspNetCore.Mvc;
using Sql;
using MicroserviceContract.Dtos.Common;
using MicroserviceBackendListing.Policies;
using Microsoft.AspNetCore.Authorization;

namespace MicroserviceBackendListing.Services
{
    /// <summary>
    /// Supports Search and Filtering of listings as well as fetching details for a listing.\
    /// See 'Manage' endpoint for creation and maintenance of a listing.
    /// </summary>
    /// <remarks>
    /// The Listing EndPoint is designed to be used on public facing as well and internal front-end systems.\
    /// ReferenceNo is used to uniquely identify Listings rather than the internal database ListingId
    /// </remarks>
    [Route("api/Listing")]
    public class ListingController : AppController
    {
        private readonly Listing _list;
        public ListingController(Listing list, IUnitOfWork unitOfWork)
        {
            _list = list;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a Single Listing
        /// </summary>
        /// <remarks>
        /// Returns a single Listing record for a requested listing Reference No. \
        /// Can optionally include associated media records (images, videos) and attributes (data).
        /// </remarks>
        /// <param name="referenceNo">The reference number that uniquely identifies the Listing to be returned</param>
        /// <param name="includeMedia">Set to false to not return the media array. Default is true</param>
        /// <param name="includeAttributes">Set to true to return Attributes with the response. Default is false</param>
        /// <param name="displayContainerCodes">Optional comma separated list of Display Container Codes to restrict the attributes returned.</param>
        /// <param name="displayGroupCode">Optional Display Group code. If set returns all display containers that are part of the group.</param>
        /// <param name="excludeAttributesWithNoData">Set to false to include all attributes available. Default is true, thus only attributes that have a value for the Listing are returned.</param>
        /// <param name="flattenAttributesAndReturnAsFields">Default false - when true does not return the Attribute objects. Instead returns a simple dictionary of fields (field for each attribute)</param>
        /// <param name="includeTagsInResponse" > Default true. When true includes all Tags that have been associated with a listing in the response.</param>
        /// <response code="200">Listing returned, or empty listing if not found</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("Get")]
        [Authorize(Policy = PolicyType.ViewListingPolicy)]
        [ProducesResponseType(typeof(ListingWithDataAndMediaCDto), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetAsync(string referenceNo, bool includeMedia = true, bool includeAttributes = false, string? displayContainerCodes = "", bool? excludeAttributesWithNoData = true, string? displayGroupCode = ""
                                                  ,bool flattenAttributesAndReturnAsFields = false, bool includeTagsInResponse = true)
        {
            string[] displayContainerCodesArray = string.IsNullOrEmpty(displayContainerCodes) ? Array.Empty<string>() : displayContainerCodes.Split(',');
            var result = await _list.GetSingleListing(referenceNo, includeMedia, includeAttributes, displayContainerCodesArray, excludeAttributesWithNoData, displayGroupCode: displayGroupCode, includeTagsInResponse: includeTagsInResponse);
            if (flattenAttributesAndReturnAsFields == true)
            {
                // Return flat Fields Dictionary instead of nested display containers and attributes
                _list.FlattenAttributesAndReturnAsDictionaryOfFields(result);
            }
            return Ok(result);
        }

        /// <summary>
        /// Get Listing Attributes Data for a single Listing
        /// </summary>
        /// <remarks>
        /// Returns Attributes for a single Listing record identified by a listing Reference No.
        /// </remarks>
        /// <param name="referenceNo">The reference number that uniquely identifies the Listing to be returned</param>
        /// <param name="displayContainerCodes">Optional comma separated list of Display Container Codes to restrict the attributes returned.</param>
        /// <param name="displayGroupCode">Optional Display Group code. If set returns all display containers that are part of the group.</param>
        /// <param name="excludeAttributesWithNoData">Set to false to include all attributes available. Default is true, thus only attributes that have a value for the Listing are returned.</param>
        /// <response code="200">Listing attributes returned, or empty listing if not found</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("GetData")]
        [Authorize(Policy = PolicyType.ViewListingPolicy)]
        [ProducesResponseType(typeof(List<ListingDisplayContainerCDto>), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetDataAsync(string referenceNo, string? displayContainerCodes = "", bool? excludeAttributesWithNoData = true, string? displayGroupCode = "")
        {
            string[] displayContainerCodesArray = string.IsNullOrEmpty(displayContainerCodes) ? Array.Empty<string>() : displayContainerCodes.Split(',');
            Int64 listingId = await _list.GetListingId(referenceNo);
            var result = await _list.GetAttributeDataAsync(listingId, displayContainerCodesArray, excludeAttributesWithNoData, displayGroupCode: displayGroupCode);
            return Ok(result);
        }

        /// <summary>
        /// Get a List of Active Listings
        /// </summary>
        /// <remarks>
        /// This is the primary call for fetching listings based on a set of filters or search parameters. \
        /// Attribute based filters can be specified to include or exclude Listings. \
        /// Attributes (data) can be returned with each Listing record. The displayContainerCodes specified in the call will determine the attributes that are returned for each listing. \
        /// It is recommended to keep the number of attributes returned for each listing to less than ten to ensure decent query speed. 
        /// 
        /// Returns Active listings by default. StatusCode can be set to return Listings that are not currently Active.
        /// </remarks>
        /// <param name="filter">A dictionary of Attribute based filters. Support unlimited number of filters
        /// Specify as
        /// &amp;filter.attrCode|expression=theValue
        /// Each filter added is treated as an And (so the listing must include each attribute in the filter
        /// The expression if not provided defaults to equals.
        /// Available expressions types are: eq, ne, gt, lt, lte, gte, range, inset (in a set of values), startswith, endswith. contains
        /// range requires 2 values on theValue side split with |
        /// inset allows 1 to many values on the theValue side split with |
        /// </param>
        /// <param name="exclude">A dictionary of Attribute based exclusion filters. Support unlimited number of filters
        /// Specify as
        /// &amp;exclude.attrCode=excludeValue
        /// The expression if not provided defaults to equals.
        /// Available expressions types are: eq, ne, gt, lt, lte, gte, range, inset
        /// range requires 2 values on theValue side split with |
        /// inset allows 1 to many values on the theValue side split with |
        /// </param>
        /// <param name="orFilter">This works the same as filter parameter except each attribute matched is based on an OR.
        /// So a listing must match at least 1 of the conditions in the orFilter
        /// </param>
        /// <param name="includeTagIds">comma separated list of tagIds that a listing must have at least 1 of </param>
        /// <param name="excludeTagIds">comma separated list of tagIds that a listing must not have any of</param>
        /// <param name="displayContainerCodes">Optional comma separated list of Display Container Codes to restrict the attributes returned.</param>
        /// <param name="displayGroupCode">Optional code that if set will return all display containers that are part of the group.</param>
        /// <param name="parentEntityIntId">Identifies an entity that owns this listing (dashboard)</param>
        /// <param name="parentEntityId">Identifies an entity that owns this listing (person, or organisation - party)</param>
        /// <param name="parentEntityType">Identifies the type of parent Entity. Normally Party.</param>
        /// <param name="parentListingId">Optional Parent Listing Id. Only listings that are children of this listing will be returned</param>
        /// <param name="visibility">0 = public, 1 = private, 2 members. Default is public. Private returns only pages the caller owns. Members returns all pages marked as members</param>
        /// <param name="listingTypeId">Listing Type. Defaults to 0.</param>
        /// <param name="statusCode">Default to Active. Set to Expired, Pending or Preview to return other Listings not currently Active</param>
        /// <param name="subject">Search subject for text</param>
        /// <param name="description">Search description for text</param>
        /// <param name="beforeDate">Return listings that had a FromDate prior to this datetime.</param>
        /// <param name="afterDate">Return listings that had a ToDate after this datetime</param>
        /// <param name="mediaCategoryCode">Optionally specify the category of media to return. If not specified all are returned.</param>
        /// <param name="includeMedia">Default is true. When true all urls media associated with a listing are returned. MediaCategoryCode can be used to filter </param>
        /// <param name="includeAttributes">Default is false. When true attribute values are returned for the requested displayContainerCode's</param>
        /// <param name="fromLocationLat">Location latitude user wants to perform a radius distance search on</param>
        /// <param name="fromLocationLong">Location longitude user wants to perform a radius distance search on</param>
        /// <param name="searchDistanceKm">return search results within this distance in Kilometers</param>
        /// <param name="standardListParameters"></param>
        /// <param name="includeTenantConnectorOnConnectorListing">Joins TenantConnector table on fetching listings of type Connector</param>
        /// <param name="availableFromDateTime">Booking item is available to be booked from this time</param>
        /// <param name="availableToDateTime">Booking item is available to be booked to this time</param>
        /// <param name="pickupFromHoldingLocationId">Booking item is available to be booked from this location</param>
        /// <param name="flattenAttributesAndReturnAsFields">Default false - when true does not return the Attribute objects. Instead returns a simple dictionary of fields (field for each attribute)</param>
        /// <param name="includeTagsInResponse">Default true. When true includes Tags that have been associated with a listing in the response. Top 5 returned (override this with parameter includeTagsInResponseCount)</param>
        /// <param name="includeTagsInResponseCount">Default 5. When includeTagsInResponse is true this parameter specifies the max number of tags that are to be returned for a listing. Tags are returned in SortOrder.</param>
        /// <response code="200">Listings returned, or empty listing if not found</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("GetList")]
        [ResponseCache(Duration = 90)]
        [ProducesResponseType(typeof(ListResponseDto<ListingWithDataAndMediaCDto>), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetListAsync(
            [FromQuery(Name = "filter")] Dictionary<string, string> filter,
            [FromQuery(Name = "exclude")] Dictionary<string, string> exclude,
            [FromQuery(Name = "orFilter")] Dictionary<string, string> orFilter,
            [FromQuery] string? displayContainerCodes = "",
            [FromQuery] string? displayGroupCode = "",
            [FromQuery] Int64? parentEntityIntId = null,
            [FromQuery] Guid? parentEntityId = null,
            [FromQuery] Int64? parentListingId = null,
            [FromQuery] string? parentEntityType = null,
            [FromQuery] string? includeTagIds = "",
            [FromQuery] string? excludeTagIds = "",
            [FromQuery] string? statusCode = "Active",
            [FromQuery] short? visibility = 0,
            [FromQuery] short? listingTypeId = 0,
            [FromQuery] string? subject = null,
            [FromQuery] string? description = null,
            [FromQuery] DateTimeOffset? beforeDate = null, DateTimeOffset? afterDate = null,
            [FromQuery] DateTimeOffset? availableFromDateTime = null,
            [FromQuery] DateTimeOffset? availableToDateTime = null,
            [FromQuery] int? pickupFromHoldingLocationId = null,
            [FromQuery] StandardListParameters? standardListParameters = null,
            [FromQuery] bool includeMedia = true, string? mediaCategoryCode = null, bool includeAttributes = false,
            [FromQuery] decimal? fromLocationLat = null, decimal? fromLocationLong = null, int? searchDistanceKm = null,
            [FromQuery] bool includeTenantConnectorOnConnectorListing = true,
            [FromQuery] bool includeTagsInResponse = true,
            [FromQuery] short? includeTagsInResponseCount = 5,
            [FromQuery] bool flattenAttributesAndReturnAsFields = false)
        {
            ListingQueryModelDto queryModel = new ListingQueryModelDto()
            {
                Filter = filter,
                OrFilter = orFilter,
                Exclude = exclude,
                DisplayContainerCodesArray = string.IsNullOrEmpty(displayContainerCodes) ? Array.Empty<string>() : displayContainerCodes.Split(','),
                IncludeTagIdsArray = string.IsNullOrEmpty(includeTagIds) ? Array.Empty<int>() : includeTagIds.Split(',').Select(i => Int32.Parse(i)).ToArray(),
                ExcludeTagIdsArray = string.IsNullOrEmpty(excludeTagIds) ? Array.Empty<int>() : excludeTagIds.Split(',').Select(i => Int32.Parse(i)).ToArray(),
                DisplayGroupCode = displayGroupCode,
                ParentEntityIntId = parentEntityIntId,
                ParentEntityId = parentEntityId,
                ParentEntityType = parentEntityType,
                ParentListingId = parentListingId,
                StatusCode = statusCode,
                Visibility = visibility ?? 0,
                ListingTypeId = listingTypeId ?? 0,
                Subject = subject,
                Description = description,
                BeforeDate = beforeDate,
                AfterDate = afterDate,
                FromLocation = (fromLocationLat != null && fromLocationLong != null ? new GpslocationCDto() { Latitude = (decimal)fromLocationLat, Longitude = (decimal)fromLocationLong } : null),
                SearchDistanceKm = searchDistanceKm,
                StandardListParameters = standardListParameters,
                AvailableFromDateTime = availableFromDateTime,
                AvailableToDateTime = availableToDateTime,
                PickupFromHoldingLocationId = pickupFromHoldingLocationId
            };

            await _list.ValidateQueryModel(queryModel, includeMedia, includeAttributes);
            var result = await _list.GetListAsync(queryModel, includeMedia, mediaCategoryCode, includeAttributes, includeTenantConnectorOnConnectorListing, includeTagsInResponse: includeTagsInResponse, includeTagsInResponseCount: includeTagsInResponseCount ?? 5);

            if (flattenAttributesAndReturnAsFields == true)
            {
                // Return flat Fields Dictionary instead of nested display containers and attributes
                result = _list.FlattenAttributesAndReturnAsDictionaryOfFields(result);
            }
            return Ok(result);
        }

        /// <summary>
        /// Get the filters to display to the user.
        /// </summary>
        /// <remarks>
        /// The displayContainerCodes drives what filters will be made available for attributes.\
        /// When called with no filters then all filtes and options are passed back.\
        /// Once a user has selected some filter options then this call is made again to get the reduced set of filters that are available (based on rows matching the filters already set).\
        /// 
        /// Each available filter attribute value is also returned with a count of available records.
        /// </remarks>
        /// <param name="filter">A dictionary of Attribute based filters. Support unlimited number of filters
        /// Specify as
        /// &amp;filter.attrCode|expression=theValue
        /// The expression if not provided defaults to equals.
        /// Available expressions types are: eq, ne, gt, lt, lte, gte, range, inset (in a set of values), startswith, endswith. contains
        /// range requires 2 values on theValue side split with |
        /// inset allows 1 to many values on the theValue side split with |</param>
        /// <param name="exclude">A dictionary of Attribute based exclusion filters. Support unlimited number of filters
        /// Specify as
        /// &amp;exclude.attrCode=excludeValue
        /// The expression if not provided defaults to equals.
        /// Available expressions types are: eq, ne, gt, lt, lte, gte, range, inset
        /// range requires 2 values on theValue side split with |
        /// inset allows 1 to many values on the theValue side split with |</param>
        /// <param name="orFilter">This works the same as filter parameter except each attribute matched is based on an OR.
        /// So a listing must match at least 1 of the conditions in the orFilter
        /// </param>
        /// <param name="includeTagIds">comma separated list of tagIds that a listing must have at least 1 of </param>
        /// <param name="excludeTagIds">comma separated list of tagIds that a listing must not have any of</param>
        /// <param name="displayContainerCodes">Optional comma separated list of Display Container Codes to restrict the attributes returned.</param>
        /// <param name="displayGroupCode">Optional code that if set will return all display containers that are part of the group.</param>
        /// <param name="parentEntityIntId"></param>
        /// <param name="parentEntityId"></param>
        /// <param name="parentEntityType"></param>
        /// <param name="parentListingId">Optional Parent Listing Id. Only listings that are children of this listing will be returned</param>
        /// <param name="statusCode"></param>
        /// <param name="visibility"></param>
        /// <param name="listingTypeId">The type of listing. Defaults to 0</param>
        /// <param name="beforeDate"></param>
        /// <param name="afterDate"></param>
        /// <param name="fromLocationLat"></param>
        /// <param name="fromLocationLong"></param>
        /// <param name="onlyReturnContainersWithFilterableAttributes">Default True. When true any containers with no filterable attributes will be removed from the response. Set to false to have containers returned regardless of attibutes.</param>
        /// <param name="searchDistanceKm"></param>
        /// <param name="returnCountsPerAttribute">Default true. When true returns a count per attribute of the number of listings with the attribute based on applied filters</param>
        /// <param name="returnCountOfAvailableListings">Default true. When true returns a count of the total listings available based on current filters.</param>
        /// <param name="availableFromDateTime">Booking item is available to be booked from this time</param>
        /// <param name="availableToDateTime">Booking item is available to be booked to this time</param>
        /// <response code="200">Available filter attributes returned</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("GetFilters")]
        [ProducesResponseType(typeof(ListingFiltersResponseCDto), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        [ResponseCache(Duration = 90)]
        public async Task<IActionResult> GetFiltersAsync([FromQuery(Name = "filter")] Dictionary<string, string> filter,
                                                          [FromQuery(Name = "exclude")] Dictionary<string, string> exclude,
                                                          [FromQuery(Name = "orFilter")] Dictionary<string, string> orFilter,
                                                          [FromQuery] string? displayContainerCodes = "",
                                                          [FromQuery] string? displayGroupCode = "",
                                                          [FromQuery] Int64? parentEntityIntId = null, 
                                                          [FromQuery] Guid? parentEntityId = null, 
                                                          [FromQuery] string? parentEntityType = null,
                                                          [FromQuery] Int64? parentListingId = null,
                                                          [FromQuery] string? includeTagIds = "",
                                                          [FromQuery] string? excludeTagIds = "",
                                                          [FromQuery] string? statusCode = "Active",
                                                          [FromQuery] short? visibility = 0,
                                                          [FromQuery] short? listingTypeId = 0,
                                                          [FromQuery] bool returnCountsPerAttribute = true,
                                                          [FromQuery] bool returnCountOfAvailableListings = true,
                                                          [FromQuery] bool onlyReturnContainersWithFilterableAttributes = true,
                                                          [FromQuery] DateTimeOffset? beforeDate = null, [FromQuery] DateTimeOffset? afterDate = null,
                                                          [FromQuery] DateTimeOffset? availableFromDateTime = null,
                                                          [FromQuery] DateTimeOffset? availableToDateTime = null,
                                                          [FromQuery] decimal? fromLocationLat = null, [FromQuery] decimal? fromLocationLong = null, [FromQuery] int? searchDistanceKm = null)
        {
            ListingQueryModelDto queryModel = new ListingQueryModelDto()
            {
                Filter = filter,
                OrFilter = orFilter,
                Exclude = exclude,
                DisplayContainerCodesArray = string.IsNullOrEmpty(displayContainerCodes) ? Array.Empty<string>() : displayContainerCodes.Split(','),
                DisplayGroupCode = displayGroupCode,
                IncludeTagIdsArray = string.IsNullOrEmpty(includeTagIds) ? Array.Empty<int>() : includeTagIds.Split(',').Select(i => Int32.Parse(i)).ToArray(),
                ExcludeTagIdsArray = string.IsNullOrEmpty(excludeTagIds) ? Array.Empty<int>() : excludeTagIds.Split(',').Select(i => Int32.Parse(i)).ToArray(),
                ParentEntityIntId = parentEntityIntId,
                ParentEntityId = parentEntityId,
                ParentEntityType = parentEntityType,
                ParentListingId = parentListingId,
                StatusCode = statusCode,
                Visibility = visibility ?? 0,
                ListingTypeId = listingTypeId ?? 0,
                BeforeDate = beforeDate,
                AfterDate = afterDate,
                FromLocation = (fromLocationLat != null && fromLocationLong != null ? new GpslocationCDto() { Latitude = (decimal)fromLocationLat, Longitude = (decimal)fromLocationLong } : null),
                SearchDistanceKm = searchDistanceKm,
                StandardListParameters = new StandardListParameters() { Limit = 9999, IsDeleted = false, Offset = 0 },
                AvailableFromDateTime = availableFromDateTime,
                AvailableToDateTime = availableToDateTime
            };
            var result = await _list.GetFiltersAsync(queryModel, returnCountsPerAttribute, onlyReturnContainersWithFilterableAttributes, returnCountOfAvailableListings);
            return Ok(result);
        }

        /// <summary>
        /// Get the filters to display to the user returned as a simple dictionary of Attribute Codes and Values.
        /// If returnCountsPerAttribute is true then each returned value has a count of the number of rows with that value.
        /// </summary>
        /// <remarks>
        /// The displayContainerCodes drives what filters will be made available for attributes.\
        /// When called with no filters then all filtes and options are passed back.\
        /// Once a user has selected some filter options then this call is made again to get the reduced set of filters that are available (based on rows matching the filters already set).\
        /// 
        /// Each available filter attribute value is also returned with a count of available records.
        /// </remarks>
        /// <param name="filter">A dictionary of Attribute based filters. Support unlimited number of filters
        /// Specify as
        /// &amp;filter.attrCode|expression=theValue
        /// The expression if not provided defaults to equals.
        /// Available expressions types are: eq, ne, gt, lt, lte, gte, range, inset (in a set of values), startswith, endswith. contains
        /// range requires 2 values on theValue side split with |
        /// inset allows 1 to many values on the theValue side split with |</param>
        /// <param name="exclude">A dictionary of Attribute based exclusion filters. Support unlimited number of filters
        /// Specify as
        /// &amp;exclude.attrCode=excludeValue
        /// The expression if not provided defaults to equals.
        /// Available expressions types are: eq, ne, gt, lt, lte, gte, range, inset
        /// range requires 2 values on theValue side split with |
        /// inset allows 1 to many values on the theValue side split with |</param>
        /// <param name="orFilter">This works the same as filter parameter except each attribute matched is based on an OR.
        /// So a listing must match at least 1 of the conditions in the orFilter
        /// </param>
        /// <param name="includeTagIds">comma separated list of tagIds that a listing must have at least 1 of </param>
        /// <param name="excludeTagIds">comma separated list of tagIds that a listing must not have any of</param>
        /// <param name="displayContainerCodes">Optional comma separated list of Display Container Codes to restrict the attributes returned.</param>
        /// <param name="displayGroupCode">Optional code that if set will return all display containers that are part of the group.</param>
        /// <param name="parentEntityId"></param>
        /// <param name="parentEntityType"></param>
        /// <param name="parentListingId">Optional Parent Listing Id. Only listings that are children of this listing will be returned</param>
        /// <param name="statusCode"></param>
        /// <param name="visibility"></param>
        /// <param name="listingTypeId">The type of listing. Defaults to 0</param>
        /// <param name="beforeDate"></param>
        /// <param name="afterDate"></param>
        /// <param name="fromLocationLat"></param>
        /// <param name="fromLocationLong"></param>
        /// <param name="onlyReturnContainersWithFilterableAttributes">Default True. When true any containers with no filterable attributes will be removed from the response. Set to false to have containers returned regardless of attibutes.</param>
        /// <param name="searchDistanceKm"></param>
        /// <param name="returnCountsPerAttribute">Default true. When true returns a count per attribute of the number of listings with the attribute based on applied filters</param>
        /// <param name="returnCountOfAvailableListings">Default true. When true returns a count of the total listings available based on current filters.</param>
        /// <param name="availableFromDateTime">Booking item is available to be booked from this time</param>
        /// <param name="availableToDateTime">Booking item is available to be booked to this time</param>
        /// <response code="200">Available filter attributes returned</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("GetFiltersFlattened")]
        [ProducesResponseType(typeof(ListingFiltersFlattenedResponseCDto), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        [ResponseCache(Duration = 90)]
        public async Task<IActionResult> GetFiltersFlattenedAsync([FromQuery(Name = "filter")] Dictionary<string, string> filter,
                                                          [FromQuery(Name = "exclude")] Dictionary<string, string> exclude,
                                                          [FromQuery(Name = "orFilter")] Dictionary<string, string> orFilter,
                                                          [FromQuery] string? displayContainerCodes = "",
                                                          [FromQuery] string? displayGroupCode = "",
                                                          [FromQuery] Guid? parentEntityId = null, [FromQuery] string? parentEntityType = null,
                                                          [FromQuery] Int64? parentListingId = null,
                                                          [FromQuery] string? includeTagIds = "",
                                                          [FromQuery] string? excludeTagIds = "",
                                                          [FromQuery] string? statusCode = "Active",
                                                          [FromQuery] short? visibility = 0,
                                                          [FromQuery] short? listingTypeId = 0,
                                                          [FromQuery] bool returnCountsPerAttribute = false,
                                                          [FromQuery] bool returnCountOfAvailableListings = true,
                                                          [FromQuery] bool onlyReturnContainersWithFilterableAttributes = true,
                                                          [FromQuery] DateTimeOffset? beforeDate = null, [FromQuery] DateTimeOffset? afterDate = null,
                                                          [FromQuery] DateTimeOffset? availableFromDateTime = null,
                                                          [FromQuery] DateTimeOffset? availableToDateTime = null,
                                                          [FromQuery] decimal? fromLocationLat = null, [FromQuery] decimal? fromLocationLong = null, [FromQuery] int? searchDistanceKm = null)
        {
            ListingQueryModelDto queryModel = new ListingQueryModelDto()
            {
                Filter = filter,
                OrFilter = orFilter,
                Exclude = exclude,
                DisplayContainerCodesArray = string.IsNullOrEmpty(displayContainerCodes) ? Array.Empty<string>() : displayContainerCodes.Split(','),
                DisplayGroupCode = displayGroupCode,
                IncludeTagIdsArray = string.IsNullOrEmpty(includeTagIds) ? Array.Empty<int>() : includeTagIds.Split(',').Select(i => Int32.Parse(i)).ToArray(),
                ExcludeTagIdsArray = string.IsNullOrEmpty(excludeTagIds) ? Array.Empty<int>() : excludeTagIds.Split(',').Select(i => Int32.Parse(i)).ToArray(),
                ParentEntityId = parentEntityId,
                ParentEntityType = parentEntityType,
                ParentListingId = parentListingId,
                StatusCode = statusCode,
                Visibility = visibility ?? 0,
                ListingTypeId = listingTypeId ?? 0,
                BeforeDate = beforeDate,
                AfterDate = afterDate,
                FromLocation = (fromLocationLat != null && fromLocationLong != null ? new GpslocationCDto() { Latitude = (decimal)fromLocationLat, Longitude = (decimal)fromLocationLong } : null),
                SearchDistanceKm = searchDistanceKm,
                StandardListParameters = new StandardListParameters() { Limit = 9999, IsDeleted = false, Offset = 0 },
                AvailableFromDateTime = availableFromDateTime,
                AvailableToDateTime = availableToDateTime
            };
            var nestedResult = await _list.GetFiltersAsync(queryModel, returnCountsPerAttribute, onlyReturnContainersWithFilterableAttributes, returnCountOfAvailableListings);

            var result = _list.FlattenFilters(nestedResult, returnCountsPerAttribute);

            return Ok(result);
        }

        /// <summary>
        /// Returns available sort options for the display container.
        /// </summary>
        /// <param name="displayContainerCodes"></param>
        /// <response code="200">Available sort options returned</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("GetSortOptions")]
        [ResponseCache(Duration = 300)]
        [ProducesResponseType(typeof(List<ListingSortOptionCDto>), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetSortOptionsAsync([FromQuery] string? displayContainerCodes = "")
        {
            var displayContainerCodesArray = string.IsNullOrEmpty(displayContainerCodes) ? Array.Empty<string>() : displayContainerCodes.Split(',');
            var result = await _list.GetSortOptionsForDisplayContainers(displayContainerCodesArray);
            return Ok(result);
        }


        [HttpGet]
        [Route("GetListingGeolocation")]
        public async Task<IActionResult> GetListingGeolocationAsync([FromQuery] int ListingId)
        {
            var result = await _list.GetListingGeolocationAsync(ListingId);
            return Ok(result);
        }

    }
}
