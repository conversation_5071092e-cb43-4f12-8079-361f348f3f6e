﻿using Microsoft.AspNetCore.Mvc;
using MicroserviceBackendListing.BusinessLogic;
using MicroserviceContract.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using MicroserviceBackendListing.BusinessLogic.Base;
using MicroserviceBackendListing.Policies;
using Microsoft.AspNetCore.Authorization;

namespace MicroserviceBackendListing.Services
{
    /// <summary>
    /// Tags can linked to any Listing.
    /// A Listing can have 1 or more tags.
    /// Tags may be put into Tag Sets to control how and where they are displayed - A Tag Set could be treated as a Field
    /// Tags can be returned either by Tag Set, or by TagSetGroup. The TagSetGroup wraps the tag sets allowing a call to request all tag sets and tags that are linked to the group.
    /// </summary>
    [Route("api/Tag")]
    public class TagController : AppController
    {
        private readonly Tag _tag;
        private readonly Listing _listing;

        public TagController(Tag tag, IUnitOfWork unitOfWork, Listing listing)
        {
            _tag = tag;
            _unitOfWork = unitOfWork;
            _listing = listing;
        }

        /// <summary>
        /// Get a single tag by tagId
        /// </summary>
        /// <param name="tagId"></param>
        /// <response code="200">Tag returned, or empty tag if not found</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("Get")]
        [ProducesResponseType(typeof(TagCDto), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetAsync(int tagId)
        {
            var result = await _tag.GetAsync(tagId);

            return Ok(result);
        }

        /// <summary>
        /// Gets a list of available tags with optional parameter for tagSetId or tagSetGroupId
        /// The returned set of tags can also optionally excude tags that are already linked to a ListingId (add parameter excludeTagsInListingId)
        /// </summary>
        /// <param name="standardListParameters"></param>
        /// <param name="tagSetIds">Optional - A comma separated list of TagSetId's for which tags should be returned</param>
        /// <param name="isEnabled"></param>
        /// <param name="tagSetGroupId"></param>
        /// <param name="excludeTagsInListingId">Optional - when set to a ListingId this will exclude returning tags that already exist for the listing</param>
        /// <response code="200">Tag list response returned, or empty tag list response if not found</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("List")]
        [ProducesResponseType(typeof(ListResponseDto<TagCDto>), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> ListAsync([FromQuery] StandardListParameters standardListParameters, string? tagSetIds = null, bool isEnabled = true, int? tagSetGroupId = null, long? excludeTagsInListingId = null)
        {
            List<int> tagSetIdsArray = string.IsNullOrEmpty(tagSetIds) ? new List<int>() : tagSetIds.Split(',').Select(i => int.Parse(i)).ToList();
            var result = await _tag.GetListAsync(standardListParameters, tagSetIdsArray, isEnabled, tagSetGroupId, excludeTagsInListingId);

            return Ok(result);
        }

        /// <summary>
        /// Get all Tags for a Listing
        /// </summary>
        /// <param name="standardListParameters"></param>
        /// <param name="listingId">Identifies the Listing. Optional. ListingReferenceNo can be specified instead</param>
        /// <param name="listingReferenceNo">Identifies the Listing. Optional. ListiingId can be specified instead</param>
        /// <param name="tagSetIds"></param>
        /// <param name="tagSetGroupId"></param>
        /// <param name="isEnabled"></param>
        /// <response code="200">Tags for listing response returned, or empty list response if not found</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("GetTagsForListing")]
        [ProducesResponseType(typeof(ListResponseDto<ListingTagListCDto>), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetTagsForListingAsync([FromQuery] StandardListParameters standardListParameters, long? listingId = null, string? listingReferenceNo = "", string? tagSetIds = null, int? tagSetGroupId = null, bool isEnabled = true)
        {
            if (listingId == null || listingId == 0)
            {
                listingId = await _listing.GetListingId(listingReferenceNo);
            }
            List<int> tagSetIdsArray = string.IsNullOrEmpty(tagSetIds) ? [] : tagSetIds.Split(',').Select(i => int.Parse(i)).ToList();

            var result = await _tag.GetTagsForListing(standardListParameters, (long)listingId, tagSetIdsArray, tagSetGroupId);

            return Ok(result);
        }

        /// <summary>
        /// Add Tag to Listing
        /// The tag record will be auto created if it does not exist.
        /// </summary>
        /// <param name="listingId">Identifies the Listing. Optional. ListingReferenceNo can be specified instead</param>
        /// <param name="listingReferenceNo">Identifies the Listing. Optional. ListiingId can be specified instead</param>
        /// <param name="tagId">Optional. The TagId to be added to the listing</param>
        /// <param name="tagLabel">Optional. The tag Label to be added to the listing. Can be specified instead of tagId. If no matching TagLabel exists a new Tag will be created (if autoCreateNewTags is true), for the tagSetId specified.</param>
        /// <param name="tagSetId">Optional. The tagSet the tag belongs to.</param>
        /// <param name="autoCreateNewTags">Optional - default false. When true if a tag label is specified that does not exist then a new tag will be created.</param>
        /// <response code="200">ListingTagId returned</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("AddToListing")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        [ProducesResponseType(typeof(int), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> AddToListingAsync(long? listingId = null, string? listingReferenceNo = "", int? tagId = null, string? tagLabel = "", int? tagSetId = null, bool autoCreateNewTags = false)
        {
            if (listingId == null || listingId == 0)
            {
                listingId = await _listing.GetListingId(listingReferenceNo);
            }
            var result = await _tag.AddToListingAsync((long)listingId, tagId, tagLabel, tagSetId, autoCreateNewTags: autoCreateNewTags);
            _unitOfWork.Commit();

            return Ok(result);
        }

        /// <summary>
        /// Bulk add tags to a listing
        /// ListingId or ListingReferenceNo must be included
        /// </summary>
        /// <param name="listingId"></param>
        /// <param name="listingReferenceNo"></param>
        /// <param name="tagIds">Comma seperated list of tags to be added to the listing.</param>
        /// <returns></returns>
        [HttpPost]
        [Route("BulkAddToListing")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        [ProducesResponseType(typeof(int), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> BulkAddToListingAsync(long? listingId = null, string? listingReferenceNo = "", string tagIds = "")
        {
            if (listingId == null || listingId == 0)
            {
                listingId = await _listing.GetListingId(listingReferenceNo);
            }
            // Use distinct to prevent someone adding the same tag twice
            List<int> tagIdsArray = string.IsNullOrEmpty(tagIds) ? [] : tagIds.Split(',').Select(i => int.Parse(i)).Distinct().ToList();

            await _tag.BulkAddToListingAsync((long)listingId, tagIdsArray);
            _unitOfWork.Commit();

            return Ok();
        }

        /// <summary>
        /// Remove a tag from a listing
        /// </summary>
        /// <param name="listingReferenceNo">Optional Listing Reference No. Either this or listingId must be specified</param>
        /// <param name="listingId">Optional listingId. </param>
        /// <param name="listingTagId">The listing tag to be removed</param>
        /// <response code="200">Operation successful</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("RemoveFromListing")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> RemoveFromListingAsync(long? listingId = null, string? listingReferenceNo = "", long? listingTagId = null)
        {
            if (listingId == null || listingId == 0)
            {
                listingId = await _listing.GetListingId(listingReferenceNo);
            }
            await _tag.RemoveFromListingAsync((long)listingId, listingTagId: listingTagId);
            _unitOfWork.Commit();

            return Ok();
        }

        /// <summary>
        /// Remove a tag from a listing
        /// </summary>
        /// <param name="listingReferenceNo">Optional Listing Reference No. Either this or listingId must be specified</param>
        /// <param name="listingId">Optional listingId. </param>
        /// <param name="tagIds">The tag to be removed</param>
        /// <response code="200">Operation successful</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("RemoveFromListingByTagId")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> RemoveByTagIdFromListingAsync(long? listingId = null, string? listingReferenceNo = "", string tagIds = "")
        {
            if (listingId == null || listingId == 0)
            {
                listingId = await _listing.GetListingId(listingReferenceNo);
            }
            List<int> tagIdsArray = string.IsNullOrEmpty(tagIds) ? [] : tagIds.Split(',').Select(i => int.Parse(i)).Distinct().ToList();

            await _tag.RemoveFromListingAsync((long)listingId, tagIds: tagIdsArray);
            _unitOfWork.Commit();

            return Ok();
        }

        /// <summary>
        /// Bulk add and remove tags from a listing. Adds any tags not already on the listing and removes any on the listing that aren't present in the list provided
        /// </summary>
        /// <param name="listingId"></param>
        /// <param name="tagIds"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("BulkUpdateListingTags")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> BulkUpdateListingTagsAsync(long listingId, string tagIds)
        {
            List<int> tagIdsArray = string.IsNullOrEmpty(tagIds) ? [] : tagIds.Split(',').Select(i => int.Parse(i)).Distinct().ToList();

            await _tag.BulkUpdateListingTagsAsync(listingId, tagIdsArray);
            _unitOfWork.Commit();

            return Ok();
        }

        /// <summary>
        /// Create a new Tag
        /// </summary>
        /// <param name="dto"></param>
        /// <response code="200">Tag Id returned</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("CreateTag")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        [ProducesResponseType(typeof(int), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> CreateTagAsync([FromBody] TagCDto dto)
        {
            var result = await _tag.CreateTagAsync(dto.Label, dto.TagSetId, dto.SortOrder, tagColour: dto.Colour, tagIcon: dto.Icon, tagNote: dto.Note, tagImageUrl: dto.ImageUrl);
            _unitOfWork.Commit();

            return Ok(result);
        }

        /// <summary>
        /// Update a Tag. Updateable: label, tagset, enabled, sortOrder
        /// </summary>
        /// <param name="dto"></param>
        /// <response code="200"></response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("UpdateTag")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> UpdateTagAsync([FromBody] TagCDto dto)
        {
            await _tag.UpdateTagAsync(dto);
            _unitOfWork.Commit();

            return Ok();
        }

        /// <summary>
        /// Create a new Tag set
        /// </summary>
        /// <param name="dto"></param>
        /// <response code="200">Tag Set Id returned</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("CreateTagSet")]
        [Authorize(Policy = PolicyType.GlobalAdminPolicy)]
        [ProducesResponseType(typeof(int), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> AddSetAsync([FromBody] TagSetCDto dto)
        {
            var result = await _tag.AddTagSetAsync(dto);
            _unitOfWork.Commit();

            return Ok(result);
        }

        /// <summary>
        /// Delete a set
        /// </summary>
        /// <param name="tagSetId"></param>
        /// <response code="200">Operation successful</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("DeleteTagSet")]
        [Authorize(Policy = PolicyType.GlobalAdminPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> DeleteSetAsync(int tagSetId)
        {
            await _tag.DeleteTagSetAsync(tagSetId);
            _unitOfWork.Commit();

            return Ok();
        }

        /// <summary>
        /// Update the name of a Tag Set
        /// </summary>
        /// <param name="dto"></param>
        /// <response code="200">Operation successful</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("UpdateTagSet")]
        [Authorize(Policy = PolicyType.GlobalAdminPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> UpdateSetAsync([FromBody] TagSetCDto dto)
        {
            await _tag.UpdateTagSetAsync(dto);
            _unitOfWork.Commit();

            return Ok();
        }

        /// <summary>
        /// Get a list of Tag Sets
        /// </summary>
        /// <param name="isEnabled">Optional - default true. Only returns Tag Sets that are enabled</param>
        /// <param name="parentTagSetId">Optional. Will return tag sets that have this tag set as a parent</param>
        /// <param name="tagSetGroupId">Optional. Will return only tag sets that are part of this Tag Set Group</param>
        /// <response code="200">tag set list response returned, or empty list response if not found</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("GetTagSetList")]
        [ProducesResponseType(typeof(List<TagSetCDto>), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> TagSetListAsync(bool? isEnabled = true, int? parentTagSetId = null, int? tagSetGroupId = null)
        {
            var result = await _tag.GetTagSetListAsync(isEnabled, parentTagSetId, tagSetGroupId);

            return Ok(result);
        }


    }
}
