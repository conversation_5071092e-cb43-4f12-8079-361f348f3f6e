﻿using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MicroserviceContract.Dtos.Booking
{
    /// <summary>
    /// A Booking Record Request
    /// Support creation and update of a booking record.
    /// </summary>
    [Mappable(nameof(BookingRecordId))]
    public class BookingRecordRequestCDto
    {
        /// <summary>
        /// The Unique Booking Record Database Id
        /// Auto assigned by <PERSON> on creation of new booking
        /// </summary>
        public int? BookingRecordId { get; set; }
        /// <summary>
        /// Comma separated list of booking items ids
        /// These are the items been booked
        /// From BookingRecordItem Table
        /// </summary>
        public string? BookingItemIds { get; set; }
        /// <summary>
        /// The Recurring Booking Id
        /// Optional Auto linked if a recurring booking is created.
        /// </summary>
        public int? BookingRecurringId { get; set; }
        /// <summary>
        /// Indicates this booking record that was originally part of a recurring booking has been customised
        /// Future changes to the recurring booking will not modify this entry
        /// </summary>
        public bool? IsRecurringBookingCustomised { get; set; }
        /// <summary>
        /// The Datetime when the booking starts
        /// </summary>
        public DateTimeOffset? FromDateTime { get; set; }
        /// <summary>
        /// The Datetime when the booking ends
        /// </summary>
        public DateTimeOffset? ToDateTime { get; set; }
        /// <summary>
        /// Identifies the type of Entity that owns the booking
        /// This could be Party, User, etc
        /// </summary>
        public string? BookingOwnerParentEntityType { get; set; }
        /// <summary>
        /// The parent entity id that owns this booking record
        /// </summary>
        public Guid? BookingOwnerParentEntityId { get; set; }
        public long? BookingOwnerParentEntityIntId { get; set; }
        public int? PickupFromHoldingLocationId { get; set; }
        public int? ReturnToHoldingLocationId { get; set; }
        /// <summary>
        /// Optional note for the booking
        /// </summary>
        public string? Note {  get; set; }
    }

    [Mappable(nameof(BookingRecordId))]
    public class BookingRecordResponseCDto : DtoBase
    {
        /// <summary>
        /// The Unique Booking Record Database Id
        /// Auto assigned by DB on creation of new booking
        /// </summary>
        public long? BookingRecordId { get; set; }
        /// <summary>
        /// The auto assigned Booking Reference
        /// </summary>
        public string? BookingRecordReference { get; set; }
        /// <summary>
        /// The Recurring Booking Id
        /// Optional Auto linked if a recurring booking is created.
        /// </summary>
        public int? BookingRecurringId { get; set; }
        /// <summary>
        /// Indicates this booking record that was originally part of a recurring booking has been customised
        /// Future changes to the recurring booking will not modify this entry
        /// </summary>
        public bool? IsRecurringBookingCustomised { get; set; }
        /// <summary>
        /// The Datetime when the booking starts
        /// </summary>
        public DateTimeOffset? FromDateTime { get; set; }
        /// <summary>
        /// The Datetime when the booking ends
        /// </summary>
        public DateTimeOffset? ToDateTime { get; set; }
        /// <summary>
        /// Identifies the type of Entity that owns the booking
        /// This could be Party, User, etc
        /// </summary>
        public string? BookingOwnerParentEntityType { get; set; }
        /// <summary>
        /// The parent entity id that owns this booking record
        /// </summary>
        public Guid? BookingOwnerParentEntityId { get; set; }
        public long? BookingOwnerParentEntityIntId { get; set; }
        public string? BookingOwnerName { get; set; }
        public int? PickupFromHoldingLocationId { get; set; }
        public string? PickupFromHoldingLocationName { get; set; }
        public int? ReturnToHoldingLocationId { get; set; }
        public string? ReturnToHoldingLocationName { get; set; }
        /// <summary>
        /// Optional note for the booking
        /// </summary>
        public string? Note { get; set; }
        public int? NotesCount { get; set; }
        /// <summary>
        /// Comma separated list of booking items ids
        /// </summary>
        public string? BookingItemIds { get; set; }
        /// <summary>
        /// Comma separated list of booking item names
        /// </summary>
        public string? BookingItemNames { get; set; }
        /// <summary>
        /// The Id of the Tenant that owns the booking
        /// </summary>
        public int? TenantId { get; set; }
        public short? BookingRecordStatusId { get; set; }
        public short? BookingRecordStatusSortOrder { get; set; }
        public string? BookingRecordStatusLabel { get; set; }
        public string? BookingRecordStatusIcon { get; set; }
        public string? BookingRecordStatusColour { get; set; }
        public Dictionary<string, object?>? Statistics { get; set; }
        public Dictionary<string, object?>? Fields { get; set; }
        
        public string? ProfileListingMediaUrl { get; set; }
    }
}
