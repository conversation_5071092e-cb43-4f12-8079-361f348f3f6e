﻿using MicroserviceBackendListing.Dtos;
using MicroserviceContract.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
namespace MicroserviceBackendListing.BusinessLogic
{
    public class DisplayContainer : BusinessLogicBase
    {
        private readonly Func<Attribute> _attribute;
        public DisplayContainer(IUnitOfWork u, Func<Attribute> attribute)
        {
            _unitOfWork = u;
            _attribute = attribute;
        }
        internal async Task<DisplayContainerDto> GetAsync(string DisplayContainerCode)
        {
            string sql = @"
                        SELECT  
                        [DisplayContainer].[Title], 
                        [DisplayContainer].[DisplayContainerCode], 
                        [DisplayContainer].[Description],                          
                        [DisplayContainer].[Enabled],
                        [DisplayContainer].[IsShowTitle],
                        [DisplayContainer].[Icon],
                        [DisplayContainer].[HelpText],
                        FROM [listing].[DisplayContainer] [DisplayContainer] 
                        WHERE [DisplayContainer].[DisplayContainerCode] = @DisplayContainerCode";
            var result = new DisplayContainerDto();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("DisplayContainerCode", DisplayContainerCode);
                result = await command.SelectSingle<DisplayContainerDto>();
            }
            if (result == null)
            {
                throw new HttpRequestException($"DisplayContainerCode '{DisplayContainerCode}' does not exist", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            return result;
        }

      
    }
}
