using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Collections.Specialized;

namespace MicroserviceBackendWorkflow.BusinessLogic
{
    public class Schedule : BusinessLogicBase
    {
        public Schedule(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a single Schedule by ID
        /// </summary>
        /// <param name="scheduleId">The schedule ID</param>
        /// <returns>ScheduleDto or null if not found</returns>
        internal async Task<ScheduleDto?> GetAsync(int scheduleId)
        {
            string sql = @"
                SELECT [ScheduleId]
                      ,[TenantId]
                      ,[Name]
                      ,[WorkflowId]
                      ,[ScheduleTypeId]
                      ,[WorkflowConditionId]
                      ,[ScheduleModeId]
                      ,[SchedulePurposeId]
                      ,[SimpleChainedQueueNames]
                      ,[ParentEntityId]
                      ,[ParentEntityIntId]
                      ,[ParentEntityType]
                      ,[ScheduledNextRunAt]
                      ,[IsEnabled]
                      ,[Note]
                      ,[ScheduleStartsOn]
                      ,[ScheduleEndsOn]
                      ,[TimezoneIanaId]
                      ,[FromTime]
                      ,[ToTime]
                      ,[ScheduledWhenDescription]
                      ,[RecurrenceFrequencyId]
                      ,[RecurOnId]
                      ,[RecurOnPositionId]
                      ,[RecurEveryX]
                      ,[RecurOnDayOfMonth]
                      ,[RecurOnMonth]
                      ,[IncludeMonday]
                      ,[IncludeTuesday]
                      ,[IncludeWednesday]
                      ,[IncludeThursday]
                      ,[IncludeFriday]
                      ,[IncludeSaturday]
                      ,[IncludeSunday]
                      ,[NotificationEventTypeCode]
                      ,[CreatedOn]
                      ,[CreatedByName]
                      ,[ModifiedOn]
                      ,[ModifiedByName]
                      ,[Deleted]
                FROM [workflow].[Schedule]
                WHERE [ScheduleId] = @scheduleId
                  AND [Deleted] = 0";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("scheduleId", scheduleId);
                return await command.SelectSingle<ScheduleDto>();
            }
        }

        /// <summary>
        /// Create a new Schedule
        /// </summary>
        /// <param name="dto">The Schedule data</param>
        internal async Task CreateAsync(ScheduleDto dto)
        {
            if (dto.ScheduleTypeId <= 0)
            {
                throw new HttpRequestException("ScheduleTypeId must be greater than 0", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (dto.ScheduleModeId <= 0)
            {
                throw new HttpRequestException("ScheduleModeId must be greater than 0", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            // Validate that ParentEntityId is not null or empty GUID
            if (dto.ParentEntityId.HasValue && dto.ParentEntityId == Guid.Empty)
            {
                throw new HttpRequestException("ParentEntityId cannot be empty GUID", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            string sql = @"
                INSERT INTO [workflow].[Schedule]
                ([TenantId], [Name], [WorkflowId], [ScheduleTypeId], [WorkflowConditionId], [ScheduleModeId], 
                 [SchedulePurposeId], [SimpleChainedQueueNames], [ParentEntityId], [ParentEntityIntId], [ParentEntityType],
                 [ScheduledNextRunAt], [IsEnabled], [Note], [ScheduleStartsOn], [ScheduleEndsOn], [TimezoneIanaId],
                 [FromTime], [ToTime], [ScheduledWhenDescription], [RecurrenceFrequencyId], [RecurOnId], [RecurOnPositionId],
                 [RecurEveryX], [RecurOnDayOfMonth], [RecurOnMonth], [IncludeMonday], [IncludeTuesday], [IncludeWednesday],
                 [IncludeThursday], [IncludeFriday], [IncludeSaturday], [IncludeSunday], [NotificationEventTypeCode],
                 [CreatedOn], [CreatedByName])
                VALUES
                (@TenantId, @Name, @WorkflowId, @ScheduleTypeId, @WorkflowConditionId, @ScheduleModeId,
                 @SchedulePurposeId, @SimpleChainedQueueNames, @ParentEntityId, @ParentEntityIntId, @ParentEntityType,
                 @ScheduledNextRunAt, @IsEnabled, @Note, @ScheduleStartsOn, @ScheduleEndsOn, @TimezoneIanaId,
                 @FromTime, @ToTime, @ScheduledWhenDescription, @RecurrenceFrequencyId, @RecurOnId, @RecurOnPositionId,
                 @RecurEveryX, @RecurOnDayOfMonth, @RecurOnMonth, @IncludeMonday, @IncludeTuesday, @IncludeWednesday,
                 @IncludeThursday, @IncludeFriday, @IncludeSaturday, @IncludeSunday, @NotificationEventTypeCode,
                 @CreatedOn, @CreatedByName)";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                dto.CreatedOn = DateTimeOffset.UtcNow;
                command.AddArguments(dto);
                await command.Execute();
            }
        }

        /// <summary>
        /// Update an existing Schedule
        /// </summary>
        /// <param name="dto">The Schedule data</param>
        internal async Task UpdateAsync(ScheduleDto dto)
        {
            if (dto.ScheduleId <= 0)
            {
                throw new HttpRequestException("ScheduleId must be greater than 0", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (dto.ScheduleTypeId <= 0)
            {
                throw new HttpRequestException("ScheduleTypeId must be greater than 0", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (dto.ScheduleModeId <= 0)
            {
                throw new HttpRequestException("ScheduleModeId must be greater than 0", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            // Validate that ParentEntityId is not empty GUID
            if (dto.ParentEntityId.HasValue && dto.ParentEntityId == Guid.Empty)
            {
                throw new HttpRequestException("ParentEntityId cannot be empty GUID", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            var exists = await GetAsync(dto.ScheduleId);
            if (exists == null)
            {
                throw new HttpRequestException($"Schedule with ID '{dto.ScheduleId}' not found", null, System.Net.HttpStatusCode.NotFound);
            }

            string sql = @"
                UPDATE [workflow].[Schedule]
                SET [TenantId] = @TenantId,
                    [Name] = @Name,
                    [WorkflowId] = @WorkflowId,
                    [ScheduleTypeId] = @ScheduleTypeId,
                    [WorkflowConditionId] = @WorkflowConditionId,
                    [ScheduleModeId] = @ScheduleModeId,
                    [SchedulePurposeId] = @SchedulePurposeId,
                    [SimpleChainedQueueNames] = @SimpleChainedQueueNames,
                    [ParentEntityId] = @ParentEntityId,
                    [ParentEntityIntId] = @ParentEntityIntId,
                    [ParentEntityType] = @ParentEntityType,
                    [ScheduledNextRunAt] = @ScheduledNextRunAt,
                    [IsEnabled] = @IsEnabled,
                    [Note] = @Note,
                    [ScheduleStartsOn] = @ScheduleStartsOn,
                    [ScheduleEndsOn] = @ScheduleEndsOn,
                    [TimezoneIanaId] = @TimezoneIanaId,
                    [FromTime] = @FromTime,
                    [ToTime] = @ToTime,
                    [ScheduledWhenDescription] = @ScheduledWhenDescription,
                    [RecurrenceFrequencyId] = @RecurrenceFrequencyId,
                    [RecurOnId] = @RecurOnId,
                    [RecurOnPositionId] = @RecurOnPositionId,
                    [RecurEveryX] = @RecurEveryX,
                    [RecurOnDayOfMonth] = @RecurOnDayOfMonth,
                    [RecurOnMonth] = @RecurOnMonth,
                    [IncludeMonday] = @IncludeMonday,
                    [IncludeTuesday] = @IncludeTuesday,
                    [IncludeWednesday] = @IncludeWednesday,
                    [IncludeThursday] = @IncludeThursday,
                    [IncludeFriday] = @IncludeFriday,
                    [IncludeSaturday] = @IncludeSaturday,
                    [IncludeSunday] = @IncludeSunday,
                    [NotificationEventTypeCode] = @NotificationEventTypeCode,
                    [ModifiedOn] = @ModifiedOn,
                    [ModifiedByName] = @ModifiedByName
                WHERE [ScheduleId] = @ScheduleId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                dto.ModifiedOn = DateTimeOffset.UtcNow;
                command.AddArguments(dto);
                await command.Execute();
            }
        }

        /// <summary>
        /// Delete a Schedule (soft delete)
        /// </summary>
        /// <param name="scheduleId">The schedule ID</param>
        internal async Task DeleteAsync(int scheduleId)
        {
            var exists = await GetAsync(scheduleId);
            if (exists == null)
            {
                throw new HttpRequestException($"Schedule with ID '{scheduleId}' not found", null, System.Net.HttpStatusCode.NotFound);
            }

            string sql = @"
                UPDATE [workflow].[Schedule]
                SET [Deleted] = 1,
                    [ModifiedOn] = @ModifiedOn
                WHERE [ScheduleId] = @scheduleId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("scheduleId", scheduleId);
                command.AddArgument("ModifiedOn", DateTimeOffset.UtcNow);
                await command.Execute();
            }
        }

        /// <summary>
        /// Get a list of Schedules with filtering and paging
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="tenantId">Filter by tenant ID</param>
        /// <param name="workflowId">Filter by workflow ID</param>
        /// <param name="isEnabled">Filter by enabled status</param>
        /// <param name="parentEntityId">Filter by parent entity ID</param>
        /// <returns>List of Schedules</returns>
        internal async Task<ListResponseDto<ScheduleListDto>> GetListAsync(StandardListParameters standardListParameters, int? tenantId = null, int? workflowId = null, bool? isEnabled = null, Guid? parentEntityId = null)
        {
            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "ScheduleId", "[Schedule].[ScheduleId]" },
                { "Name", "[Schedule].[Name]" },
                { "TenantId", "[Schedule].[TenantId]" },
                { "WorkflowId", "[Schedule].[WorkflowId]" },
                { "ScheduleTypeId", "[Schedule].[ScheduleTypeId]" },
                { "ScheduleModeId", "[Schedule].[ScheduleModeId]" },
                { "IsEnabled", "[Schedule].[IsEnabled]" },
                { "ScheduledNextRunAt", "[Schedule].[ScheduledNextRunAt]" },
                { "CreatedOn", "[Schedule].[CreatedOn]" },
                { "ModifiedOn", "[Schedule].[ModifiedOn]" }
            };

            string sql = @"
                SELECT [ScheduleId], [TenantId], [Name], [WorkflowId], [ScheduleTypeId], [WorkflowConditionId], [ScheduleModeId],
                       [SchedulePurposeId], [SimpleChainedQueueNames], [ParentEntityId], [ParentEntityIntId], [ParentEntityType],
                       [ScheduledNextRunAt], [IsEnabled], [Note], [ScheduleStartsOn], [ScheduleEndsOn], [TimezoneIanaId],
                       [FromTime], [ToTime], [ScheduledWhenDescription], [RecurrenceFrequencyId], [RecurOnId], [RecurOnPositionId],
                       [RecurEveryX], [RecurOnDayOfMonth], [RecurOnMonth], [IncludeMonday], [IncludeTuesday], [IncludeWednesday],
                       [IncludeThursday], [IncludeFriday], [IncludeSaturday], [IncludeSunday], [NotificationEventTypeCode],
                       [CreatedOn], [CreatedByName], [ModifiedOn], [ModifiedByName], [Deleted]
                FROM [workflow].[Schedule]
                WHERE [Deleted] = 0";

            if (tenantId.HasValue)
            {
                sql += " AND ([TenantId] = @tenantId OR [TenantId] IS NULL)";
            }

            if (workflowId.HasValue)
            {
                sql += " AND [WorkflowId] = @workflowId";
            }

            if (isEnabled.HasValue)
            {
                sql += " AND [IsEnabled] = @isEnabled";
            }

            if (parentEntityId.HasValue && parentEntityId != Guid.Empty)
            {
                sql += " AND [ParentEntityId] = @parentEntityId";
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "ScheduleId");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<ScheduleListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);
                if (tenantId.HasValue)
                {
                    command.AddArgument("tenantId", tenantId.Value);
                }
                if (workflowId.HasValue)
                {
                    command.AddArgument("workflowId", workflowId.Value);
                }
                if (isEnabled.HasValue)
                {
                    command.AddArgument("isEnabled", isEnabled.Value);
                }
                if (parentEntityId.HasValue && parentEntityId != Guid.Empty)
                {
                    command.AddArgument("parentEntityId", parentEntityId.Value);
                }
                result.List = await command.SelectMany<ScheduleListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[Schedule]
                WHERE [Deleted] = 0";

            if (tenantId.HasValue)
            {
                countSql += " AND ([TenantId] = @tenantId OR [TenantId] IS NULL)";
            }

            if (workflowId.HasValue)
            {
                countSql += " AND [WorkflowId] = @workflowId";
            }

            if (isEnabled.HasValue)
            {
                countSql += " AND [IsEnabled] = @isEnabled";
            }

            if (parentEntityId.HasValue && parentEntityId != Guid.Empty)
            {
                countSql += " AND [ParentEntityId] = @parentEntityId";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                if (tenantId.HasValue)
                {
                    command.AddArgument("tenantId", tenantId.Value);
                }
                if (workflowId.HasValue)
                {
                    command.AddArgument("workflowId", workflowId.Value);
                }
                if (isEnabled.HasValue)
                {
                    command.AddArgument("isEnabled", isEnabled.Value);
                }
                if (parentEntityId.HasValue && parentEntityId != Guid.Empty)
                {
                    command.AddArgument("parentEntityId", parentEntityId.Value);
                }
                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }
    }
}
