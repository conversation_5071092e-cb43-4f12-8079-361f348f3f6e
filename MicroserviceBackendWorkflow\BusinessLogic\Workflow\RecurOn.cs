using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Collections.Specialized;

namespace MicroserviceBackendWorkflow.BusinessLogic
{
    public class RecurOn : BusinessLogicBase
    {
        public RecurOn(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a single RecurOn by ID
        /// </summary>
        /// <param name="recurOnId">The recur on ID</param>
        /// <returns>RecurOnDto or null if not found</returns>
        internal async Task<RecurOnDto?> GetAsync(byte recurOnId)
        {
            string sql = @"
                SELECT [RecurOnId]
                      ,[Label]
                      ,[SortOrder]
                      ,[IsEnabled]
                FROM [workflow].[RecurOn]
                WHERE [RecurOnId] = @recurOnId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("recurOnId", recurOnId);
                return await command.SelectSingle<RecurOnDto>();
            }
        }

        /// <summary>
        /// Get a list of RecurOns with filtering and paging
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="showDisabled">Include disabled records</param>
        /// <returns>List of RecurOns</returns>
        internal async Task<ListResponseDto<RecurOnListDto>> GetListAsync(StandardListParameters? standardListParameters = null, bool showDisabled = false)
        {
            if (standardListParameters == null)
            {
                standardListParameters = new StandardListParameters();
            }

            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "RecurOnId", "[RecurOn].[RecurOnId]" },
                { "Label", "[RecurOn].[Label]" },
                { "SortOrder", "[RecurOn].[SortOrder]" },
                { "IsEnabled", "[RecurOn].[IsEnabled]" }
            };

            string sql = @"
                SELECT [RecurOnId]
                      ,[Label]
                      ,[SortOrder]
                      ,[IsEnabled]
                FROM [workflow].[RecurOn]
                WHERE 1=1";

            if (!showDisabled)
            {
                sql += " AND [IsEnabled] = 1";
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "SortOrder");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<RecurOnListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);
                result.List = await command.SelectMany<RecurOnListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[RecurOn]
                WHERE 1=1";

            if (!showDisabled)
            {
                countSql += " AND [IsEnabled] = 1";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }
    }
}
