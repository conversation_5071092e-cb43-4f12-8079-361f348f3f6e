﻿using Redi.Prime3.MicroService.BaseLib;
using Sql;

namespace MicroserviceBackendListing.BusinessLogic
{
    public class StatusCode: BusinessLogicBase
    {
        public StatusCode(IUnitOfWork u)
        {
            _unitOfWork = u;
        }
        public async Task<bool> StatusCodeExist(String StatusCode)
        {
            string sql = @"
                        SELECT  
                        [Status].[StatusCode]                       
                        FROM [common].[Status][Status]
                        WHERE [StatusCode] = @StatusCode";
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("StatusCode", StatusCode);

                var result = await command.SelectSingle<string>("StatusCode");
                if (result == null)
                    return false;
                return true;
            }
        }
    }
}
