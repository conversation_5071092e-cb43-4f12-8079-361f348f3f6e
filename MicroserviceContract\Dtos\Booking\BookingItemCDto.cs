﻿using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MicroserviceContract.Dtos.Booking
{
    [Mappable(nameof(BookingItemId))]
    public class BookingItemRequestCDto
    {
        public int? BookingItemId { get; set; }
        public string? ParentEntityType { get; set; }
        public Guid? ParentEntityId { get; set; }
        public long? ParentEntityIntId { get; set; }
        public string? TimeZoneIanaId { get; set; }
        public Int16? MinimumGapBetweenBookingsMinutes { get; set; }
        public Int16? MinimumBookingTimeInMinutes { get; set; }
        public Int16? MaximumBookingTimeInMinutes { get; set; }
        public Int16? MaximumBookingIntoFutureDays {  get; set; }
        public bool? IsRecurringBookingEnabled { get; set; }
        public bool? IsEnabled {  get; set; }
        public string? Note {  get; set; }
        public int? PublicHolidaySetId { get; set; }
        /// <summary>
        /// The current holding location for the booking item.
        /// </summary>
        public int? CurrentHoldingLocationId { get; set; }
        /// <summary>
        /// This is the normal home location for the booking item.
        /// Note! this may not be the current holding location. See CurrentHoldLocationId
        /// </summary>
        public int? HomeHoldingLocationId { get; set; }
    }

    [Mappable(nameof(BookingItemId))]
    public class BookingItemResponseCDto : DtoBase
    {
        public int? BookingItemId { get; set; }
        public string? ParentEntityType { get; set; }
        public Guid? ParentEntityId { get; set; }
        public long? ParentEntityIntId { get; set; }
        public string? ParentEntityName { get; set; }
        public string? TimeZoneIanaId { get; set; }
        public Int16? MinimumGapBetweenBookingsMinutes { get; set; }
        public Int16? MinimumBookingTimeInMinutes { get; set; }
        public Int16? MaximumBookingTimeInMinutes { get; set; }
        public Int16? MaximumBookingIntoFutureDays { get; set; }
        public bool? IsRecurringBookingEnabled { get; set; }
        public bool? IsEnabled { get; set; }
        public string? Note { get; set; }
        public int? PublicHolidaySetId { get; set; }
        public string? PublicHolidaySetLabel { get; set; }
        public Dictionary<string, object?>? Statistics { get; set; }
        public int? CurrentHoldingLocationId { get; set; }
        public string? CurrentHoldingLocationName { get; set; }
        public int? HomeHoldingLocationId { get; set; }
        public string? HomeHoldingLocationName { get; set; }
    }
}
