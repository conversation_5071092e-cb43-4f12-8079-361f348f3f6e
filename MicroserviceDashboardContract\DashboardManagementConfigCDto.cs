﻿using Sql;

namespace MicroserviceDashboardContract.Dtos
{
    public class BaseDashboardManagementConfigCDto
    {
        public Int64 ListingId { get; set; }
        public string? ConfigJson { get; set; }
        public string? HelpText { get; set; }
        public int CurrentVersionNo { get; set; }
    }

    public class GetDashboardManagementConfigCDto : BaseDashboardManagementConfigCDto {  }


    [Mappable(nameof(DashboardConfigHistoryId))]
    public class DashboardManagementConfigHistoryCDto
    {
        public int DashboardConfigHistoryId { get; set; }
        public Int64 ListingId { get; set; }
        public string? ConfigJson { get; set; }
        public DateTimeOffset CreatedOn { get; set; }
        public string? CreatedByName { get; set; }
        public int VersionNumber { get; set; }
        public string? Note { get; set; }
    }
}
