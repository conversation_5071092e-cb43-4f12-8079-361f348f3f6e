﻿using Redi.Prime3.MicroService.BaseLib;
using Sql;

namespace MicroserviceContract.Dtos
{
    [Mappable(nameof(FileId))]
    public class FileCDto : DtoBase
    {
        public Guid FileId { get; set; }
        public string? DisplayName { get; set; }
        public Guid? FolderId { get; set; }
        public string? FileName { get; set; }
        public string? PathOrUrl { get; set; }
        public int VersionNumber { get; set; }
        public string? FileCategoryCode { get; set; }
        public string? ThumbnailPathOrUrl { get; set; }
        public Guid ParentEntityId { get; set; }
        public string? ParentEntityType { get; set; }
        public int? SortOrder { get; set; }
    }

    public class FileListCDto : FileCDto
    {

    }


}
