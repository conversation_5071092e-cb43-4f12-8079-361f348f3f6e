diff a/azure-deploy/environment/.env.deploy.development b/azure-deploy/environment/.env.deploy.development	(rejected hunks)
@@ -1,19 +1,19 @@
 DOCKERHUB_USERNAME=jadredi
 DOCKERHUB_NAMESPACE=redisoftware
-DOCKER_REPO=redi-microservice-listing
+DOCKER_REPO=cs-microservice-listing
 IMAGETAGVERSION=dev-latest
-AZURE_GLOBAL_SUBSCRIPTION_ID=43530b49-4eaf-4344-8dd5-824eaec5485d
 AZURE_GLOBAL_RESOURCE_GROUP=test_redi
-AZURE_SERVICE_PRINCIPAL_ID=ddbc6916-4198-479d-bed6-c0f3fa23a294
-AZURE_SUBSCRIPTION_ID=43530b49-4eaf-4344-8dd5-824eaec5485d
-AZURE_RESOURCE_GROUP=test-base
+AZURE_RESOURCE_GROUP=test-connectsource
 AZURE_TENANT_ID=ee84645d-1f03-4d0e-8e54-377dbf082ab2
-AZURE_APP_ID=ddbc6916-4198-479d-bed6-c0f3fa23a294
+AZURE_SERVICE_PRINCIPAL_ID=ddbc6916-4198-479d-bed6-c0f3fa23a294
+AZURE_APP_ID=57c410a3-ff95-4628-89e8-0dc596004291
 AZURE_ACCOUNT_STORAGE=testredi1storage
-AZURE_STORAGE_VOLUME=traefik-files-volume
+AZURE_STORAGE_VOLUME=connectsource-traefik-files
 SYSTEM_ENVIRONMENT=development
-AZURE_ENVIRONMENT_NAME=test-base
+AZURE_ENVIRONMENT_NAME=test-connectsource
 AZURE_IDENTITY_NAME=test-redi-managed-identity
-KEY_VAULT_NAME=test-redi
-SECRET_STORE_NAME=testsecretstore
-HOST_NAME=base.redi3.dev/listingapi
\ No newline at end of file
+KEY_VAULT_NAME=test-connectsource
+SECRET_STORE_NAME=listingsecretstore
+HOST_NAME=connectsource.redi3.dev/listingapi
+AZURE_GLOBAL_SUBSCRIPTION_ID=43530b49-4eaf-4344-8dd5-824eaec5485d
+AZURE_SUBSCRIPTION_ID=a3cf024e-e2c6-476c-a5d4-1fb1f66ca6a9
\ No newline at end of file
