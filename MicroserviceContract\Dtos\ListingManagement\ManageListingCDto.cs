﻿using Redi.Prime3.MicroService.BaseLib;
using MicroserviceContract.Dtos.Listing;
using Sql;
using System.Text.Json.Serialization;
using MicroserviceContract.Dtos.Common;
namespace MicroserviceContract.Dtos.ListingManagement
{
    [Mappable(nameof(ListingId))]
    public class ManageListingListCDto
    {
        public Int64? ListingId { set; get; }
        /// <summary>
        /// Unique Id for Listing that is used by public facing front-ends to identify the listing.
        /// </summary>
        public string? ReferenceNo { get; set; }
        public string? Subject { get; set; }
        /// <summary>
        /// Status of the listing: Draft, Pending, Active, Expired, Paused
        /// </summary>
        public string? StatusCode { get; set; }
        /// <summary>
        /// Identifies the type of listing. 0 is default.
        /// </summary>
        public short? ListingTypeId { get; set; }

        /// <summary>
        /// The parent listing this listing belongs to. Optional.
        /// Allows listings to have child listings.The child listings could serve other purposes.
        /// </summary>
        public Int64? ParentListingId { get; set; }
        /// <summary>
        /// Optional sort order for adjusting order of small sets of listings
        /// </summary>
        public Int32? SortOrder { get; set; }

        public string? Icon { get; set; }

        /// <summary>
        /// The Lat/Long of the listing
        /// </summary>
        public GpslocationCDto? GpsLocation { get; set; }

        [JsonIgnore]
        public double? Latitude 
        { set {
                if (value != null)
                {
                    if (GpsLocation == null) { GpsLocation = new GpslocationCDto(); }
                    GpsLocation.Latitude = (decimal)value;
                }
            } 
        }
        [JsonIgnore]
        public double? Longitude
        {
            set
            {
                if (value != null)
                {
                    if (GpsLocation == null) { GpsLocation = new GpslocationCDto(); }
                    GpsLocation.Longitude = (decimal)value;
                }
            }
        }
        /// <summary>
        /// When the listing is Active from
        /// </summary>
        public DateTimeOffset FromDate { get; set; }
        /// <summary>
        /// When the listing is active to. After this date it will be Expired (status) automatically.
        /// No value indicates no expiry
        /// </summary>
        public DateTimeOffset? ToDate { get; set; }
        /// <summary>
        /// Default media to be used when displaying listing
        /// </summary>
        public Int64? ProfileListingMediaId { get; set; }
        /// <summary>
        /// Default media url to be used when displaying listing
        /// </summary>
        public string? ProfileListingMediaUrl { get; set; }
        public Int64? ParentEntityIntId { set; get; }
        public Guid? ParentEntityId { set; get; }
        public string? ParentEntityType { get; set; }
        public Guid? ParentEntityId2 { set; get; }
        public string? ParentEntityType2 { get; set; }
        public DateTimeOffset CreatedOn { get; set; }
        public string? CreatedByName { get; set; }
        public DateTimeOffset? ModifiedOn { get; set; }
        public string? ModifiedByName { get; set; }
        /// <summary>
        /// Distance away from requested location. Calculated on listing call.
        /// </summary>
        public decimal? DistanceAwayInKm { get; set; }
        /// <summary>
        /// Indicates who can view the listing.
        /// 0 - public, everyone(default)
        /// 1 - private, only the owner
        /// 2 - members
        /// </summary>
        public byte VisibilityId { get; set; } = 0;
    }

    public class ManageListingCDto : ManageListingListCDto
    {
        public string? Description { get; set; }
        public bool? IsFavourite { get; set; }
        public List<dynamic>? PartyViewAccess { get; set; }
        public List<dynamic>? PartyEditAccess { get; set; }
    }

    public class ManageListingWithDataAndMediaCDto : ManageListingCDto
    {
        [IgnoreDbMapping]
        public string? ListingStatusLabel {  get; set; }
        [IgnoreDbMapping]
        public string? VisibilityLabel { get; set; }
        [IgnoreDbMapping]
        public string? ListingTypeLabel {  get; set; }
        [IgnoreDbMapping]
        public string? ParentEntityName { get; set; }
        [IgnoreDbMapping]
        public string? ParentEntityName2 { get; set; }
        public string? ParentListingSubject { get; set; }
        [IgnoreDbMapping]
        public List<ManageListingMediaCDto>? Media { get; set; }

        [IgnoreDbMapping]
        public List<ManageListingAttributeCDto>? Attributes { get; set; }
        /// <summary>
        /// Simple name value pairs for all the returned attributes
        /// </summary>
        public Dictionary<string, object?>? Fields { get; set; }
    }

    public class ManageListingWithDisplayGroupedDataAndMediaCDto : ManageListingCDto
    {
        [IgnoreDbMapping]
        public ListingCDto? ParentListing { get; set; }
        [IgnoreDbMapping]
        public List<ManageListingMediaCDto>? Media { get; set; }

        [IgnoreDbMapping]
        public List<ManageListingDisplayContainerCDto>? Attributes { get; set; }
        /// <summary>
        /// Simple name value pairs for all the returned attributes
        /// </summary>
        public Dictionary<string, object?>? Fields { get; set; }
        /// <summary>
        /// List of Tags linked to the Listing.
        /// </summary>
        public List<ListingTagCDto>? Tags { get; set; }
    }

    [Mappable(nameof(ListingMediaId))]
    public class ManageListingMediaCDto : DtoBase
    {
        public Int64? ListingMediaId { set; get; } 
        public Int64 ListingId { set; get; }
        public string? MediaUrl { set; get; }
        public string? Title { set; get; }
        public string? MediaTypeCode { set; get; }
        public string? MediaCategoryCode { set; get; }
        public string? MediaTypeLabel { set; get; }
        public string? MediaCategoryLabel { set; get; }
        public int SortOrder {  set; get; }
    }

    [Mappable(nameof(DisplayContainerCode))]
    public class ManageListingDisplayContainerCDto
    {
        public string? DisplayContainerCode { set; get; }
        public string? Title { set; get; }
        public string? IsShowTitle { set; get; }
        public string? Icon { set; get; }
        public bool? Enabled { set; get; }
        public string? HelpText { set; get; }

        [IgnoreDbMapping]
        public List<ManageListingAttributeCDto>? Attributes { set; get; }
    }

    [Mappable(nameof(ListingAttributeId))]
    public class ManageListingAttributeCDto
    {
        public Int64 ListingAttributeId { set; get; }
        public string? AttributeCode { set; get; }
        public string? ValueString { set; get; }
        public string? ValueStringMax { set; get; }
        public decimal? ValueNumeric { set; get; }
        public DateTimeOffset? ValueDateTime { set; get; }
        public GpslocationCDto? ValueGeography { set; get; }
        [JsonIgnore]
        public double? Latitude
        {
            set
            {
                if (value != null)
                {
                    if (ValueGeography == null) { ValueGeography = new GpslocationCDto(); }
                    ValueGeography.Latitude = (decimal)value;
                }
            }
        }
        [JsonIgnore]
        public double? Longitude
        {
            set
            {
                if (value != null)
                {
                    if (ValueGeography == null) { ValueGeography = new GpslocationCDto(); }
                    ValueGeography.Longitude = (decimal)value;
                }
            }
        }
        public int? DisplayContainerAttributeId { set; get; }
        public string? DisplayContainerCode { set; get; }
        public bool IsReadOnly { set; get; }
        public string? Label { set; get; }
        public int? SortOrder { set; get; }
        public string? AttributeGroupCode { set; get; }
        public string? AttributeValueTypeCode { set; get; }
        public string? Icon { set; get; }
        public string? ReadAccessClaim { set; get; }
        public string? WriteAccessClaim { set; get; }
        public bool IsManyAllowed { set; get; }
        public string? InputTypeCode { set; get; }
        public short? NumericDecimalPlaces { get; set; }
        public bool? IsRequired { set; get; }
        public decimal? NumericMinValue { set; get; }
        public decimal? NumericMaxValue { set; get; }
        public int? TextMinCharacters {  set; get; }
        public int? TextMaxCharacters { set; get; }
        public int? MinDateDaysFromToday { set; get; }
        public int? MaxDateDaysFromToday { get; set; }
        public int? MinSelectionsRequired { set; get; }
        public int? MaxSelectionsAllowed { set; get; }
        public int? MaxStars {  set; get; }
        public string? HelpText { set; get; }

        public string? DefaultTextValue { set; get; }
        public decimal? DefaultNumericValue { set; get; }
        public int? DefaultDateAddDays { set; get; }
        public int? DefaultDateAddMonths { set; get; }
        public int? DefaultTimeAddMinutes { set; get; }

    }

    [Mappable(nameof(SortOptionId))]
    public class ManageListingSortOptionCDto
    {
        public int SortOptionId { set; get; }
        public string? Label { set; get; }
        public string? DisplayContainerCode { set; get; }
    }
}
