﻿# Listing Micro Service

Listing Micro Service is s set of API's and database schema that supports the display and filtering of sets of data in a generic manner.
listings can be used for different purposes within the same implementation. ListingTypeId defines the different Types (purposes).
An Item in a listing is made up of:

* Subject  
* Detailed description (optional)
* Location (optional)
* From and To Date (when the listing is visible/available)
* Profile image/video (optional)
* Parent Entity (x2) the listing item is related to or owned by (eg. a party)
* Reference No - unique system generated reference that can be displayed to users and used to fetch a listing
* Visibilty setting. 0 Public, 1 Private (only the owner can view), 2 Logged in members only
* Listing Type. Supports listings been used for many purposes. 0 - Default, 1, Settings, 2 - Support, 3 = Task, .. as required.
* Attributes. A listing item can have an unlimited number of attributes.

## API Endpoints

*For full api documentation see <http://redi-microservice-listing.docker.localhost:8084/scalar/v1>*

## Attributes 

Attributes are a key part of the Listing model. A Listing item can have an unlimited number of attributes.
An attribute can be used to store any field for a listing (an alternative to a database table with many columns). Examples uses: car make, car model, car rego, tool type, truck engine type, gps location, price, size, colour, age, star rating, tag/s, category, features (multi-select from list of features)

An attributes value can be a: 

* string (max 500 chars),   
* numeric (decimal),   
* datetime (offset), 
* large string (max), 
* or a Geography (lat/long)

An Attribute is defined as having:

* A unique attribute code - this code can be used to easily identify a specific attribute in front-end or backend.
* Label - this is displayed to end users
* Value Type - how it is stored (String, Numeric, DateTime, etc)
* Description - detailed description of the purpose of an attribute
* Many Allowed - set to true to indicate the attribute can have many values (ie. dropdown multi-select)
* Icon - icon to display on UI _(optional)_
* Input Type - for an attribute that can be entered by users this is the default input field type (this can be overriden per Display Container)
* Read Access Claim - security claim to control read/view access _(optional)_
* Write Access Claim - security claim to control write/edit access _(optional)_
* Read Access Claim - security claim to control read/view access _(optional)_
* Selection Set - a set of values that can be selected for an attribute (ie. dropdown choices) _(optional)_

## Display Containers
Display Containers are a key part of the listing model. They allow attributes to be grouped together for different display purposes. A display container can represent:

* A set of filters to display next to a list (the filters control what listing items are included in the list)
* Summary card. A small set of attributes to display for a listing
* Multiple sets of details (can have as many detail Display Containers as required) for a listing (ie. a car listing may have a Engine Display Container that includes all attributes related to the engine)
* Edit/Create set of attributes. This defines the fields (attributes) to be entered along with the order to display the fields and the input type.

A display container is defined as having:

- A unique display container code - this code can be used to easily identify or request a display container from the front-end
- Title - this should be displayed on the front-end (if IsShowTitle is true)
- Description - describes the purpose of a Display Container, for documentation purposes only _(optional)_
- Show Title flag - title to show on front-end if true (default true)
- Icon - icon to display on UI _(optional)_
- Help Text - text to show to end user regarding a Display Container _(optional)_
- A list of Attributes
    * Sort Order - control the order the attributes are displayed
	* Read Only - on an edit style display container indicates an attribute is read only
	* Input Type - overrides the attributes default input type.

### Attribute Input Types
Input Types that may be supported by a front-end. The input can be used for entry/update of an attribute as well as for search filters. 

* Dropdown
* Text
* Text Area (multi-line)
* Checkbox
* Integer
* Decimal
* Radio Buttons
* Star Rating
* Address
* Date
* Time
* Map
* Date Range _(for filters)_
* Numeric Range _(for filters)_
* Price Range _(for filters)_
* Distance _(for filters)_
* Suburb
* Multi-select

## Listing Life Cycle

A listing has the following lifecycle (status):

- Draft  - been created
- Pending - ready but FromDate not yet
- Active - currently displayed
- Paused - temporarily not displayed
- Expired - ToDate has past

A background process (Azure Function) will automatically manage the Status based on FromDate and ToDate.
A filtered Index is in place based on Active Status, SQL hints will be required to force use.

## Multi Tenant Support

Multi-Tenancy support is controlled at the Listing Type level. Each Listing Type is set to either Tenant based or global.
Tenant based will ensure only users of the Tenant can interact with a Listing of the Listing Type.
Global based will have no TenantId associated with each Listing record.
Correctly setting [IsTenantBased] on the ListingType is critical. Set to true if Listing is tenant based, false for global.

## Other Listing Features

* Favourites - a user can favourite a listing
* Favourite Sets - a user can have different sets of favourites
* Display Container Sort Options - Provides sort options that can be displayed on the UI in a dropdown when showing a list (eg. Lowest to highest price, Most popular)
* Listing media - a listing can have many media records. They can be images, photos, videos, pdf's, etc. They can be grouped into category for any purpose (public, private, resolution, front, back, etc)

## Listing Data Model

![Database Model](./ListingDatabaseModel.svg)
___

## Installing and Using this Micro Service Project

Following Microservice Software Architecture patterns, Microservices are loosely coupled autonomous services that collaborate with other services in a system. The idea that

services can be tested, built and deployed independently of other services that makeup the entirety of a system. Platform and language are not important in this regard, as services

talk to each other through the use of DAPR (Distributed Application Runtime) and deployed through Docker, the container of choice for Microservices. DAPR for its microservice connectivity

and vast integration of building blocks (Redis, Azure, SQL server), and Dockers availability to run on most modern systems.

As such, microservices should only handle data for its schema e.g. A Customer microservice handles data for only customer related tables.

### PREREQUISITES

1. Install DAPR CLI for Windows (https://docs.dapr.io/getting-started/install-dapr-cli/)

a. Open Command Prompt in Admin Mode and copy below line

powershell -Command "iwr -useb https://raw.githubusercontent.com/dapr/cli/master/install/install.ps1 | iex"

2. Install Docker (https://docs.docker.com/get-docker/)

### COMMANDS (CLI)

2. Init Dapr environment 

dapr init

3. Run Microservice (Without Docker) (https://docs.dapr.io/getting-started/get-started-api/)

dapr run --app-id redi-microservice-listing --components-path ./components --config ./configuration/setup-config.yaml --app-port 3500 -- dotnet run

4. Run Microservice (With Docker) (https://docs.dapr.io/getting-started/get-started-api/)

If you are using Visual Studio. Right-click on the docker-compose file and set as 'Set as Start-up project', and press the 'Docker Compose' button at the top.

If you have Docker Desktop installed (which you should have), you can view the Containers running for each Service.

Notice that all the components in the docker-compose.yml file have their own container when running.

5. To connect to a local DB. 

Create a Security User Login. Then in your connection string point to the DB and port SQL server is running on. Something similar to below:

Data Source=host.docker.internal,1436;Initial Catalog=redi-base-db;User=admin;Password=**********;Min Pool Size=5;Max Pool Size=100;Pooling=True;multipleactiveresultsets=True;application name=redi-base-db;

6. Zipkin

You can visit Zipkin traces at http://zipkin:9411

7. Test, call 
	HTTP: http://redi-microservice-listing.docker.localhost:8084/api/Test/Get to fetch the information within the Secret store. Visit the above Zipkin link to view traces.
	HTTPS: http://redi-microservice-listing.docker.localhost:8443/api/Test/Get to fetch the information within the Secret store. Visit the above Zipkin link to view traces.

8. Visit Traefik (traefik/middlewares) at http://localhost:8084