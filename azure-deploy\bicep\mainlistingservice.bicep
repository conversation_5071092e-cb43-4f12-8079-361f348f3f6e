param location string = resourceGroup().location
param enviornmentName string
param keyVaultName string
param identityName string
param systemEnvironment string
param dockerUserName string
@secure()
param dockerPassword string
param imageTagVersion string
param dockerRepoName string
@secure()
param secretStoreName string
param hostName string
param azureStorageAccountName string

module identityModule 'modules/container-apps/identity.bicep' = {
  name: '${deployment().name}--identity'
  params: {
    identityName: identityName
  }
}

module apiserviceModule 'modules/container-apps/listingapiservice.bicep' = {
  name: '${deployment().name}--apiservice'
  params: {
    imageTagVersion:imageTagVersion
    systemEnvironment: systemEnvironment
    dockerUserName: dockerUserName
    enviornmentName: enviornmentName
    location: location
    dockerPasswordRef: dockerPassword
    managedIdentityObjectId: identityModule.outputs.identityId
    dockerRepoName: dockerRepoName
    secretStoreName: secretStoreName
    hostName: hostName
    azureStorageAccountName: azureStorageAccountName
    managedIdentityClientId: identityModule.outputs.identityClientId
  }
}

module secretStoreModule 'modules/dapr-components/secretstore.bicep' = {
  name: '${deployment().name}--dapr-secretstore'
  dependsOn: [
    apiserviceModule
  ]
  params: {
    environmentName: enviornmentName
    keyVaultName: keyVaultName
    azureClientId: identityModule.outputs.identityClientId
    dockerRepoName: dockerRepoName
    secretStoreName: secretStoreName
  }
}
