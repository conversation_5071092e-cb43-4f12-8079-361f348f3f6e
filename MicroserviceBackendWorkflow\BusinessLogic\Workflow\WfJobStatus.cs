using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Collections.Specialized;

namespace MicroserviceBackendWorkflow.BusinessLogic
{
    public class WfJobStatus : BusinessLogicBase
    {
        public WfJobStatus(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a single WfJobStatus by ID
        /// </summary>
        /// <param name="wfJobStatusId">The workflow job status ID</param>
        /// <param name="ignoreErrorIfNotExists">If false, throws exception when not found</param>
        /// <returns>WfJobStatusDto or null if not found</returns>
        internal async Task<WfJobStatusDto?> GetAsync(byte wfJobStatusId, bool ignoreErrorIfNotExists = false)
        {
            string sql = @"
                SELECT [WfJobStatusId]
                      ,[Label]
                      ,[IsEnabled]
                      ,[SortOrder]
                FROM [workflow].[WfJobStatus]
                WHERE [WfJobStatusId] = @wfJobStatusId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("wfJobStatusId", wfJobStatusId);
                var result = await command.SelectSingle<WfJobStatusDto>();

                if (result == null && !ignoreErrorIfNotExists)
                {
                    throw new HttpRequestException($"WfJobStatus with ID '{wfJobStatusId}' not found", null, System.Net.HttpStatusCode.NotFound);
                }

                return result;
            }
        }

        /// <summary>
        /// Create a new WfJobStatus
        /// </summary>
        /// <param name="dto">The WfJobStatus data</param>
        internal async Task CreateAsync(WfJobStatusDto dto)
        {
            if (dto.WfJobStatusId <= 0)
            {
                throw new HttpRequestException("WfJobStatusId must be greater than 0", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (string.IsNullOrWhiteSpace(dto.Label))
            {
                throw new HttpRequestException("Label cannot be null or empty", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            var exists = await GetAsync(dto.WfJobStatusId);
            if (exists != null)
            {
                throw new HttpRequestException($"WfJobStatus with ID '{dto.WfJobStatusId}' already exists", null, System.Net.HttpStatusCode.Conflict);
            }

            string sql = @"
                INSERT INTO [workflow].[WfJobStatus]
                ([WfJobStatusId], [Label], [IsEnabled], [SortOrder])
                VALUES
                (@WfJobStatusId, @Label, @IsEnabled, @SortOrder)";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArguments(dto);
                await command.Execute();
            }
        }

        /// <summary>
        /// Update an existing WfJobStatus
        /// </summary>
        /// <param name="dto">The WfJobStatus data</param>
        internal async Task UpdateAsync(WfJobStatusDto dto)
        {
            if (dto.WfJobStatusId <= 0)
            {
                throw new HttpRequestException("WfJobStatusId must be greater than 0", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (string.IsNullOrWhiteSpace(dto.Label))
            {
                throw new HttpRequestException("Label cannot be null or empty", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            var exists = await GetAsync(dto.WfJobStatusId);
            if (exists == null)
            {
                throw new HttpRequestException($"WfJobStatus with ID '{dto.WfJobStatusId}' not found", null, System.Net.HttpStatusCode.NotFound);
            }

            string sql = @"
                UPDATE [workflow].[WfJobStatus]
                SET [Label] = @Label,
                    [IsEnabled] = @IsEnabled,
                    [SortOrder] = @SortOrder
                WHERE [WfJobStatusId] = @WfJobStatusId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArguments(dto);
                await command.Execute();
            }
        }

        /// <summary>
        /// Delete a WfJobStatus
        /// </summary>
        /// <param name="wfJobStatusId">The workflow job status ID</param>
        internal async Task DeleteAsync(byte wfJobStatusId)
        {
            var exists = await GetAsync(wfJobStatusId);
            if (exists == null)
            {
                throw new HttpRequestException($"WfJobStatus with ID '{wfJobStatusId}' not found", null, System.Net.HttpStatusCode.NotFound);
            }

            string sql = @"
                DELETE FROM [workflow].[WfJobStatus]
                WHERE [WfJobStatusId] = @wfJobStatusId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Delete, sql))
            {
                command.AddArgument("wfJobStatusId", wfJobStatusId);
                await command.Execute();
            }
        }

        /// <summary>
        /// Get a list of WfJobStatuses with filtering and paging
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="showDisabled">Include disabled records</param>
        /// <returns>List of WfJobStatuses</returns>
        internal async Task<ListResponseDto<WfJobStatusListDto>> GetListAsync(StandardListParameters standardListParameters, bool showDisabled = false)
        {
            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "WfJobStatusId", "[WfJobStatus].[WfJobStatusId]" },
                { "Label", "[WfJobStatus].[Label]" },
                { "SortOrder", "[WfJobStatus].[SortOrder]" },
                { "IsEnabled", "[WfJobStatus].[IsEnabled]" }
            };

            string sql = @"
                SELECT [WfJobStatusId]
                      ,[Label]
                      ,[IsEnabled]
                      ,[SortOrder]
                FROM [workflow].[WfJobStatus]
                WHERE 1=1";

            if (!showDisabled)
            {
                sql += " AND [IsEnabled] = 1";
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "SortOrder");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<WfJobStatusListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);
                result.List = await command.SelectMany<WfJobStatusListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[WfJobStatus]
                WHERE 1=1";

            if (!showDisabled)
            {
                countSql += " AND [IsEnabled] = 1";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }
    }
}
