﻿using MicroserviceContract.Dtos.Booking;
using MicroserviceContract.Dtos;
using Sql;
using MicroserviceBackendListing.Enums;
using Redi.Prime3.MicroService.BaseLib;

namespace MicroserviceBackendListing.BusinessLogic.Booking.Management
{
    public partial class BookingItemAvailability : BusinessLogicBase
    {
        internal async Task<int?> CreateBookingItemAvailabilityAsync(RequestCDto<BookingItemAvailableRequestCDto> requestCDto)
        {
            if (requestCDto == null || requestCDto.Data == null) { return null; }
            BookingItemAvailableRequestCDto dto = requestCDto.Data;

            var exists = dto.BookingItemAvailabilityId != null ? await GetBookingItemAvailabilityAsync((int)dto.BookingItemAvailabilityId) : null;
            if (exists != null)
            {
                //await BookingItemAvailabilityId(requestCDto);
                return dto.BookingItemId;
            }


            await ValidateBookingItemAvailability(dto.BookingItemAvailabilityId, requestCDto);

            string sql = @"
INSERT INTO [booking].[BookingItemAvailability](
       [BookingItemId]
      ,[AvailabilityModeId]
      ,[FromDate]
      ,[ToDate]
      ,[FromTime]
      ,[ToTime]
      ,[IncludeMonday]
      ,[IncludeTuesday]
      ,[IncludeWednesday]
      ,[IncludeThursday]
      ,[IncludeFriday]
      ,[IncludeSaturday]
      ,[IncludeSunday]
      ,[IncludePublicHolidays]
      ,[CreatedOn]
      ,[CreatedByName]
      ,[Deleted]
)
VALUES
(
      @BookingItemId,
      @AvailabilityModeId,
      @FromDate,
      @ToDate,
      @FromTime,
      @ToTime,
      @IncludeMonday,
      @IncludeTuesday,
      @IncludeWednesday,
      @IncludeThursday,
      @IncludeFriday,
      @IncludeSaturday,
      @IncludeSunday,
      @IncludePublicHolidays,
      @CreatedOn,
      @CreatedByName,
      @Deleted
);";
            int result = 0;
            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArgument("AvailabilityModeId", BookingItemAvailabilityModeEnum.Available);
                command.AddArgument("CreatedOn", DateTimeOffset.UtcNow);
                command.AddArgument("CreatedByName", _utils.UserFullName);
                command.AddArgument("Deleted", false);

                // Create
                command.AddArguments(dto);

                result = await command.ExecuteAndReturnIdentity();

                // Log
                string description = $"New BookingItemAvailability with mode Available created by {_utils.UserFullName}";
                await AddBookingItemAvailabilityEvent(result, description);
            }

            return result;
        }

        internal async Task UpdateBookingItemAvailabilityAsync(RequestCDto<BookingItemAvailableRequestCDto>? requestCDto)
        {
            if (requestCDto == null || requestCDto.Data == null) { return; }
            BookingItemAvailableRequestCDto dto = requestCDto.Data;

            var exists = dto.BookingItemAvailabilityId != null ? await GetBookingItemAvailabilityAsync((int)dto.BookingItemAvailabilityId) : null;
            if (exists == null)
            {
                await CreateBookingItemAvailabilityAsync(requestCDto);
                return;
            }


            bool hasChanged = await ValidateBookingItemAvailability(dto.BookingItemAvailabilityId, requestCDto);
            if (!hasChanged)
            {
                return;
            }

            string sql = @"
UPDATE [booking].[BookingItemAvailability]
       SET [BookingItemId] = @BookingItemId
      ,[FromDate] = @FromDate
      ,[ToDate] = @ToDate
      ,[FromTime] = @FromTime
      ,[ToTime] = @ToTime
      ,[IncludeMonday] = @IncludeMonday
      ,[IncludeTuesday] = @IncludeTuesday
      ,[IncludeWednesday] = @IncludeWednesday
      ,[IncludeThursday] = @IncludeThursday
      ,[IncludeFriday] = @IncludeFriday
      ,[IncludeSaturday] = @IncludeSaturday
      ,[IncludeSunday] = @IncludeSunday
      ,[IncludePublicHolidays] = @IncludePublicHolidays
      ,[ModifiedOn] = @ModifiedOn
      ,[ModifiedByName] = @ModifiedByName
WHERE [BookingItemAvailabilityId] = @BookingItemAvailabilityId;
";
            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {

                // Create
                command.AddArguments(dto);
                command.AddArgument("ModifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("ModifiedByName", _utils.UserFullName);

                await command.Execute();

                string description = $"BookingItemAvailability updated ";
#pragma warning disable CS8629 // Nullable value type may be null.
                await AddBookingItemAvailabilityEvent((int)dto.BookingItemAvailabilityId, description);
#pragma warning restore CS8629 // Nullable value type may be null.
            }
        }

        internal async Task DeleteBookingItemAvailabilityAsync(int bookingItemAvailabilityId)
        {
            var existing = await GetBookingItemAvailabilityAsync(bookingItemAvailabilityId);
            if (existing == null)
            {
                throw new ArgumentException("Booking item availability does not exist into the db");
            }

            string sql = @"
UPDATE [booking].[BookingItemAvailability]
SET [Deleted] = 1
    ,[ModifiedOn] = @ModifiedOn
    ,[ModifiedByName] = @ModifiedByName
WHERE [BookingItemAvailabilityId] = @bookingItemAvailabilityId";

            if (_utils.TenantId != null)
            {
                sql += " AND [TenantId] = @tenantId";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("Deleted", true);
                command.AddArgument("ModifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("ModifiedByName", _utils.UserFullName);
                command.AddArgument("bookingItemAvailabilityId", bookingItemAvailabilityId);
                command.AddArgument("tenantId", _utils.TenantId);

                await command.Execute();
            }
        }

        internal async Task UnDeleteBookingItemAvailabilityAsync(int bookingItemAvailabilityId)
        {
            var existing = await GetBookingItemAvailabilityAsync(bookingItemAvailabilityId);
            if (existing == null)
            {
                throw new ArgumentException("Booking item availability does not exist into the db");
            }

            string sql = @"
UPDATE [booking].[BookingItemAvailability]
SET [Deleted] = 0
    ,[ModifiedOn] = @ModifiedOn
    ,[ModifiedByName] = @ModifiedByName
WHERE [BookingItemAvailabilityId] = @bookingItemAvailabilityId";

            if (_utils.TenantId != null)
            {
                sql += " AND [TenantId] = @tenantId";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("Deleted", true);
                command.AddArgument("ModifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("ModifiedByName", _utils.UserFullName);
                command.AddArgument("bookingItemAvailabilityId", bookingItemAvailabilityId);
                command.AddArgument("tenantId", _utils.TenantId);

                await command.Execute();
            }
        }

#pragma warning disable CS1998 // Async method lacks 'await' operators and will run synchronously
        internal async Task<bool> ValidateBookingItemAvailability(int? BookingItemAvailabilityId, RequestCDto<BookingItemAvailableRequestCDto> newRequestCDto, BookingItemAvailableResponseCDto? existCDto = null)
#pragma warning restore CS1998 // Async method lacks 'await' operators and will run synchronously
        {
            if (newRequestCDto == null || newRequestCDto.Data == null) { return false; }
            // The ExcludeFields cannot be directly updated by data passed into this call.
            List<string> excludeFields = new List<string>() { "BookingItemId", "BookingItemAvailabilityId", "CreatedOn", "CreatedByName", "Deleted", "ModifiedOn", "ModifiedByName", "Statistics" };

            var dto = newRequestCDto.Data;
            bool isNew = existCDto == null;
            // Validate fields
            bool hasChanges = false;

            // Check what data has changed (determine what is to be updated in the database)
            if (isNew == false && existCDto != null)
            {
                if (newRequestCDto?.ClearFields?.ContainsKey("FromDate") == true) { dto.FromDate = null; hasChanges = true; }
                else { if (dto.FromDate != null && dto.FromDate != existCDto.FromDate) { hasChanges = true; } else { dto.FromDate = existCDto.FromDate; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("ToDate") == true) { dto.ToDate = null; hasChanges = true; }
                else { if (dto.ToDate != null && dto.ToDate != existCDto.ToDate) { hasChanges = true; } else { dto.ToDate = existCDto.ToDate; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("FromTime") == true) { dto.FromTime = null; hasChanges = true; }
                else { if (dto.FromTime != null && dto.FromTime != existCDto.FromTime) { hasChanges = true; } else { dto.FromTime = existCDto.FromTime; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("ToTime") == true) { dto.ToTime = null; hasChanges = true; }
                else { if (dto.ToTime != null && dto.ToTime != existCDto.ToTime) { hasChanges = true; } else { dto.ToTime = existCDto.ToTime; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("IncludeMonday") == true) { dto.IncludeMonday = null; hasChanges = true; }
                else { if (dto.IncludeMonday != null && dto.IncludeMonday != existCDto.IncludeMonday) { hasChanges = true; } else { dto.IncludeMonday = existCDto.IncludeMonday; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("FromDate") == true) { dto.IncludeTuesday = null; hasChanges = true; }
                else { if (dto.IncludeTuesday != null && dto.IncludeTuesday != existCDto.IncludeTuesday) { hasChanges = true; } else { dto.IncludeTuesday = existCDto.IncludeTuesday; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("IncludeWednesday") == true) { dto.IncludeWednesday = null; hasChanges = true; }
                else { if (dto.IncludeWednesday != null && dto.IncludeWednesday != existCDto.IncludeWednesday) { hasChanges = true; } else { dto.IncludeWednesday = existCDto.IncludeWednesday; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("IncludeThursday") == true) { dto.IncludeThursday = null; hasChanges = true; }
                else { if (dto.IncludeThursday != null && dto.IncludeThursday != existCDto.IncludeThursday) { hasChanges = true; } else { dto.IncludeThursday = existCDto.IncludeThursday; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("IncludeFriday") == true) { dto.IncludeFriday = null; hasChanges = true; }
                else { if (dto.IncludeFriday != null && dto.IncludeFriday != existCDto.IncludeFriday) { hasChanges = true; } else { dto.IncludeFriday = existCDto.IncludeFriday; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("IncludeSaturday") == true) { dto.IncludeSaturday = null; hasChanges = true; }
                else { if (dto.IncludeSaturday != null && dto.IncludeSaturday != existCDto.IncludeSaturday) { hasChanges = true; } else { dto.IncludeSaturday = existCDto.IncludeSaturday; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("IncludeSunday") == true) { dto.IncludeSunday = null; hasChanges = true; }
                else { if (dto.IncludeSunday != null && dto.IncludeSunday != existCDto.IncludeSunday) { hasChanges = true; } else { dto.IncludeSunday = existCDto.IncludeSunday; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("IncludePublicHolidays") == true) { dto.IncludePublicHolidays = null; hasChanges = true; }
                else { if (dto.IncludePublicHolidays != null && dto.IncludePublicHolidays != existCDto.IncludePublicHolidays) { hasChanges = true; } else { dto.IncludePublicHolidays = existCDto.IncludePublicHolidays; } }
            }

            if (!hasChanges && !isNew)
            {
                // no data has changed to exit.
                return (false);
            }

            if(dto.BookingItemId == null) { throw new HttpRequestException("BookingItemId is required."); }
            if (dto.IncludeMonday == null) { throw new HttpRequestException("IncludeMonday is required."); }
            if (dto.IncludeTuesday == null) { throw new HttpRequestException("IncludeTuesday is required."); }
            if (dto.IncludeWednesday == null) { throw new HttpRequestException("IncludeWednesday is required."); }
            if (dto.IncludeThursday == null) { throw new HttpRequestException("IncludeThursday is required."); }
            if (dto.IncludeFriday == null) { throw new HttpRequestException("IncludeFriday is required."); }
            if (dto.IncludeSaturday == null) { throw new HttpRequestException("IncludeSaturday is required."); }
            if (dto.IncludeSunday == null) { throw new HttpRequestException("IncludeSunday is required."); }
            if (dto.IncludePublicHolidays == null) { throw new HttpRequestException("IncludePublicHolidays is required."); }


            return true;
        }

        private async Task AddBookingItemAvailabilityEvent(int BookingItemAvailabilityId, string description)
        {
            EventCDto dto = new EventCDto()
            {
                EventId = 0,
                ParentEntityIntId = BookingItemAvailabilityId,
                ParentEntityType = ENTITY_TYPE,
                Description = (description.Length > 4000 ? description.Substring(0, 4000) : description),
                CreatedOn = DateTimeOffset.UtcNow,
                CreatedByName = _utils.UserFullName
            };

            string sql = @"
            INSERT INTO [common].[Event]
                        (
                         [ParentEntityIntId]
                        ,[ParentEntityType]
                        ,[Description]
                        ,[CreatedOn]
                        ,[CreatedByName]
                        )
            VALUES
                        (
                         @ParentEntityIntId
                        ,@ParentEntityType
                        ,@Description
                        ,@CreatedOn
                        ,@CreatedByName
                        )";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArguments(dto);

                await command.Execute();
            }
        }
    }
}
