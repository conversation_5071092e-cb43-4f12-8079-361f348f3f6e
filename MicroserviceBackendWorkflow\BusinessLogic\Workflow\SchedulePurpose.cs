using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Collections.Specialized;

namespace MicroserviceBackendWorkflow.BusinessLogic
{
    public class SchedulePurpose : BusinessLogicBase
    {
        public SchedulePurpose(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a single SchedulePurpose by ID
        /// </summary>
        /// <param name="schedulePurposeId">The schedule purpose ID</param>
        /// <returns>SchedulePurposeDto or null if not found</returns>
        internal async Task<SchedulePurposeDto?> GetAsync(short schedulePurposeId)
        {
            string sql = @"
                SELECT [SchedulePurposeId]
                      ,[Label]
                      ,[IsEnabled]
                FROM [workflow].[SchedulePurpose]
                WHERE [SchedulePurposeId] = @schedulePurposeId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("schedulePurposeId", schedulePurposeId);
                return await command.SelectSingle<SchedulePurposeDto>();
            }
        }

        /// <summary>
        /// Get a list of SchedulePurposes with filtering and paging
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="showDisabled">Include disabled records</param>
        /// <returns>List of SchedulePurposes</returns>
        internal async Task<ListResponseDto<SchedulePurposeListDto>> GetListAsync(StandardListParameters? standardListParameters = null, bool showDisabled = false)
        {
            if (standardListParameters == null)
            {
                standardListParameters = new StandardListParameters();
            }

            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "SchedulePurposeId", "[SchedulePurpose].[SchedulePurposeId]" },
                { "Label", "[SchedulePurpose].[Label]" },
                { "IsEnabled", "[SchedulePurpose].[IsEnabled]" }
            };

            string sql = @"
                SELECT [SchedulePurposeId]
                      ,[Label]
                      ,[IsEnabled]
                FROM [workflow].[SchedulePurpose]
                WHERE 1=1";

            if (!showDisabled)
            {
                sql += " AND [IsEnabled] = 1";
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "SchedulePurposeId");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<SchedulePurposeListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);
                result.List = await command.SelectMany<SchedulePurposeListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[SchedulePurpose]
                WHERE 1=1";

            if (!showDisabled)
            {
                countSql += " AND [IsEnabled] = 1";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }
    }
}
