﻿using global::MicroserviceBackendListing.BusinessLogic.Base;
using MicroserviceBackendListing.BusinessLogic;
using MicroserviceBackendListing.Dtos;
using MicroserviceBackendListing.Enums;
using Microsoft.AspNetCore.Authorization;

namespace MicroserviceBackendListing.Policies
{
    /// <summary>
    /// Requirement will check if the User performing the request has the required persmission to edit or view other Listing records. Both handlers
    /// used below are evaluated on an OR basis, both checking for 'ListingRoleAccessId' field.
    /// <para>PartyQueryPermissionHandler checks the Query request for 'ListingRoleAccessId'</para>
    /// <para>PartyBodyPermission<PERSON>and<PERSON> checks the Body request for 'ListingRoleAccessId'</para>
    /// </summary>
    public class ListingPermissionRequirement : IAuthorizationRequirement
    {
        /// <summary>
        /// Roles required access to the Listing
        /// </summary>
        public List<ListingAccessRoleEnum> RoleAccesses { get; } = [ListingAccessRoleEnum.EditAccess, ListingAccessRoleEnum.ViewAccess];
        /// <summary>
        /// 
        /// </summary>
        public bool IsManagedListing { get; }
        /// <summary>
        /// Expected Listing Type Id when checking access. Very rarely, some Listings can be managed without checking the ListingAccess table, such as Folders.
        /// e.g. EditFolderListingPolicy will have a requirement to check the Listing Type Id matches the Listing Id/Reference No passed in.
        /// </summary>
        public ListingTypeEnum? ListingTypeId { get; }
        /// <summary>
        /// Requirement will check if the User performing the request has the required persmission to edit or view other Listing records. Both handlers
        /// used below are evaluated on an OR basis, both checking for ListingRoleAccessId field.
        /// <para>ListingQueryPermissionHandler checks the Query request for 'ListingRoleAccessId'</para>
        /// <para>ListingBodyPermissionHandler checks the Body request for 'ListingRoleAccessId'</para>
        /// </summary>
        /// <param name="roleAccesses">Role accesses required</param>
        /// <param name="isManagedListing"></param>
        /// <param name="listingTypeId"></param>
        public ListingPermissionRequirement(List<ListingAccessRoleEnum>? roleAccesses = null, bool isManagedListing = false, ListingTypeEnum? listingTypeId = null)
        {
            RoleAccesses = roleAccesses ?? RoleAccesses;
            IsManagedListing = isManagedListing;
            ListingTypeId = listingTypeId;
        }
    }

    /// <summary>
    /// 
    /// </summary>
    public class ListingQueryPermissionHandler : AuthorizationHandler<ListingPermissionRequirement>
    {
        private readonly Func<ListingAccess> _listingAccessFactory;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="listingAccessFactory"></param>
        public ListingQueryPermissionHandler(Func<ListingAccess> listingAccessFactory)
        {
            _listingAccessFactory = listingAccessFactory;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="context"></param>
        /// <param name="requirement"></param>
        /// <returns></returns>
        protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context, ListingPermissionRequirement requirement)
        {
            if (context.Resource is HttpContext httpContext)
            {
                var requestListingId = httpContext.Request.Query["listingId"];
                var requestReferenceNo = httpContext.Request.Query["referenceNo"];
                long listingId = 0;
                if ((!string.IsNullOrEmpty(requestListingId) && long.TryParse(requestListingId, out listingId)) || (!string.IsNullOrEmpty(requestReferenceNo)))
                {
                    var hasAccess = await _listingAccessFactory().GetHasListingAccess(new() { ListingId = listingId, ReferenceNo = requestReferenceNo, IsManagedListing = requirement.IsManagedListing, RoleAccesses = requirement.RoleAccesses, ListingTypeId = requirement.ListingTypeId });
                    if (hasAccess)
                    {
                        context.Succeed(requirement);
                    }
                    else
                    {
                        context.Fail();
                    }
                }
            }
        }
    }


    /// <summary>
    /// 
    /// </summary>
    public class ListingBodyPermissionHandler : AuthorizationHandler<ListingPermissionRequirement>
    {
        private readonly Func<ListingAccess> _listingAccessFactory;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="listingAccessFactory"></param>
        public ListingBodyPermissionHandler(Func<ListingAccess> listingAccessFactory)
        {
            _listingAccessFactory = listingAccessFactory;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="context"></param>
        /// <param name="requirement"></param>
        /// <returns></returns>
        protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context, ListingPermissionRequirement requirement)
        {
            if (context.Resource is HttpContext httpContext && httpContext.Request.ContentLength > 0 && httpContext.Request.ContentType != null && httpContext.Request.ContentType.Contains("application/json"))
            {
                //Enable Buffer request to allow for reading body and resetting so Controller method can read
                httpContext.Request.EnableBuffering();
                try
                {
                    var result = await httpContext.Request.ReadFromJsonAsync<RequestListingFieldDto>();
                    if (result != null && result.ListingId != null && result.ListingId != 0)
                    {
                        var hasAccess = await _listingAccessFactory().GetHasListingAccess(new() { ListingId = result.ListingId, IsManagedListing = requirement.IsManagedListing, RoleAccesses = requirement.RoleAccesses, ListingTypeId = requirement.ListingTypeId });
                        if (hasAccess)
                        {
                            context.Succeed(requirement);
                        }
                        else
                        {
                            context.Fail();
                        }
                    }
                }
                catch (System.Text.Json.JsonException)
                {
                    // Failed to deserialize body into an object containing ListingId
                    // Fallback to ListingQueryPermissionHandler
                }
                catch (Exception ex)
                {
                    // Failed in some other way -- context.Fail() here?
                }
                finally
                {
                    // Reset the body stream
                    httpContext.Request.Body.Seek(0, SeekOrigin.Begin);
                }
            }
        }
    }
}

