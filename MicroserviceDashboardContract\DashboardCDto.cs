﻿using Sql;

namespace MicroserviceDashboardContract.Dtos
{
    [Mappable(nameof(DashboardId))]
    public class BaseDashboardCDto
    {
        // dashboard table
        public int DashboardId { get; set; }
        public string? Title { get; set; }
        public bool IsShowTitle { get; set; }
        public int DashboardStatusId { get; set; }
        public string? Description { get; set; }
        public string? Icon { get; set; }
        public bool IsTemplate { get; set; }
        public int? InheritedFromTemplateDashboardId { get; set; }
        public int? TenantId { get; set; }
        public bool Deleted { get; set; }
        public string? PublicDashboardUrl { get; set; }
        public int PublicDashboardStatusId { get; set; }
        public int? SortOrder { get; set; }
        public bool IsDisplayAtRootLevel { get; set; }

        // dashboard config table
        public string? ConfigJson { get; set; }
        public string? HelpText { get; set; }
        public int CurrentVersionNo { get; set; }
    }

    public class GetDashboardCDto : BaseDashboardCDto { }

    public class GetListDashboardCDto : BaseDashboardCDto { }
}
