﻿using Dapr.Client;
using MicroserviceBackendListing.BusinessLogic;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace MicroserviceBackendListing.Services
{
	[Route("api/Test")]
	public class TestController : AppController
	{
		/// <summary>
		/// Health check ping to see if API is up and responding.
		/// </summary>
		/// <response code="200">Ok, Api Happy</response>
		[AllowAnonymous]
		[HttpGet]
		[Route("Get")]
		[ProducesResponseType(200)]
		public IActionResult Get()
		{
			return Ok("Hello");
		}

	}
}
