﻿using MicroserviceContract.Dtos.Editor;
using Microsoft.Extensions.Caching.Memory;
using Redi.Prime3.MicroService.BaseLib;
using Sql;

namespace MicroserviceBackendListing.BusinessLogic
{
    public class ListingType: BusinessLogicBase
    {
        public ListingType(IUnitOfWork u, IMemoryCache memoryCache)
        {
            _unitOfWork = u;
            _memoryCache = memoryCache;
        }
        public async Task<bool> ListingTypeForTenant(int listingTypeId)
        {
            string cacheKey = "ListingTypeForTenant" + listingTypeId;

            if (_memoryCache.TryGetValue(cacheKey, out bool cacheValue))
            {
                return cacheValue;
            }

            string sql = @"
                        SELECT [IsTenantBased]                     
                        FROM [listing].[ListingType]
                        WHERE [ListingTypeId] = @ListingTypeId";
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ListingTypeId", listingTypeId);

                var result = await command.SelectSingle<bool>("IsTenantBased");

                _memoryCache.Set(cacheKey, result, CacheOptions());

                return result;
            }
        }
    }
}
