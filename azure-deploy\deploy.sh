
AZURE_ENVIRONMENT_NAME="${AZURE_CLIENT_NAME}-env"
AZURE_IDENTITY_NAME="${AZURE_CLIENT_NAME}-identity"
KEY_VAULT_NAME="${AZURE_CLIENT_NAME}-vault"
AZURE_RESOURCE_GROUP="rg-$AZURE_CLIENT_NAME"

#Run main bicep
az deployment group create --resource-group $AZURE_RESOURCE_GROUP \
--template-file azure-deploy/bicep/mainlistingservice.bicep \
--parameters dockerUserName=$DOCKERHUB_USERNAME dockerPassword=$DOCKERHUB_PASSWORD \
systemEnvironment=$SYSTEM_ENVIRONMENT \
enviornmentName=$AZURE_ENVIRONMENT_NAME \
identityName=$AZURE_IDENTITY_NAME \
imageTagVersion=$IMAGETAGVERSION \
keyVaultName=$KEY_VAULT_NAME \
dockerRepoName=$DOCKER_REPO \
secretStoreName=$SECRET_STORE_NAME \
hostName=$HOST_NAME \
azureStorageAccountName=$AZURE_ACCOUNT_STORAGE

echo 'Restart Container'
# Restart Container from latest revision
az config set extension.use_dynamic_install=yes_without_prompt
latestRevision=$(az containerapp revision list --resource-group $AZURE_RESOURCE_GROUP --subscription=$AZURE_SUBSCRIPTION_ID --name $DOCKER_REPO --query '[].name | max(@)' | tr -cd '[:alnum:]._-')
echo "Latest revision Name: $latestRevision"

if [[ -n "$latestRevision" ]]; then
    az containerapp revision restart --resource-group $AZURE_RESOURCE_GROUP --subscription=$AZURE_SUBSCRIPTION_ID --name $DOCKER_REPO --revision $latestRevision
else
    az containerapp revision restart --resource-group $AZURE_RESOURCE_GROUP --subscription=$AZURE_SUBSCRIPTION_ID --name $DOCKER_REPO
fi
echo 'Done restart'