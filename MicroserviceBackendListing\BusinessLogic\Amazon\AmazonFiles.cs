using Amazon;
using Amazon.S3;
using Amazon.S3.Model;
using Amazon.S3.Transfer;
using MicroserviceBackendListing.Interfaces;

namespace MicroserviceBackendCommon.BusinessLogic.Amazon
{
    internal class AmazonFiles : ICloudStorage
    {
        private readonly IConfiguration configuration;

        public AmazonFiles(IConfiguration configuration)
        {
            this.configuration = configuration;
        }


        public async Task<string> UploadStreamAsync(Stream dataStream, string container, string cloudFileName, bool isPublic)
        {
            string bucketName = configuration["Amazon:AmazonBucketName"] ?? "";
            string accessKey = configuration["Amazon:AmazonAccessKey"] ?? "";
            string secretKey = configuration["Amazon:AmazonSecretKey"] ?? "";
            RegionEndpoint s3EndPoint = GetEndpointForRegion(configuration["Amazon:AmazonS3EndPoint"] ?? "");
            string? newUrl = null;
            try
            {
                using (var client = new AmazonS3Client(accessKey, secretKey, s3EndPoint))
                {
                    TransferUtility s3TransferUtil = new TransferUtility(client);
                    TransferUtilityUploadRequest s3TrfrReq = new TransferUtilityUploadRequest();
                    s3TrfrReq.CannedACL = S3CannedACL.PublicRead;
                    s3TrfrReq.InputStream = dataStream;
                    s3TrfrReq.BucketName = bucketName;
                    s3TrfrReq.Key = container + "/" + cloudFileName;
                    await s3TransferUtil.UploadAsync(s3TrfrReq);
                    newUrl = "http://" +
                        bucketName +
                        ".s3-" +
                        s3EndPoint.SystemName +
                        ".amazonaws.com/" +
                        container + "/" + cloudFileName;

                    newUrl = newUrl.Replace("#", "%23");
                }
            }
            catch (Exception)
            {
                //ErrorWriter.WriteInfo("Amazon UploadStream failed" + ex.Message);
                throw;
            }
            return newUrl;
        }

        public async Task<string> UploadFileAsync(string path, string container, string cloudFileName, bool isPublic, bool deleteOncomplete = true)
        {
            string bucketName = configuration["Amazon:AmazonBucketName"] ?? "";
            string accessKey = configuration["Amazon:AmazonAccessKey"] ?? "";
            string secretKey = configuration["Amazon:AmazonSecretKey"] ?? "";
            RegionEndpoint s3EndPoint = GetEndpointForRegion(configuration["Amazon:AmazonS3EndPoint"] ?? "");
            if (cloudFileName == null)
            {
                cloudFileName = Path.GetFileName(path);
            }
            string? newUrl = null;
            try
            {
                using (var client = new AmazonS3Client(accessKey, secretKey, s3EndPoint))
                {
                    TransferUtility s3TransferUtil = new TransferUtility(client);
                    TransferUtilityUploadRequest s3TrfrReq = new TransferUtilityUploadRequest();
                    s3TrfrReq.CannedACL = S3CannedACL.PublicRead;
                    s3TrfrReq.FilePath = path;
                    s3TrfrReq.BucketName = bucketName;
                    s3TrfrReq.Key = container + "/" + cloudFileName;
                    if (isPublic == true) // isPublic => forceDownloadNotDisplay
                    {
                        // Adding attachment as the Disposition forces the browser to download the file and not attempt to display it.
                        s3TrfrReq.Metadata.Add("Content-Disposition", "attachment; filename=" + cloudFileName);
                    }
                    await s3TransferUtil.UploadAsync(s3TrfrReq);
                    newUrl = "http://" +
                        bucketName +
                        ".s3-" +
                        s3EndPoint.SystemName +
                        ".amazonaws.com/" +
                        container + "/" + cloudFileName;
                    // Delete the temporary file on the web server
                    FileInfo TheFile = new FileInfo(s3TrfrReq.FilePath);
                    if (TheFile.Exists && deleteOncomplete)
                    {
                        // File found so delete it.
                        TheFile.Delete();
                    }

                    newUrl = newUrl.Replace("#", "%23");
                }
            }
            catch (Exception)
            {
                //ErrorWriter.WriteInfo("Amazon UploadFile failed" + ex.Message);
                throw;
            }

            return newUrl;
        }

        public async Task<Uri> GetAuthenticatedUrl(string url, CloudItemPermission permissions)
        {
            string bucketName = configuration["Amazon:AmazonBucketName"] ?? "";
            string accessKey = configuration["Amazon:AmazonAccessKey"] ?? "";
            string secretKey = configuration["Amazon:AmazonSecretKey"] ?? "";
            RegionEndpoint s3EndPoint = GetEndpointForRegion(configuration["Amazon:AmazonS3EndPoint"] ?? "");

            var fileUri = new Uri(url);
            var cloudFileName = fileUri.AbsolutePath.Split('/')[fileUri.AbsolutePath.Split('/').Length - 1];

            //var s3Client = new AmazonS3Client(new SessionAWSCredentials(creds.AccessKeyId, creds.SecretAccessKey, creds.Token)))

            ResponseHeaderOverrides headerOverrides = new ResponseHeaderOverrides();
            headerOverrides.ContentDisposition = "attachment; filename=" + cloudFileName;

            int secs = 0;
            do
            {
                using (var client = new AmazonS3Client(accessKey, secretKey, s3EndPoint))
                {
                    GetPreSignedUrlRequest request = new GetPreSignedUrlRequest
                    {
                        BucketName = bucketName,
                        Key = cloudFileName.TrimStart('/'),
                        Verb = HttpVerb.GET,
                        Protocol = Protocol.HTTPS,
                        ResponseHeaderOverrides = headerOverrides,
                        Expires = DateTime.UtcNow.AddDays(7)
                    };

                    url = await client.GetPreSignedURLAsync(request);
                    secs++;
                }
            } while ((url.Contains("%2B") || url.Contains("%2b") || url.Contains("+")) && secs < 30); // try again until a signature with no + sign is generated (+ gives error with signature - fails).

            return new Uri(url);
        }

        public async Task Delete(string url)
        {
            var fileUri = new Uri(url);
            string bucketName = configuration["Amazon:AmazonBucketName"] ?? "";
            string accessKey = configuration["Amazon:AmazonAccessKey"] ?? "";
            string secretKey = configuration["Amazon:AmazonSecretKey"] ?? "";
            RegionEndpoint s3EndPoint = GetEndpointForRegion(configuration["Amazon:AmazonS3EndPoint"] ?? "");
            try
            {
                using (var client = new AmazonS3Client(accessKey, secretKey, s3EndPoint))
                {
                    await client.DeleteObjectAsync(new DeleteObjectRequest() { BucketName = bucketName, Key = fileUri.PathAndQuery });
                }
            }
            catch (Exception)
            {
                //ErrorWriter.WriteInfo("Amazon UploadFile failed" + ex.Message);
                throw;
            }
        }

        public static RegionEndpoint GetEndpointForRegion(string region)
        {
            switch (region)
            {
                case "us-east-1":
                    return RegionEndpoint.USEast1;
                case "us-west-1":
                    return RegionEndpoint.USWest1;
                case "us-west-2":
                    return RegionEndpoint.USWest2;
                case "eu-west-1":
                    return RegionEndpoint.EUWest1;
                case "eu-central-1":
                    return RegionEndpoint.EUCentral1;
                case "ap-northeast-1":
                    return RegionEndpoint.APNortheast1;
                case "ap-southeast-1":
                    return RegionEndpoint.APSoutheast1;
                case "ap-southeast-2":
                    return RegionEndpoint.APSoutheast2;
                case "sa-east-1":
                    return RegionEndpoint.SAEast1;
                default:
                    return RegionEndpoint.USEast1;
            }
        }

    }
}