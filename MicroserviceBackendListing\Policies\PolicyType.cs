using MicroserviceBackendListing.Enums;

namespace MicroserviceBackendListing.Policies
{
    public static class PolicyType
    {
        //Global Admin (catch-all for any calls to be blocked unless the user had this claim or a new claim is added for that particular call)
        public const string GlobalAdminPolicy = "GlobalAdminPolicy";

        //Manage Listing
        public const string CreateManageListingPolicy = "CreateManageListingPolicy";
        public const string EditManageListingPolicy = "EditManageListingPolicy";
        public const string DeleteManageListingPolicy = "DeleteManageListingPolicy";
        public const string ViewManageListingPolicy = "ViewManageListingPolicy";
        public const string ViewManageListingListPolicy = "ViewManageListingListPolicy";

        //View Listing
        public const string ViewListingPolicy = "ViewListingPolicy";

        //View Dashboard Listing
        public const string ViewDashboardListingPolicy = "ViewDashboardListingPolicy";

        //Edit Folder Listing
        public const string EditFolderListingPolicy = "EditFolderListingPolicy";

        //Presentation
        public const string ViewDashboardPresentationPolicy = "ViewDashboardPresentationPolicy";
        public const string ViewDashboardPresentationListPolicy = "ViewDashboardPresentationListPolicy";
        public const string UpdateDashboardPresentationPolicy = "UpdateDashboardPresentationPolicy";
        public const string CreateDashboardPresentationPolicy = "CreateDashboardPresentationPolicy";
        public const string DeleteDashboardPresentationPolicy = "DeleteDashboardPresentationPolicy";

        // Files
        public const string UploadFilePolicy = "UploadFilePolicy";

        // Note! ListingType table has a colummn NonOwnerEditClaimId which can optionally control the role required to edit a listing.

        public static List<PolicyTypeDefinition> List = new List<PolicyTypeDefinition>()
        {
            new PolicyTypeDefinition(GlobalAdminPolicy, (policy) => policy.RequireClaim("Global Admin")),

            //Manage Listing Policies
            //Purposely forced Manage Listings to only be "Edit Access" as the purpose of Managing a Listing would be to Edit
            new PolicyTypeDefinition(EditManageListingPolicy, (policy) => policy.RequireClaim("Edit Manage Listing").Requirements.Add(new ListingPermissionRequirement( roleAccesses: [ListingAccessRoleEnum.EditAccess], isManagedListing:true))),
            new PolicyTypeDefinition(DeleteManageListingPolicy, (policy) => policy.RequireClaim("Delete Manage Listing").Requirements.Add(new ListingPermissionRequirement( roleAccesses: [ListingAccessRoleEnum.EditAccess], isManagedListing:true))),
            new PolicyTypeDefinition(ViewManageListingPolicy, (policy) => policy.RequireClaim("View Manage Listing").Requirements.Add(new ListingPermissionRequirement( roleAccesses: [ListingAccessRoleEnum.EditAccess], isManagedListing:true))),
            new PolicyTypeDefinition(ViewManageListingListPolicy,  (policy) => policy.RequireClaim("View Manage Listing List")),
            new PolicyTypeDefinition(CreateManageListingPolicy, (policy) => policy.RequireClaim("Create Manage Listing")),

            //Listing SearchAndFilter Policies (No Claim permissions needed)
            new PolicyTypeDefinition(ViewListingPolicy, (policy) => policy.Requirements.Add(new ListingPermissionRequirement())),

            //Edit Folder Listing (No Claim permissions needed)
            new PolicyTypeDefinition(EditFolderListingPolicy, (policy) => policy.Requirements.Add(new ListingPermissionRequirement(listingTypeId: ListingTypeEnum.Folder))),

            //View Dashboard Listing (No Claim permissions needed)
            new PolicyTypeDefinition(ViewDashboardListingPolicy, (policy) => policy.Requirements.Add(new ListingPermissionRequirement(listingTypeId: ListingTypeEnum.Dashboard))),

            //Presentation Policies
            new PolicyTypeDefinition(ViewDashboardPresentationPolicy, (policy) => policy.RequireClaim("View Dashboard Presentation")),
            new PolicyTypeDefinition(ViewDashboardPresentationListPolicy, (policy) => policy.RequireClaim("View Dashboard Presentation List")),
            new PolicyTypeDefinition(UpdateDashboardPresentationPolicy, (policy) => policy.RequireClaim("Update Dashboard Presentation")),
            new PolicyTypeDefinition(CreateDashboardPresentationPolicy, (policy) => policy.RequireClaim("Create Dashboard Presentation")),
            new PolicyTypeDefinition(DeleteDashboardPresentationPolicy, (policy) => policy.RequireClaim("Delete Dashboard Presentation")),

            // Files
            new PolicyTypeDefinition(UploadFilePolicy, (policy) => policy.RequireClaim("Manage Listing Upload File")),
        };
    }
}
