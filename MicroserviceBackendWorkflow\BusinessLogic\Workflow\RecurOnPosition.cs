using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Collections.Specialized;

namespace MicroserviceBackendWorkflow.BusinessLogic
{
    public class RecurOnPosition : BusinessLogicBase
    {
        public RecurOnPosition(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a single RecurOnPosition by ID
        /// </summary>
        /// <param name="recurOnPositionId">The recur on position ID</param>
        /// <returns>RecurOnPositionDto or null if not found</returns>
        internal async Task<RecurOnPositionDto?> GetAsync(byte recurOnPositionId)
        {
            string sql = @"
                SELECT [RecurOnPositionId]
                      ,[Label]
                      ,[SortOrder]
                      ,[IsEnabled]
                FROM [workflow].[RecurOnPosition]
                WHERE [RecurOnPositionId] = @recurOnPositionId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("recurOnPositionId", recurOnPositionId);
                return await command.SelectSingle<RecurOnPositionDto>();
            }
        }

        /// <summary>
        /// Get a list of RecurOnPositions with filtering and paging
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="showDisabled">Include disabled records</param>
        /// <returns>List of RecurOnPositions</returns>
        internal async Task<ListResponseDto<RecurOnPositionListDto>> GetListAsync(StandardListParameters? standardListParameters = null, bool showDisabled = false)
        {
            if (standardListParameters == null)
            {
                standardListParameters = new StandardListParameters();
            }

            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "RecurOnPositionId", "[RecurOnPosition].[RecurOnPositionId]" },
                { "Label", "[RecurOnPosition].[Label]" },
                { "SortOrder", "[RecurOnPosition].[SortOrder]" },
                { "IsEnabled", "[RecurOnPosition].[IsEnabled]" }
            };

            string sql = @"
                SELECT [RecurOnPositionId]
                      ,[Label]
                      ,[SortOrder]
                      ,[IsEnabled]
                FROM [workflow].[RecurOnPosition]
                WHERE 1=1";

            if (!showDisabled)
            {
                sql += " AND [IsEnabled] = 1";
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "SortOrder");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<RecurOnPositionListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);
                result.List = await command.SelectMany<RecurOnPositionListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[RecurOnPosition]
                WHERE 1=1";

            if (!showDisabled)
            {
                countSql += " AND [IsEnabled] = 1";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }
    }
}
