﻿using MicroserviceContract.Dtos.Booking;
using MicroserviceContract.Dtos;
using Sql;
using Redi.Prime3.MicroService.BaseLib;

namespace MicroserviceBackendListing.BusinessLogic.Booking.Management
{
    public partial class BookingItem : BusinessLogicBase
    {

        internal async Task<int?> CreateBookingItemAsync(RequestCDto<BookingItemRequestCDto> requestCDto)
        {
            if (requestCDto == null || requestCDto.Data == null) { return null; }
            BookingItemRequestCDto dto = requestCDto.Data;

            var exists = dto.BookingItemId != null ? await GetBookingItemAsync((int)dto.BookingItemId) : null;
            if (exists != null)
            {
                await UpdateBookingItemAsync(requestCDto);
                return dto.BookingItemId;
            }

            // Set default values
            // ******************
            if (dto.ParentEntityId.GuidIsNullOrEmpty() && (dto.ParentEntityIntId == null || dto.ParentEntityIntId <= 0))
            {
                if (_utils.PartyId != null)
                {
                    dto.ParentEntityId = _utils.PartyId;
                    dto.ParentEntityType = "Party";
                }
                else
                {
                    dto.ParentEntityId = _utils.UserId;
                    dto.ParentEntityType = "User";
                }
            }

            if (dto.MinimumGapBetweenBookingsMinutes == null) { dto.MinimumGapBetweenBookingsMinutes = 0; }
            if (dto.MinimumBookingTimeInMinutes == null) { dto.MinimumBookingTimeInMinutes = 1; }
            if (dto.MaximumBookingTimeInMinutes == null) { dto.MaximumBookingTimeInMinutes = 60; }
            if (dto.MaximumBookingIntoFutureDays == null) { dto.MaximumBookingIntoFutureDays = 1; }
            if (dto.IsRecurringBookingEnabled == null) { dto.IsRecurringBookingEnabled = false; }
            if (dto.IsEnabled == null) { dto.IsEnabled = true; }

            await ValidateBookingItem(dto.BookingItemId, requestCDto);

            string sql = @"
INSERT INTO [booking].[BookingItem](
       [ParentEntityType]
      ,[ParentEntityId]
      ,[ParentEntityIntId]
      ,[TimeZoneIanaId]
      ,[MinimumGapBetweenBookingsMinutes]
      ,[MinimumBookingTimeInMinutes]
      ,[MaximumBookingTimeInMinutes]
      ,[MaximumBookingIntoFutureDays]
      ,[IsRecurringBookingEnabled]
      ,[IsEnabled]
      ,[Note]
      ,[PublicHolidaySetId]
      ,[CreatedOn]
      ,[CreatedByName]
      ,[Deleted]
      ,[TenantId]
      ,[HomeHoldingLocationId]
)
VALUES
(
    @ParentEntityType,
    @ParentEntityId,
    @ParentEntityIntId,
    @TimeZoneIanaId,
    @MinimumGapBetweenBookingsMinutes,
    @MinimumBookingTimeInMinutes,
    @MaximumBookingTimeInMinutes,
    @MaximumBookingIntoFutureDays,
    @IsRecurringBookingEnabled,
    @IsEnabled,
    @Note,
    @PublicHolidaySetId,
    @CreatedOn,
    @CreatedByName,
    @Deleted,
    @TenantId,
    @HomeHoldingLocationId
);";
            int bookingItemId = 0;
            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArgument("TenantId", _utils.TenantId);
                command.AddArgument("CreatedOn", DateTimeOffset.UtcNow);
                command.AddArgument("CreatedByName", _utils.UserFullName);
                command.AddArgument("Deleted", false);

                // Create
                command.AddArguments(dto);

                bookingItemId = await command.ExecuteAndReturnIdentity();

                // Log
                string description = $"New BookingItem created by {_utils.UserFullName}";
                await AddBookingItemEvent(bookingItemId, description);
            }

            await AddBookingItemHoldingLocationRecord(bookingItemId, dto.CurrentHoldingLocationId, null, null);

            return bookingItemId;
        }

        internal async Task UpdateBookingItemAsync(RequestCDto<BookingItemRequestCDto>? requestCDto)
        {
            if (requestCDto == null || requestCDto.Data == null) { return; }
            BookingItemRequestCDto dto = requestCDto.Data;

            var exists = dto.BookingItemId != null ? await GetBookingItemAsync((int)dto.BookingItemId) : null;
            if (exists == null)
            {
                await CreateBookingItemAsync(requestCDto);
                return;
            }

            // Set default values
            // ******************
            if (dto.ParentEntityId.GuidIsNullOrEmpty() && (dto.ParentEntityIntId == null || dto.ParentEntityIntId <= 0))
            {
                if (_utils.PartyId != null)
                {
                    dto.ParentEntityId = _utils.PartyId;
                    dto.ParentEntityType = "Party";
                }
                else
                {
                    dto.ParentEntityId = _utils.UserId;
                    dto.ParentEntityType = "User";
                }
            }

            if (dto.MinimumGapBetweenBookingsMinutes == null) { dto.MinimumGapBetweenBookingsMinutes = 0; }
            if (dto.MinimumBookingTimeInMinutes == null) { dto.MinimumBookingTimeInMinutes = 1; }
            if (dto.MaximumBookingTimeInMinutes == null) { dto.MaximumBookingTimeInMinutes = 60; }
            if (dto.MaximumBookingIntoFutureDays == null) { dto.MaximumBookingIntoFutureDays = 1; }
            if (dto.IsRecurringBookingEnabled == null) { dto.IsRecurringBookingEnabled = false; }
            if (dto.IsEnabled == null) { dto.IsEnabled = true; }

            (bool hasChanged, string description) = await ValidateBookingItem(dto.BookingItemId, requestCDto, exists);
            if (!hasChanged)
            {
                return;
            }

            string sql = @"
UPDATE [booking].[BookingItem]
       SET [ParentEntityType] = @ParentEntityType
      ,[ParentEntityId] = @ParentEntityId
      ,[ParentEntityIntId] = @ParentEntityIntId
      ,[TimeZoneIanaId] = @TimeZoneIanaId
      ,[MinimumGapBetweenBookingsMinutes] = @MinimumGapBetweenBookingsMinutes
      ,[MinimumBookingTimeInMinutes] = @MinimumBookingTimeInMinutes
      ,[MaximumBookingTimeInMinutes] = @MaximumBookingTimeInMinutes
      ,[MaximumBookingIntoFutureDays] = @MaximumBookingIntoFutureDays
      ,[IsRecurringBookingEnabled] = @IsRecurringBookingEnabled
      ,[IsEnabled] = @IsEnabled
      ,[Note] = @Note
      ,[PublicHolidaySetId] = @PublicHolidaySetId
      ,[ModifiedOn] = @ModifiedOn
      ,[ModifiedByName] = @ModifiedByName
      ,[HomeHoldingLocationId] = @HomeHoldingLocationId
WHERE [BookingItemId] = @BookingItemId;
";
            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {

                // Create
                command.AddArguments(dto);
                command.AddArgument("ModifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("ModifiedByName", _utils.UserFullName);

                await command.Execute();

                description = string.IsNullOrEmpty(description) ? "Updated" : description; ;
#pragma warning disable CS8629 // Nullable value type may be null.
                await AddBookingItemEvent((int)dto.BookingItemId, description);
#pragma warning restore CS8629 // Nullable value type may be null.
            }

            if (dto.CurrentHoldingLocationId != exists.CurrentHoldingLocationId)
            {
                await AddBookingItemHoldingLocationRecord((int)dto.BookingItemId, dto.CurrentHoldingLocationId);
            }
        }

        internal async Task DeleteBookingItemAsync(int bookingItemId)
        {
            var existing = await GetBookingItemAsync(bookingItemId);
            if(existing == null)
            {
                throw new ArgumentException("Booking item does not exist into the db");
            }

            string sql = @"
UPDATE [booking].[BookingItem]
SET [Deleted] = 1
    ,[ModifiedOn] = @ModifiedOn
    ,[ModifiedByName] = @ModifiedByName
WHERE [BookingItemId] = @bookingItemId";

            if(_utils.TenantId != null)
            {
                sql += " AND [TenantId] = @tenantId";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("Deleted", true);
                command.AddArgument("ModifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("ModifiedByName", _utils.UserFullName);
                command.AddArgument("bookingItemId", bookingItemId);
                command.AddArgument("tenantId", _utils.TenantId);

                await command.Execute();
            }
        }

        internal async Task UnDeleteBookingItemAsync(int bookingItemId)
        {
            var existing = await GetBookingItemAsync(bookingItemId);
            if (existing == null)
            {
                throw new ArgumentException("Booking item does not exist into the db");
            }

            string sql = @"
UPDATE [booking].[BookingItem]
SET [Deleted] = 0
    ,[ModifiedOn] = @ModifiedOn
    ,[ModifiedByName] = @ModifiedByName
WHERE [BookingItemId] = @bookingItemId";

            if (_utils.TenantId != null)
            {
                sql += " AND [TenantId] = @tenantId";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("Deleted", true);
                command.AddArgument("ModifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("ModifiedByName", _utils.UserFullName);
                command.AddArgument("bookingItemId", bookingItemId);
                command.AddArgument("tenantId", _utils.TenantId);

                await command.Execute();
            }
        }

        internal async Task<(bool, string)> ValidateBookingItem(int? BookingItemId, RequestCDto<BookingItemRequestCDto> newRequestCDto, BookingItemResponseCDto? existCDto = null)
        {
            string description = "";
            if (newRequestCDto == null || newRequestCDto.Data == null) { return (false, description); }
            // The ExcludeFields cannot be directly updated by data passed into this call.
            List<string> excludeFields = new List<string>() { "BookingItemId", "PublicHolidaySetLabel", "CreatedOn", "CreatedByName", "Deleted", "ModifiedOn", "ModifiedByName", "Statistics" };

            var dto = newRequestCDto.Data;
            bool isNew = existCDto == null;
            // Validate fields
            bool hasChanges = false;

            // Check what data has changed (determine what is to be updated in the database)
            if (isNew == false && existCDto != null)
            {
                if (newRequestCDto?.ClearFields?.ContainsKey("ParentEntityType") == true) { dto.ParentEntityType = null; hasChanges = true; }
                else { if (!string.IsNullOrEmpty(dto.ParentEntityType) && dto.ParentEntityType != existCDto.ParentEntityType) { hasChanges = true; } else { dto.ParentEntityType = existCDto.ParentEntityType; } }
                
                if (newRequestCDto?.ClearFields?.ContainsKey("ParentEntityId") == true) { dto.ParentEntityId = null; hasChanges = true; }
                else { if (dto.ParentEntityId != null && dto.ParentEntityId != existCDto.ParentEntityId) { hasChanges = true; } else { dto.ParentEntityId = existCDto.ParentEntityId; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("ParentEntityIntId") == true) { dto.ParentEntityIntId = null; hasChanges = true; }
                else { if (dto.ParentEntityIntId != null && dto.ParentEntityIntId != existCDto.ParentEntityIntId) { hasChanges = true; } else { dto.ParentEntityIntId = existCDto.ParentEntityIntId; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("TimeZoneIanaId") == true) { dto.TimeZoneIanaId = null; hasChanges = true; description += ", timezone cleared"; }
                else { if (dto.TimeZoneIanaId != null && dto.TimeZoneIanaId != existCDto.TimeZoneIanaId) { hasChanges = true; description += $", timezone updated to {dto.TimeZoneIanaId}"; } else { dto.TimeZoneIanaId = existCDto.TimeZoneIanaId; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("MinimumGapBetweenBookingsMinutes") == true) { dto.MinimumGapBetweenBookingsMinutes = null; hasChanges = true; description += ", min gap between bookings cleared"; }
                else { if (dto.MinimumGapBetweenBookingsMinutes != null && dto.MinimumGapBetweenBookingsMinutes != existCDto.MinimumGapBetweenBookingsMinutes) { hasChanges = true; description += $",  min gap between bookings updated to {dto.MinimumGapBetweenBookingsMinutes}"; } else { dto.MinimumGapBetweenBookingsMinutes = existCDto.MinimumGapBetweenBookingsMinutes; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("MinimumBookingTimeInMinutes") == true) { dto.MinimumBookingTimeInMinutes = null; hasChanges = true; description += ", min booking time cleared"; }
                else { if (dto.MinimumBookingTimeInMinutes != null && dto.MinimumBookingTimeInMinutes != existCDto.MinimumBookingTimeInMinutes) { hasChanges = true; description += $", min booking time updated to {dto.MinimumBookingTimeInMinutes}"; } else { dto.MinimumBookingTimeInMinutes = existCDto.MinimumBookingTimeInMinutes; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("MaximumBookingTimeInMinutes") == true) { dto.MaximumBookingTimeInMinutes = null; hasChanges = true; description += ", max booking time cleared"; }
                else { if (dto.MaximumBookingTimeInMinutes != null && dto.MaximumBookingTimeInMinutes != existCDto.MaximumBookingTimeInMinutes) { hasChanges = true; description += $", max booking time updated to {dto.MaximumBookingTimeInMinutes}"; } else { dto.MaximumBookingTimeInMinutes = existCDto.MaximumBookingTimeInMinutes; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("MaximumBookingIntoFutureDays") == true) { dto.MaximumBookingIntoFutureDays = null; hasChanges = true; description += ", max booking into future cleared"; }
                else { if (dto.MaximumBookingIntoFutureDays != null && dto.MaximumBookingIntoFutureDays != existCDto.MaximumBookingIntoFutureDays) { hasChanges = true; description += $", max booking into future updated to {dto.MaximumBookingIntoFutureDays}"; } else { dto.MaximumBookingIntoFutureDays = existCDto.MaximumBookingIntoFutureDays; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("IsRecurringBookingEnabled") == true) { dto.IsRecurringBookingEnabled = null; hasChanges = true; description += ", recurring booking cleared"; }
                else { if (dto.IsRecurringBookingEnabled != null && dto.IsRecurringBookingEnabled != existCDto.IsRecurringBookingEnabled) { hasChanges = true; description += $", recurring booking enabled update to {dto.IsRecurringBookingEnabled}"; } else { dto.IsRecurringBookingEnabled = existCDto.IsRecurringBookingEnabled; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("IsEnabled") == true) { dto.IsEnabled = null; hasChanges = true; description += ", enabled cleared"; }
                else { if (dto.IsEnabled != null && dto.IsEnabled != existCDto.IsEnabled) { hasChanges = true; description += $", enabled updated to {dto.IsEnabled}"; } else { dto.IsEnabled = existCDto.IsEnabled; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("Note") == true) { dto.Note = null; hasChanges = true; description += ", note cleared"; }
                else { if (!string.IsNullOrEmpty(dto.Note) && dto.Note != existCDto.Note) { hasChanges = true; description += $", note updated {dto.Note}"; } else { dto.Note = existCDto.Note; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("CurrentHoldingLocationId") == true) { dto.CurrentHoldingLocationId = null; hasChanges = true; description += ", current holding location cleared"; }
                else { if (dto.CurrentHoldingLocationId != null && dto.CurrentHoldingLocationId != existCDto.CurrentHoldingLocationId) 
                    {
                        var currentLocation = await _holdingLocation().GetHoldingLocationAsync((int)dto.CurrentHoldingLocationId);
                        hasChanges = true; 
                        description += $", current holding location updated to {currentLocation?.HoldingLocationName}"; 
                    }
                    else { dto.CurrentHoldingLocationId = existCDto.CurrentHoldingLocationId; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("HomeHoldingLocationId") == true) { dto.HomeHoldingLocationId = null; hasChanges = true; description += ", home holding location cleared"; }
                else { if (dto.HomeHoldingLocationId != null && dto.HomeHoldingLocationId != existCDto.HomeHoldingLocationId) 
                    {
                        var homeLocation = await _holdingLocation().GetHoldingLocationAsync((int)dto.HomeHoldingLocationId);
                        hasChanges = true; 
                        description += $", home holding location updated to {homeLocation?.HoldingLocationName}"; 
                    } 
                    else { dto.HomeHoldingLocationId = existCDto.HomeHoldingLocationId; } }

                if (newRequestCDto?.ClearFields?.ContainsKey("PublicHolidaySetId") == true) { dto.PublicHolidaySetId = null; hasChanges = true; description += ", public holiday set cleared"; }
                else { if (dto.PublicHolidaySetId != null && dto.PublicHolidaySetId != existCDto.PublicHolidaySetId) { hasChanges = true; description += ", public holiday set updated"; } else { dto.PublicHolidaySetId = existCDto.PublicHolidaySetId; } }
            }

            if (!hasChanges && !isNew)
            {
                // no data has changed to exit.
                return (false, description);
            }

            if (string.IsNullOrEmpty(dto.ParentEntityType)) { throw new HttpRequestException("ParentEntityType is required."); }
            if (dto.Note?.Length > 2000) { throw new HttpRequestException("Note must be less than 2,000 characters."); }
            if (dto.MinimumGapBetweenBookingsMinutes == null) { throw new HttpRequestException("MinimumGapBetweenBookingsMinutes is required."); }
            if (dto.MinimumBookingTimeInMinutes == null) { throw new HttpRequestException("MinimumBookingTimeInMinutes is required."); }
            if (dto.MaximumBookingTimeInMinutes == null) { throw new HttpRequestException("MaximumBookingTimeInMinutes is required."); }
            if (dto.MaximumBookingIntoFutureDays == null) { throw new HttpRequestException("MaximumBookingIntoFutureDays is required."); }
            if (dto.IsRecurringBookingEnabled == null) { throw new HttpRequestException("IsRecurringBookingEnabled is required."); }
            if (dto.IsEnabled == null) { throw new HttpRequestException("IsEnabled is required."); }
            

            return (true, description);
        }

        private async Task AddBookingItemHoldingLocationRecord(int bookingItemId, int? holdingLocationId, DateTimeOffset? fromDateTime = null, DateTimeOffset? toDateTime = null)
        {

            if (holdingLocationId == null) { return; }

            string sql = @"
            UPDATE [booking].[BookingItemHoldingLocation]
            SET [Deleted] = 1, [ModifiedOn] = @CreatedOn, [ModifiedByName] = @CreatedByName
            WHERE [BookingItemId] = @BookingItemId AND [Deleted] = 0 AND [FromDateTime] <= @FromDateTime AND ([ToDateTime] IS NULL OR [ToDateTime] >= @ToDateTime);
            INSERT INTO [booking].[BookingItemHoldingLocation]
                        (
                         [BookingItemId]
                        ,[FromDateTime]
                        ,[ToDateTime]
                        ,[HoldingLocationId]
                        ,[CreatedOn]
                        ,[CreatedByName]
                        ,[Deleted]
                        )
            VALUES
                        (
                         @BookingItemId
                        ,@FromDateTime
                        ,@ToDateTime
                        ,@HoldingLocationId
                        ,@CreatedOn
                        ,@CreatedByName
                        ,@Deleted
                        )";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArgument("BookingItemId", bookingItemId);
                command.AddArgument("FromDateTime", fromDateTime ?? DateTimeOffset.UtcNow);
                command.AddArgument("ToDateTime", toDateTime);
                command.AddArgument("HoldingLocationId", bookingItemId);
                command.AddArgument("CreatedOn", DateTimeOffset.UtcNow);
                command.AddArgument("CreatedByName", _utils.UserFullName);
                command.AddArgument("Deleted", 0);

                await command.Execute();
            }
        }

        private async Task AddBookingItemEvent(int bookingItemId, string description)
        {
            EventCDto dto = new EventCDto()
            {
                EventId = 0,
                ParentEntityIntId = bookingItemId,
                ParentEntityType = ENTITY_TYPE,
                Description = (description.Length > 4000 ? description.Substring(0, 4000) : description),
                CreatedOn = DateTimeOffset.UtcNow,
                CreatedByName = _utils.UserFullName
            };

            string sql = @"
            INSERT INTO [common].[Event]
                        (
                         [ParentEntityIntId]
                        ,[ParentEntityType]
                        ,[Description]
                        ,[CreatedOn]
                        ,[CreatedByName]
                        )
            VALUES
                        (
                         @ParentEntityIntId
                        ,@ParentEntityType
                        ,@Description
                        ,@CreatedOn
                        ,@CreatedByName
                        )";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArguments(dto);

                await command.Execute();
            }
        }
    }
}
