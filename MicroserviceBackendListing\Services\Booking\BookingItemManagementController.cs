﻿using MicroserviceBackendListing.BusinessLogic.Booking.Management;
using Sql;
using Microsoft.AspNetCore.Mvc;
using MicroserviceContract.Dtos.Booking;
using MicroserviceContract.Dtos;
using MicroserviceBackendListing.BusinessLogic.Booking;
using Redi.Prime3.MicroService.BaseLib;
using MicroserviceBackendListing.Enums;
using MicroserviceContract.Dtos.Listing;
using MicroserviceContract.Dtos.ListingManagement;
using MicroserviceBackendListing.Policies;
using Microsoft.AspNetCore.Authorization;

namespace MicroserviceBackendListing.Services.Booking
{
    [Route("api/BookingItemManagement")]
    public class BookingItemManagementController : AppController
    {
        private readonly BookingItem _bookingItem;
        private readonly BookingItemAvailability _bookingItemAvailability;
        private readonly BookingItemManagement _bookingItemManagement;
        public BookingItemManagementController(BookingItem bookingItem, IUnitOfWork unitOfWork, BookingItemAvailability bookingItemAvailability, BookingItemManagement bookingItemManagement)
        {
            _bookingItem = bookingItem;
            _unitOfWork = unitOfWork;
            _bookingItemAvailability = bookingItemAvailability;
            _bookingItemManagement = bookingItemManagement;
        }

        [HttpGet]
        [Route("GetBookingItem")]
        [ProducesResponseType(typeof(BookingItemResponseCDto), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetBookingItemAsync([FromQuery] int bookingItemId)
        {
            if (bookingItemId <= 0) { throw new HttpRequestException("parameter bookingItemId is required", null, System.Net.HttpStatusCode.BadRequest); }

            var result = await _bookingItem.GetBookingItemAsync(bookingItemId);
            return Ok(result);
        }

        [HttpPost]
        [Route("CreateBookingItem")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        public async Task<IActionResult> CreateBookingItemAsync([FromBody] RequestCDto<BookingItemRequestCDto> dto)
        {
            var result = await _bookingItem.CreateBookingItemAsync(dto);
            _unitOfWork.Commit();
            return Ok(result);
        }

        [HttpPost]
        [Route("UpdateBookingItem")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        public async Task<IActionResult> UpdateBookingItemAsync([FromBody] RequestCDto<BookingItemRequestCDto> dto)
        {
            await _bookingItem.UpdateBookingItemAsync(dto);
            _unitOfWork.Commit();
            return Ok();
        }

        [HttpPost]
        [Route("DeleteBookingItem")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        public async Task<IActionResult> DeleteBookingItemAsync(int bookingItemId)
        {
            if (bookingItemId <= 0) { throw new HttpRequestException("parameter bookingItemId is required", null, System.Net.HttpStatusCode.BadRequest); }
            await _bookingItem.DeleteBookingItemAsync(bookingItemId);
            _unitOfWork.Commit();
            return Ok();
        }

        [HttpPost]
        [Route("UnDeleteBookingItem")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        public async Task<IActionResult> UnDeleteBookingItemAsync(int bookingItemId)
        {
            if (bookingItemId <= 0) { throw new HttpRequestException("parameter bookingItemId is required", null, System.Net.HttpStatusCode.BadRequest); }
            await _bookingItem.UnDeleteBookingItemAsync(bookingItemId);
            _unitOfWork.Commit();
            return Ok();
        }

        // Availability

        [HttpGet]
        [Route("GetBookingItemAvailability")]
        [ProducesResponseType(typeof(BookingItemAvailableResponseCDto), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetBookingItemAvailability([FromQuery] int bookingItemAvailabilityId)
        {
            if (bookingItemAvailabilityId <= 0) { throw new HttpRequestException("parameter bookingItemAvailabilityId is required", null, System.Net.HttpStatusCode.BadRequest); }

            var result = await _bookingItemAvailability.GetBookingItemAvailabilityAsync(bookingItemAvailabilityId);
            return Ok(result);
        }

        /// <summary>
        /// Returns a list of booking items
        /// </summary>
        /// <param name="standardListParameters"></param>
        /// <param name="query"></param>
        /// <param name="timeZoneIanaIds"></param>
        /// <param name="minimumGapBetweenBookingsMinutes"></param>
        /// <param name="maximumGapBetweenBookingsMinutes"></param>
        /// <param name="minimumBookingTimeInMinutes"></param>
        /// <param name="maximumBookingTimeInMinutes"></param>
        /// <param name="minimumBookingIntoFutureDays"></param>
        /// <param name="maximumBookingIntoFutureDays"></param>
        /// <param name="isRecurringBookingEnabled"></param>
        /// <param name="publicHolidaySetId"></param>
        /// <param name="parentEntityIds"></param>
        /// <param name="parentEntityIntIds"></param>
        /// <param name="parentEntityType"></param>
        /// <param name="isEnabled"></param>
        /// <param name="createdOnDateFrom"></param>
        /// <param name="createdOnDateTo"></param>
        /// <param name="modifiedOnDateFrom"></param>
        /// <param name="modifiedOnDateTo"></param>
        /// <param name="createdByName"></param>
        /// <param name="modifiedByName"></param>
        /// <param name="statisticFields"></param>
        /// <param name="tenantId"></param>
        /// <param name="returnNotesCount"></param>
        /// <param name="holdingLocationIds"></param>
        /// <param name="homeHoldingLocationIds"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("ListBookingItems")]
        [ProducesResponseType(typeof(ListResponseDto<BookingItemResponseCDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> ListAsync(
            [FromQuery] StandardListParameters standardListParameters,
            [FromQuery] string? query = null,
            [FromQuery] string? timeZoneIanaIds = null,
            [FromQuery] Int16? minimumGapBetweenBookingsMinutes = null,
            [FromQuery] Int16? maximumGapBetweenBookingsMinutes = null,
            [FromQuery] Int16? minimumBookingTimeInMinutes = null,
            [FromQuery] Int16? maximumBookingTimeInMinutes = null,
            [FromQuery] Int16? minimumBookingIntoFutureDays = null,
            [FromQuery] Int16? maximumBookingIntoFutureDays = null,
            [FromQuery] bool? isRecurringBookingEnabled = null,
            [FromQuery] int? publicHolidaySetId = null,
            [FromQuery] string? parentEntityIds = null,
            [FromQuery] string? parentEntityIntIds = null,
            [FromQuery] string? parentEntityType = null,
            [FromQuery] bool? isEnabled = false,
            [FromQuery] string? holdingLocationIds = null,
            [FromQuery] string? homeHoldingLocationIds = null,
            [FromQuery] DateTimeOffset? createdOnDateFrom = null,
            [FromQuery] DateTimeOffset? createdOnDateTo = null,
            [FromQuery] DateTimeOffset? modifiedOnDateFrom = null,
            [FromQuery] DateTimeOffset? modifiedOnDateTo = null,
            [FromQuery] string? createdByName = null,
            [FromQuery] string? modifiedByName = null,
            [FromQuery] string? statisticFields = null,
            [FromQuery] int? tenantId = null,
            [FromQuery] bool? returnNotesCount = false)
        {
            var logic = _bookingItem;
            List<Guid>? ParentEntityIdsList = null;
            if (!string.IsNullOrEmpty(parentEntityIds))
            {
                ParentEntityIdsList = parentEntityIds.Split(',').Select(x => Guid.Parse(x)).ToList();
            }
            List<long>? ParentEntityIntIdsList = null;
            if (!string.IsNullOrEmpty(parentEntityIntIds))
            {
                ParentEntityIntIdsList = parentEntityIntIds.Split(',').Select(x => long.Parse(x)).ToList();
            }
            List<string>? timeZoneIanaIdsList = null;
            if (!string.IsNullOrEmpty(timeZoneIanaIds))
            {
                timeZoneIanaIdsList = timeZoneIanaIds.Split(',').Select(x => x).ToList();
            }
            List<int>? HoldingLocationIdsList = null;
            if (!string.IsNullOrEmpty(holdingLocationIds))
            {
                HoldingLocationIdsList = holdingLocationIds.Split(',').Select(x => int.Parse(x)).ToList();
            }
            List<int>? HomeHoldingLocationIdsList = null;
            if (!string.IsNullOrEmpty(homeHoldingLocationIds))
            {
                HomeHoldingLocationIdsList = homeHoldingLocationIds.Split(',').Select(x => int.Parse(x)).ToList();
            }
            var result = await logic.ListBookingItemsAsync(standardListParameters,
                query,
                timeZoneIanaIdsList,
                minimumGapBetweenBookingsMinutes,
                maximumGapBetweenBookingsMinutes,
                minimumBookingTimeInMinutes,
                maximumBookingTimeInMinutes,
                minimumBookingIntoFutureDays,
                maximumBookingIntoFutureDays,
                isRecurringBookingEnabled,
                publicHolidaySetId,
                ParentEntityIdsList,
                ParentEntityIntIdsList,
                parentEntityType,
                isEnabled,
                createdOnDateFrom,
                createdOnDateTo,
                modifiedOnDateFrom,
                modifiedOnDateTo,
                createdByName,
                modifiedByName,
                statisticFields,
                tenantId,
                returnNotesCount,
                HoldingLocationIdsList,
                HomeHoldingLocationIdsList);

            return Ok(result);
        }

        [HttpPost]
        [Route("CreateBookingItemAvailability")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        public async Task<IActionResult> CreateBookingItemAvailabilityAsync([FromBody] RequestCDto<BookingItemAvailableRequestCDto> dto)
        {
            var result = await _bookingItemAvailability.CreateBookingItemAvailabilityAsync(dto);
            _unitOfWork.Commit();
            return Ok(result);
        }

        [HttpPost]
        [Route("UpdateBookingItemAvailability")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        public async Task<IActionResult> UpdateBookingItemAvailabilityAsync([FromBody] RequestCDto<BookingItemAvailableRequestCDto> dto)
        {
            await _bookingItemAvailability.UpdateBookingItemAvailabilityAsync(dto);
            _unitOfWork.Commit();
            return Ok();
        }

        [HttpPost]
        [Route("DeleteBookingItemAvailability")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        public async Task<IActionResult> DeleteBookingItemAvailabilityAsync(int bookingItemAvailabilityId)
        {
            if (bookingItemAvailabilityId <= 0) { throw new HttpRequestException("parameter bookingItemAvailabilityId is required", null, System.Net.HttpStatusCode.BadRequest); }
            await _bookingItemAvailability.DeleteBookingItemAvailabilityAsync(bookingItemAvailabilityId);
            _unitOfWork.Commit();
            return Ok();
        }

        [HttpPost]
        [Route("UnDeleteBookingItemAvailability")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        public async Task<IActionResult> UnDeleteBookingItemAvailabilityAsync(int bookingItemAvailabilityId)
        {
            if (bookingItemAvailabilityId <= 0) { throw new HttpRequestException("parameter bookingItemAvailabilityId is required", null, System.Net.HttpStatusCode.BadRequest); }
            await _bookingItemAvailability.UnDeleteBookingItemAvailabilityAsync(bookingItemAvailabilityId);
            _unitOfWork.Commit();
            return Ok();
        }

        [HttpGet]
        [Route("ListBookingItemAvailability")]
        [ProducesResponseType(typeof(ListResponseDto<BookingItemAvailableResponseCDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> ListBookingItemAvailabilityAsync(
            [FromQuery] StandardListParameters standardListParameters,
            [FromQuery] string? bookingItemIds = null,
            //[FromQuery] int? availabilityModeId = BookingItemAvailabilityModeEnum.Available,
            [FromQuery] DateOnly? fromDateMin = null,
            [FromQuery] DateOnly? fromDateMax = null,
            [FromQuery] DateOnly? toDateMin = null,
            [FromQuery] DateOnly? toDateMax = null,
            [FromQuery] TimeOnly? fromTimeMin = null,
            [FromQuery] TimeOnly? fromTimeMax = null,
            [FromQuery] TimeOnly? toTimeMin = null,
            [FromQuery] TimeOnly? toTimeMax = null,
            [FromQuery] bool? includeMonday = null,
            [FromQuery] bool? includeTuesday = null,
            [FromQuery] bool? includeWednesday = null,
            [FromQuery] bool? includeThursday = null,
            [FromQuery] bool? includeFriday = null,
            [FromQuery] bool? includeSaturday = null,
            [FromQuery] bool? includeSunday = null,
            [FromQuery] bool? includePublicHolidays = null,
            [FromQuery] DateTimeOffset? createdOnDateFrom = null,
            [FromQuery] DateTimeOffset? createdOnDateTo = null,
            [FromQuery] DateTimeOffset? modifiedOnDateFrom = null,
            [FromQuery] DateTimeOffset? modifiedOnDateTo = null,
            [FromQuery] string? createdByName = null,
            [FromQuery] string? modifiedByName = null
            )
        {
            List<long>? BookingItemIdsList = null;
            if (!string.IsNullOrEmpty(bookingItemIds))
            {
                BookingItemIdsList = bookingItemIds.Split(',').Select(x => long.Parse(x)).ToList();
            }
            var result = await _bookingItemAvailability.ListBookingItemAvailabilityAsync(standardListParameters,
            BookingItemIdsList,
            fromDateMin,
            fromDateMax,
            toDateMin,
            toDateMax,
            fromTimeMin,
            fromTimeMax,
            toTimeMin,
            toTimeMax,
            includeMonday,
            includeTuesday,
            includeWednesday,
            includeThursday,
            includeFriday,
            includeSaturday,
            includeSunday,
            includePublicHolidays,
            createdOnDateFrom,
            createdOnDateTo,
            modifiedOnDateFrom,
            modifiedOnDateTo,
            createdByName,
            modifiedByName);

            return Ok(result);
        }

        [HttpPost]
        [Route("CreateListingAndBookingItem")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        [ProducesResponseType(typeof(long), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> CreateListingAndBookingItemPair([FromBody] ManageListingWithDisplayGroupedDataAndMediaCDto dto, [FromQuery] bool? supportOfflineReferenceCreate = false)
        {
            var logic = _bookingItemManagement;

            long listingId = await logic.CreateListingAndBookingItemPair(dto, supportOfflineReferenceCreate);

            _unitOfWork.Commit();
            return Ok(listingId);
        }

        [HttpPost]
        [Route("UpdateBookingItemAndSettings")]
        [Authorize(Policy = PolicyType.EditManageListingPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> UpdateBookingItemAndSettings([FromBody] BookingItemAndSettingsCDto dto)
        {
            var logic = _bookingItemManagement;

            await logic.UpdateBookingItemAndSettingsAsync(dto);

            _unitOfWork.Commit();
            return Ok();
        }
    }
}
