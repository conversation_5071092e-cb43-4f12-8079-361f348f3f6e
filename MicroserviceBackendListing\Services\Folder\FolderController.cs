﻿using MicroserviceBackendListing.BusinessLogic;
using MicroserviceBackendListing.Policies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sql;

namespace MicroserviceBackendListing.Services
{
    /// <summary>
    /// Allows Creation, Fetching and Management of Dashboards and Folders.
    /// For general searching and public facing requests use the Dashboard endpoint.
    /// </summary>
    [Route("api/Folder")]
    public class FolderController : AppController
    {
        private Func<Folder> _folderFactory;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="unitOfWork"></param>
        /// <param name="folderFactory"></param>
        public FolderController(IUnitOfWork unitOfWork,Func<Folder> folderFactory)
        {
            _unitOfWork = unitOfWork;
            _folderFactory = folderFactory;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="listingIds"></param>
        /// <param name="parentListingId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("BulkAddListings")]
        [Authorize(Policy = PolicyType.EditFolderListingPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> BulkAddListings([FromQuery] List<long> listingIds, [FromQuery] long parentListingId)
        {
            await _folderFactory().BulkAddListingsAsync(listingIds, parentListingId);
            _unitOfWork.Commit();

            return Ok();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="listingIds"></param>
        /// <param name="parentListingId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("BulkRemoveListings")]
        [Authorize(Policy = PolicyType.EditFolderListingPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> BulkRemoveListings([FromQuery] List<long> listingIds, [FromQuery] long parentListingId)
        {
            await _folderFactory().BulkRemoveListingsAsync(listingIds, parentListingId);
            _unitOfWork.Commit();

            return Ok();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="listingId"></param>
        /// <param name="subject"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("FolderRename")]
        [Authorize(Policy = PolicyType.EditFolderListingPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> FolderRename([FromQuery] long listingId, [FromQuery] string subject)
        {
            await _folderFactory().FolderRenameAsync(listingId, subject);
            _unitOfWork.Commit();

            return Ok();
        }


        /// <summary>
        /// Delete a listing.\
        /// Listing is logically deleted
        /// </summary>
        /// <param name="ListingId">The ListingId to be deleted.</param>
        /// <response code="200">Operation successful</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("DeleteListing")]
        [Authorize(Policy = PolicyType.EditFolderListingPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> DeleteListingAsync([FromQuery] long ListingId)
        {
            var list = _folderFactory();
            await list.DeleteAsync(ListingId);
            _unitOfWork.Commit();
            return Ok();
        }
    }
}
