using Redi.Prime3.MicroService.BaseLib;

namespace MicroserviceBackendListing.Constants
{
    /// <summary>
    /// This class holds the App ID's for microservices used in this application
    /// It extends the MicroserviceConstantsBase class to inherit the App ID's for common microservices
    /// </summary>
    public class MicroserviceConstants : MicroserviceConstantsBase
    {
        /// <summary>
        /// User Microservice App ID
        /// </summary>
        public static readonly string AppId = "redi-microservice-listing";

        /// <summary>
        /// Microservice Version
        /// major . minor . bug
        /// </summary>
        public static readonly string Version = "0.9.0";
    }
}