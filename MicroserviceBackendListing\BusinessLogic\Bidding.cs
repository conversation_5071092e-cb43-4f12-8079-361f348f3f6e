﻿using Google.Protobuf.WellKnownTypes;
using MicroserviceBackendListing.Dtos;
using MicroserviceContract.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Newtonsoft.Json;
using Sql;
using System.Collections.Specialized;
using Microsoft.Data.SqlClient;

namespace MicroserviceBackendListing.BusinessLogic
{
    public class Bidding : BusinessLogicBase
    {
        public Bidding(IUnitOfWork u)
        {
            _unitOfWork = u;
        }

        internal async Task<PreviouslyBidCDto> GetPreviouslyBidStatus(long listingId, Guid? partyId)
        {
            if(partyId == null)
            {
                var result = new PreviouslyBidCDto()
                {
                    ListingId = listingId,
                    HasPreviousBid = false
                };
                return result;
            }

            var sql = @"
SELECT 'true'
FROM [listing].[BidTrack] BT
WHERE BT.[ListingId] = @listingId AND
BT.[BidPartyId] = @partyId;
";
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("listingId", listingId);
                command.AddArgument("partyId", partyId);

                int count = 0;
                using (var reader = await command.SelectRaw())
                {
                    while (reader.Read())
                    {
                        count++;
                        break;
                    }
                }

                bool hasPreviousBid = false;
                if(count > 0)
                {
                    hasPreviousBid = true;
                }
                var result = new PreviouslyBidCDto()
                {
                    ListingId = listingId,
                    HasPreviousBid = hasPreviousBid
                };
                return result;
            }
        }

        /// <summary>
        /// Get a Single Attribute with an AttributeCode
        /// </summary>
        /// <param name="listingId"></param>
        /// <returns></returns>
        internal async Task<BiddingCDto> GetCurrentBiddingInformation(long listingId)
        {
            string sql = @"
SELECT LA1.[ValueNumeric] as 'BidCurrentAmount'
, LA2.[ValueString] as 'BidLeadingPartyAlias'
, LA3.[ValueString] as 'AuctionReserveStatus'
, LA4.[ValueNumeric] as 'BidTotalsBidsCount'
, LA5.[ValueDateTime] as 'BidEndDate'
, LA6.[ValueNumeric] as 'AuctionStartingAmount'
, LA7.[ValueString] as 'AuctionStatus'
FROM [listing].[Listing] L
LEFT JOIN [listing].[ListingAttribute] LA1 ON L.[ListingId] = LA1.[ListingId] AND LA1.[AttributeCode] = @bidCurrentAmountCode
LEFT JOIN [listing].[ListingAttribute] LA2 ON L.[ListingId] = LA2.[ListingId] AND LA2.[AttributeCode] = @bidLeadingPartyAliasCode
LEFT JOIN [listing].[ListingAttribute] LA3 ON L.[ListingId] = LA3.[ListingId] AND LA3.[AttributeCode] = @auctionReserveStatusCode
LEFT JOIN [listing].[ListingAttribute] LA4 ON L.[ListingId] = LA4.[ListingId] AND LA4.[AttributeCode] = @bidTotalsBidsCountCode
LEFT JOIN [listing].[ListingAttribute] LA5 ON L.[ListingId] = LA5.[ListingId] AND LA5.[AttributeCode] = @bidEndDateCode
LEFT JOIN [listing].[ListingAttribute] LA6 ON L.[ListingId] = LA6.[ListingId] AND LA6.[AttributeCode] = @auctionStartAmountCode
LEFT JOIN [listing].[ListingAttribute] LA7 ON L.[ListingId] = LA7.[ListingId] AND LA7.[AttributeCode] = @auctionStatusCode
WHERE L.[ListingId] = @listingId
";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("listingId", listingId);
                command.AddArgument("bidCurrentAmountCode", "BidCurrentAmount");
                command.AddArgument("bidLeadingPartyAliasCode", "BidLeadingPartyAlias");
                command.AddArgument("auctionReserveStatusCode", "AuctionReserveStatus");
                command.AddArgument("bidTotalsBidsCountCode", "BidTotalBidsCount");
                command.AddArgument("bidEndDateCode", "BidEndDate");
                command.AddArgument("auctionStartAmountCode", "AuctionStartingAmount");
                command.AddArgument("auctionStatusCode", "AuctionStatus");

                using (var reader = await command.SelectRaw())
                {
                    while (reader.Read())
                    {
                        decimal? bidCurrentAmount = reader.IsDBNull(0) ? null : reader.GetDecimal(0);
                        string? bidLeadingPartyAlias = reader.IsDBNull(1) ? null : reader.GetString(1);
                        string? auctionReserveStatus = reader.IsDBNull(2) ? null : reader.GetString(2);
                        decimal? bidTotalsBidCount = reader.IsDBNull(3) ? null : reader.GetDecimal(3);
                        DateTimeOffset? bidEndDate = reader.IsDBNull(4) ? null : reader.GetDateTimeOffset(4);
                        decimal? auctionStartingAmount = reader.IsDBNull(5) ? null : reader.GetDecimal(5);
                        string? auctionStatus = reader.IsDBNull(6) ? null : reader.GetString(6);

                        var result = new BiddingCDto()
                        {
                            ListingId = listingId,
                            CurrentBidAmount = bidCurrentAmount,
                            LeadingBidPartyAlias = bidLeadingPartyAlias,
                            AuctionReserveStatus = auctionReserveStatus,
                            CurrentBidCount = bidTotalsBidCount,
                            AuctionEndDate = bidEndDate,
                            AuctionStatus = auctionStatus,
                            AuctionStartingAmount = auctionStartingAmount,
                            TimeRemainingSecs = GetRemainingTimeInSecs(bidEndDate)
                        };
                        return result;
                    }
                }
            }

            return null;
        }

        private static long GetRemainingTimeInSecs(DateTimeOffset? auctionEndDate)
        {
            if (!auctionEndDate.HasValue)
            {
                return 0;
            }

            var difference = auctionEndDate.Value.Subtract(DateTimeOffset.Now);
            return (long)difference.TotalSeconds;
        }
    }
}
