﻿using MicroserviceBackendListing.BusinessLogic;
using MicroserviceBackendListing.Enums;
using Sql;

namespace MicroserviceBackendListing.Dtos
{
    public class ListingAccessRequestDto
    {
        public long? ListingId { get; set; }
        public string? ReferenceNo { get; set; }
        /// <summary>
        /// Expected Listing Type Id when checking access. Very rarely, some Listings can be managed without checking the ListingAccess table, such as Folders.
        /// e.g. EditFolderListingPolicy will have a requirement to check the Listing Type Id matches the Listing Id/Reference No passed in.
        /// </summary>
        public ListingTypeEnum? ListingTypeId { get; set; }
        public bool IsManagedListing { get; set; }
        public List<ListingAccessRoleEnum>? RoleAccesses { get; set; }
    }
}