﻿using MicroserviceBackendListing.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;

namespace MicroserviceBackendListing.BusinessLogic
{
    public class AttributeInputType : BusinessLogicBase
    {
        public AttributeInputType(IUnitOfWork u) {
            _unitOfWork = u;
        }
        public async Task<bool> TypeIsEnable(string InputTypeCode)
        {
            string sql = @"
                        SELECT  
                        [AttributeInputType].[InputTypeCode],                        
                        [AttributeInputType].[IsEnabled]
                        FROM [listing].[AttributeInputType][AttributeInputType]
                        WHERE [AttributeInputType].[InputTypeCode] = @InputTypeCode";
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("InputTypeCode", InputTypeCode);

                var result = await command.SelectSingle<AttributeInputTypeDto>();
                if (result == null)
                    return false;
                return result.IsEnabled;
            }
        }

    }
}
