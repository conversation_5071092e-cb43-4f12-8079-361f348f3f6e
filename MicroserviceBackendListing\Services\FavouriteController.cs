﻿using Microsoft.AspNetCore.Mvc;
using MicroserviceBackendListing.BusinessLogic;
using MicroserviceContract.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;

namespace MicroserviceBackendListing.Services
{
    /// <summary>
    /// NOTE! Favourites requires the PartyId to be included in the JWT for the user.
    /// </summary>
    [Route("api/Favourite")]
    public class FavouriteController : AppController
    {
        private readonly Favourite _favourite;
        private UtilityFunctions _utils;

        public FavouriteController(Favourite favourite, IUnitOfWork unitOfWork, UtilityFunctions utils)
        {
            _favourite = favourite;
            _unitOfWork = unitOfWork;
            _utils = utils;
        }

        /// <summary>
        /// Get a single favourite
        /// </summary>
        /// <param name="favouriteId"></param>
        /// <response code="200">Favourite returned, or empty favourite if not found</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("Get")]
        [ProducesResponseType(typeof(GetFavouriteCDto), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetAsync(int favouriteId)
        {
            var logic = _favourite;
            var result = await logic.GetAsync(favouriteId);

            return Ok(result);
        }

        /// <summary>
        /// Gets a list of favourites with optional parameters for favouriteSetId, listingId.
        /// </summary>
        /// <param name="standardListParameters"></param>
        /// <param name="favouriteSetId"></param>
        /// <param name="referenceNo"></param>
        /// <response code="200">Favourite list response returned, or empty favourite list response if not found</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("List")]
        [ProducesResponseType(typeof(ListResponseDto<GetFavouriteCDto>), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> ListAsync([FromQuery] StandardListParameters standardListParameters, int? favouriteSetId = null, string? referenceNo = null)
        {
            var logic = _favourite;
            var parentEntityId = _utils.PartyId;
            var result = await logic.ListAsync(standardListParameters, favouriteSetId, referenceNo, parentEntityId);

            return Ok(result);
        }

        /// <summary>
        /// Get the user's list of favourite tagged entries. Optional parameters for SetId allows grouping favourites into sets for a user.
        /// </summary>
        /// <param name="standardListParameters"></param>
        /// <param name="setId"></param>
        /// <response code="200">Favourite list response returned, or empty favourite list response if not found</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("GetList")]
        [ProducesResponseType(typeof(ListResponseDto<GetFavouriteCDto>), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetListAsync([FromQuery] StandardListParameters standardListParameters, int? setId)
        {
            var logic = _favourite;
            var parentEntityId = _utils.PartyId;
            if (parentEntityId == null) { return Ok(); }
            var result = await logic.GetListAsync(standardListParameters, (Guid)parentEntityId, setId);

            return Ok(result);
        }

        /// <summary>
        /// Add a Listing to the user's favourite listing to the user's favourites. Optional SetId allows grouping favourites into sets for a user.
        /// </summary>
        /// <param name="referenceNo"></param>
        /// <param name="sortOrder"></param>
        /// <param name="setId"></param>
        /// <response code="200">Favourite returned</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("Add")]
        [ProducesResponseType(typeof(GetFavouriteCDto), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> AddAsync(string referenceNo, int sortOrder, int? setId = null)
        {
            var logic = _favourite;
            var parentEntityId = _utils.PartyId;
            if (parentEntityId == null) { return Ok(); }
            string parentEntityType = "Party";
            var result = await logic.AddAsync(referenceNo, (Guid)parentEntityId, parentEntityType, sortOrder, setId); 
            _unitOfWork.Commit();

            return Ok(result);
        }

        /// <summary>
        /// Remove a listing from a user's favourites
        /// </summary>
        /// <param name="referenceNo"></param>
        /// <response code="200">Operation successful</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("Remove")]
        [ProducesResponseType(200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> RemoveAsync(string referenceNo)
        {
            var logic = _favourite;
            var parentEntityId = _utils.PartyId;
            if (parentEntityId == null) { return Ok(); }
            await logic.RemoveAsync(referenceNo, (Guid)parentEntityId);
            _unitOfWork.Commit();

            return Ok();
        }

        /// <summary>
        /// Create a new Favourite set for a user and fix deletion
        /// </summary>
        /// <param name="dto"></param>
        /// <response code="200">Favourite Set returned</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("AddSet")]
        [ProducesResponseType(typeof(GetFavouriteSetCDto), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> AddSetAsync([FromBody] GetFavouriteSetCDto dto)
        {
            var logic = _favourite;
            var parentEntityId = _utils.PartyId;
            if (parentEntityId == null) { return Ok(); }
            dto.ParentEntityId = (Guid)parentEntityId;
            dto.ParentEntityType = "Party";
            var result = await logic.AddSetAsync(dto);
            _unitOfWork.Commit();

            return Ok(result);
        }

        /// <summary>
        /// Delete a set
        /// </summary>
        /// <param name="setId"></param>
        /// <response code="200">Operation successful</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("DeleteSet")]
        [ProducesResponseType(200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> DeleteSetAsync(int setId)
        {
            var logic = _favourite;
            await logic.DeleteSetAsync(setId);
            _unitOfWork.Commit();

            return Ok();
        }

        /// <summary>
        /// Update the name of a Favourite Set
        /// </summary>
        /// <param name="dto"></param>
        /// <response code="200">Operation successful</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("UpdateSet")]
        [ProducesResponseType(200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> UpdateSetAsync([FromBody] GetFavouriteSetCDto dto)
        {
            var logic = _favourite;
            await logic.UpdateSetAsync(dto);
            _unitOfWork.Commit();

            return Ok();
        }

        /// <summary>
        /// Get a list of Favourite Sets for a user
        /// </summary>
        /// <response code="200">Favourite list response returned, or empty favourite list response if not found</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("GetSetList")]
        [ProducesResponseType(typeof(ListResponseDto<GetFavouriteCDto>), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> SetListAsync()
        {
            var logic = _favourite;
            var parentEntityId = _utils.PartyId;
            if (parentEntityId == null) { return Ok(); }
            var result = await logic.GetSetListsAsync((Guid)parentEntityId);

            return Ok(result);
        }

        /// <summary>
        /// Add a private note to a listing favourited by a user. Only the user can see the note, or someone they have shared a favourite with.
        /// </summary>
        /// <param name="favouriteId"></param>
        /// <param name="note"></param>
        /// <response code="200">Operation successful</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("AddListingNote")]
        [ProducesResponseType(200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> AddListingNoteAsync([FromQuery] int favouriteId, string note)
        {
            var logic = _favourite;
            await logic.AddListingNoteAsync(favouriteId, note);
            _unitOfWork.Commit();

            return Ok();
        }

        /// <summary>
        /// Update a note on a listing favourited by a user.
        /// </summary>
        /// <param name="favouriteId"></param>
        /// <param name="note"></param>
        /// <response code="200">Operation successful</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("UpdateListingNote")]
        [ProducesResponseType(200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> UpdateListingNoteAsync([FromQuery] int favouriteId, string note)
        {
            var logic = _favourite;
            await logic.UpdateListingNoteAsync(favouriteId, note);
            _unitOfWork.Commit();

            return Ok();
        }

        /// <summary>
        /// Delete a note on a listing favourited by a user.
        /// </summary>
        /// <param name="favouriteId"></param>
        /// <response code="200">Operation successful</response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("DeleteListingNote")]
        [ProducesResponseType(200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> DeleteListingNoteAsync([FromQuery] int favouriteId)
        {
            var logic = _favourite;
            await logic.DeleteListingNoteAsync(favouriteId);
            _unitOfWork.Commit();

            return Ok();
        }
    }
}
