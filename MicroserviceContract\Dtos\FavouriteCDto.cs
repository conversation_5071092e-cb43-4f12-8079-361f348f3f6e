﻿using Sql;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MicroserviceContract.Dtos
{
    [Mappable(nameof(FavouriteId))]
    public class FavouriteCDto
    {
        public int FavouriteId { get; set; }
        public int? FavouriteSetId { get; set; }
        public string? ReferenceNo { get; set; }
        public Guid ParentEntityId { get; set; }
        public string? ParentEntityType { get; set; }
        public string? Note { get; set; }
        public int SortOrder { get; set; }
    }

    public class GetFavouriteCDto : FavouriteCDto
    {

    }
}
