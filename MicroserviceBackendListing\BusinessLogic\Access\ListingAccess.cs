﻿using Microsoft.Extensions.Caching.Memory;
using Sql;
using Redi.Prime3.MicroService.BaseLib;
using MicroserviceBackendListing.Enums;
using MicroserviceContract.Dtos.Listing;
using MicroserviceBackendListing.Dtos;

namespace MicroserviceBackendListing.BusinessLogic
{
    /// <summary>
    /// 
    /// </summary>
    public class ListingAccess : BusinessLogicBase
    {
        private UtilityFunctions _utils;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="u"></param>
        /// <param name="memoryCache"></param>
        /// <param name="utils"></param>
        public ListingAccess(IUnitOfWork u, IMemoryCache memoryCache, UtilityFunctions utils)
        {
            _unitOfWork = u;
            _memoryCache = memoryCache;
            _utils = utils;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="listingId"></param>
        /// <param name="partyId"></param>
        /// <returns></returns>
        internal async Task<List<PartyListingAccessCDto>> GetListAsync(long listingId, Guid? partyId = null)
        {
            if (listingId < 0)
                throw new ArgumentOutOfRangeException(nameof(listingId));

            string sql = @"
                SELECT
                       [ListingAccessId]
                      ,[ListingId]
                      ,[PartyId]
                      ,[ListingAccessRoleId]
                      ,[DisplayName]
                FROM [listing].[ListingAccess]
                WHERE [ListingId] = @listingId AND [Deleted] = 0
            ";

            if (!partyId.GuidIsNullOrEmpty())
            {
                sql += @" AND [PartyId] = @partyId ";
            }

            var result = new List<PartyListingAccessCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("partyId", partyId);
                command.AddArgument("listingId", listingId);
                result = await command.SelectMany<PartyListingAccessCDto>();
            }

            return result;
        }

        /// <summary>
        /// Get Listing Access for Party (PartyId) to Listing (ListingId)
        /// Internal use in UpdateAccess. [private]
        /// </summary>
        /// <param name="listingId"></param>
        /// <param name="partyId"></param>
        /// <param name="accessRoleId"></param>
        /// <param name="ignoreErrorIfNotExist"></param>
        /// <returns></returns>
        internal async Task<PartyListingAccessCDto> GetAccessAsync(long listingId, Guid partyId, int accessRoleId, bool ignoreErrorIfNotExist = false)
        {
            if (listingId < 0)
                throw new ArgumentOutOfRangeException(nameof(listingId));

            if (partyId.GuidIsNullOrEmpty())
                throw new ArgumentOutOfRangeException(nameof(partyId));

            string sql = @"
                SELECT
                      [ListingAccessId]
                      ,[ListingId]
                      ,[PartyId]
                      ,[ListingAccessRoleId]
                      ,[DisplayName]
                FROM [listing].[ListingAccess]
                WHERE [ListingId] = @listingId AND [PartyId] = @partyId AND [Deleted] = 0 AND [ListingAccessRoleId] = @accessRoleId ";

            var result = new PartyListingAccessCDto();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("accessRoleId", accessRoleId);
                command.AddArgument("listingId", listingId);
                command.AddArgument("partyId", partyId);
                result = await command.SelectSingle<PartyListingAccessCDto>();
            }
            if (result == null)
            {
                if (!(bool)ignoreErrorIfNotExist)
                {
                    throw new HttpRequestException($"Listing Access for Party '{partyId}' to Listing '{listingId}' does not exist", null, System.Net.HttpStatusCode.UnprocessableEntity);
                }

            }

            return result;
        }

        internal async Task<(bool viewAccessChanged, bool editAccessChanged)> UpdateViewAndEditListingAccessAsync(long listingId, List<PartyListingAccessCDto>? partyViewAccess, List<PartyListingAccessCDto>? partyEditAccess)
        {
            bool viewAccessChanged = false;
            var existingDbListingAccess = await GetListAsync(listingId);
            if (partyViewAccess != null)
            {
                //Add
                foreach (var partyViewAccessItem in partyViewAccess)
                {
                    var existingRec = existingDbListingAccess.FirstOrDefault(x => x.ListingAccessRoleId == (int)ListingAccessRoleEnum.ViewAccess && x.PartyId == partyViewAccessItem.PartyId);
                    if (existingRec == null)
                    {
                        partyViewAccessItem.ListingAccessRoleId = (int)ListingAccessRoleEnum.ViewAccess;
                        partyViewAccessItem.ListingId = listingId;
                        await AddAccess(partyViewAccessItem);
                        viewAccessChanged = true;
                    }
                }
                //Delete
                foreach (var partyViewAccessItem in existingDbListingAccess.Where(x => x.ListingAccessRoleId == (int)ListingAccessRoleEnum.ViewAccess))
                {
                    var existingRec = partyViewAccess.FirstOrDefault(x => x.PartyId == partyViewAccessItem.PartyId);
                    if (existingRec == null)
                    {
                        await RemoveAccess(listingId, partyViewAccessItem.PartyId, (int)ListingAccessRoleEnum.ViewAccess);
                        viewAccessChanged = true;
                    }
                }
            }
            bool editAccessChanged = false;
            if (partyEditAccess != null)
            {
                //Add
                foreach (var partyEditAccessItem in partyEditAccess)
                {
                    var existingRec = existingDbListingAccess.FirstOrDefault(x => x.ListingAccessRoleId == (int)ListingAccessRoleEnum.EditAccess && x.PartyId == partyEditAccessItem.PartyId);
                    if (existingRec == null)
                    {
                        partyEditAccessItem.ListingAccessRoleId = (int)ListingAccessRoleEnum.EditAccess;
                        partyEditAccessItem.ListingId = listingId;
                        await AddAccess(partyEditAccessItem);
                        editAccessChanged = true;
                    }
                }
                //Delete
                foreach (var partyEditAccessItem in existingDbListingAccess.Where(x => x.ListingAccessRoleId == (int)ListingAccessRoleEnum.EditAccess))
                {
                    var existingRec = partyEditAccess.FirstOrDefault(x => x.PartyId == partyEditAccessItem.PartyId);
                    if (existingRec == null)
                    {
                        await RemoveAccess(listingId, partyEditAccessItem.PartyId, (int)ListingAccessRoleEnum.EditAccess);
                        editAccessChanged = true;
                    }
                }
            }
            return (viewAccessChanged, editAccessChanged);
        }

        /// <summary>
        /// Create Listing Access for Party (PartyId) to Listing (ListingId) with type of access
        /// (ListingAccessRoleId: 0 -- "No Access", 1 -- "View Only", 2 -- "Editor")
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        internal async Task AddAccess(PartyListingAccessCDto dto)
        {
            if (dto.ListingId < 0)
                throw new ArgumentOutOfRangeException(nameof(dto.ListingId));

            if (dto.PartyId.GuidIsNullOrEmpty())
                throw new ArgumentOutOfRangeException(nameof(dto.PartyId));

            if (dto.ListingAccessRoleId < 0)
                throw new ArgumentOutOfRangeException(nameof(dto.ListingAccessRoleId));

            var exist = await GetAccessAsync(dto.ListingId, dto.PartyId, dto.ListingAccessRoleId, true);
            if (exist != null)
            {
                return;
            }
            else
            {
                GetListingAccessCDto access = new GetListingAccessCDto()
                {
                    ListingId = dto.ListingId,
                    PartyId = dto.PartyId,
                    ListingAccessRoleId = dto.ListingAccessRoleId,
                    DisplayName = dto.DisplayName,
                    TenantId = (int)_utils.TenantId!,
                    CreatedOn = DateTimeOffset.UtcNow,
                    CreatedByName = _utils.UserFullName,
                    PartyType = dto.PartyType,
                    Deleted = false
                };

                string sql = @"
            INSERT INTO [listing].[ListingAccess]
            (
              [ListingId]
              ,[PartyId]
              ,[PartyType]
              ,[ListingAccessRoleId]
              ,[TenantId]
              ,[DisplayName]
              ,[CreatedOn]
              ,[CreatedByName]
              ,[Deleted]
            )
            VALUES
            (
              @ListingId,
              @PartyId,
              @PartyType,
              @ListingAccessRoleId,
              @TenantId,
              @DisplayName,
              @CreatedOn,
              @CreatedByName,
              @Deleted
            )
            ";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
                {
                    command.AddArguments(access);
                    await command.Execute();
                }
            }
        }

        /// <summary>
        /// Create Listing Access for Party (listPartyIds) to Listing (ListingId) with type of access
        /// (ListingAccessRoleId: 0 -- "No Access", 1 -- "View Only", 2 -- "Editor")
        /// </summary>
        /// <param name="listingId"></param>
        /// <param name="list"></param>
        /// <returns></returns>
        internal async Task AddAccessForParty(long listingId, List<PartyListingAccessCDto> list)
        {
            if (list.Count > 0)
            {
                foreach (var item in list)
                {
                    item.ListingId = listingId;
                    await AddAccess(item);
                }
            }
        }

        /// <summary>
        /// Remove Listing Access for Party (PartyId)
        /// </summary>
        /// <param name="listingId"></param>
        /// <param name="partyId"></param>
        /// <param name="accessRoleId"></param>
        /// <returns></returns>
        internal async Task RemoveAccess(long listingId, Guid partyId, int accessRoleId)
        {
            string sql = @"
                    UPDATE [listing].[ListingAccess]
                    SET
                        [Deleted] = 1,
                        [ModifiedOn] = @ModifiedOn,
                        [ModifiedByName] = @ModifiedByName
                    WHERE [ListingId] = @listingId AND [PartyId] = @partyId AND [Deleted] = 0 AND [ListingAccessRoleId] = @accessRoleId
                    ";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Delete, sql))
            {

                command.AddArgument("accessRoleId", accessRoleId);
                command.AddArgument("listingId", listingId);
                command.AddArgument("partyId", partyId);
                command.AddArgument("ModifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("ModifiedByName", _utils.UserFullName);
                await command.Execute();
            }
        }

        /// <summary>
        /// Check if client user has access to Listing
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        internal async Task<bool> GetHasListingAccess(ListingAccessRequestDto dto)
        {
            string sql = $@"SELECT [Listing].[ListingId]
                        FROM [listing].[Listing] [Listing]
                        INNER JOIN [listing].[ListingType] ON [ListingType].[ListingTypeId] = [Listing].[ListingTypeId] AND ([ListingType].[IsTenantBased] = 0 OR [Listing].[TenantId] = @TenantId )
                        WHERE {(dto.ListingId != null && dto.ListingId > 0 ? "[Listing].[ListingId] = @ListingId" : "[Listing].[ReferenceNo] = @ReferenceNo")}
";
            if (dto.IsManagedListing)
            {
                //Enforce check for any role(s) passed in (Used predominately for Authorised Managed Listing queries). This is because we enforce the Role Check on all Visbility levels except Private.
                sql += $@"
                        AND ([Listing].[ParentEntityId] = @partyId --check if party is owner of listing
                            OR [Listing].[ParentEntityId2] = @partyId --check if party is owner of listing
                            OR ([ListingType].[NonOwnerEditClaimId] IS NOT NULL AND EXISTS (SELECT 1 FROM [users].[UserClaim] UC WHERE UC.ClaimId = [ListingType].[NonOwnerEditClaimId] AND UC.UserId = @userId))
                            OR ([Listing].[VisibilityId] != @privateVisibilityId 
                                AND [Listing].[ListingId] IN (SELECT [listingAccess].[ListingId] --check if party has access to listing, unless its privately listed. This means checking Public listings too.
                                                            FROM [listing].[ListingAccess] [listingAccess]
                                                            WHERE  [listingAccess].[ListingId] = [Listing].[ListingId] AND
                                                                          [listingAccess].[ListingAccessRoleId] IN (@roleAccesses) AND
                                                                          [listingAccess].[Deleted] = 0 AND
                                                                          (([listingAccess].[PartyType] = 'Person' AND [listingAccess].[PartyId] = @partyId) {(ConfigBase.Schemas.ContainsKey("crm") ? "OR ([listingAccess].[PartyType] = 'Organisation' AND EXISTS (SELECT * FROM [crm].[vActivePartyRelationships] [AA] WHERE  [AA].[FromPartyId] = [listingAccess].[PartyId] AND [AA].[ToPartyId] = @partyId))" : "")}))))";
            }
            else
            {
                //Check at a minimum the party has access to view this listing (Used predominately by Public SearchAndFilter Listing queries). This is because of the check made on public Listings.
                sql += $@"
                        AND ([Listing].[VisibilityId] = @publicVisibilityId --public listing
                            OR [Listing].[ParentEntityId] = @partyId --check if party is owner of listing
                            OR [Listing].[ParentEntityId2] = @partyId --check if party is owner of listing
                            OR ([Listing].[VisibilityId] = @membersVisibilityId 
                                AND [Listing].[ListingId] IN (SELECT [listingAccess].[ListingId] --check if party has access to listing (Visibility: Members - 2)
                                                            FROM [listing].[ListingAccess] [listingAccess]
                                                            WHERE  [listingAccess].[ListingId] = [Listing].[ListingId] AND
                                                                          [listingAccess].[ListingAccessRoleId] IN (@roleAccesses) AND
                                                                          [listingAccess].[Deleted] = 0 AND
                                                                          (([listingAccess].[PartyType] = 'Person' AND [listingAccess].[PartyId] = @partyId) {(ConfigBase.Schemas.ContainsKey("crm") ? "OR ([listingAccess].[PartyType] = 'Organisation' AND EXISTS (SELECT * FROM [crm].[vActivePartyRelationships] [AA] WHERE  [AA].[FromPartyId] = [listingAccess].[PartyId] AND [AA].[ToPartyId] = @partyId))" : "")}))))";
            }

            if (dto.ListingTypeId != null)
            {
                //Ensure the Listing Type Id matches the Listing Id passed in
                sql += " AND [Listing].[ListingTypeId] = @listingTypeId ";
            }

            if (ConfigBase.Schemas.ContainsKey("users"))
            {
                sql += @" AND ([Listing].[VisibilityClaimId] IS NULL OR EXISTS (SELECT 1 FROM [users].[UserClaim] UC WHERE UC.ClaimId = [Listing].[VisibilityClaimId] AND UC.UserId = @userId))";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("roleAccesses", dto.RoleAccesses?.ConvertAll<int>(item => (int)item));
                command.AddArgument("userId", _utils.UserId);
                command.AddArgument("listingTypeId", dto.ListingTypeId);
                command.AddArgument("publicVisibilityId", (int)ListingVisibilityEnum.Public);
                command.AddArgument("membersVisibilityId", (int)ListingVisibilityEnum.Members);
                command.AddArgument("privateVisibilityId", (int)ListingVisibilityEnum.Private);
                command.AddArgument("partyId", _utils.PartyId);
                command.AddArgument("ListingId", dto.ListingId);
                command.AddArgument("ReferenceNo", dto.ReferenceNo);
                command.AddArgument("TenantId", _utils.TenantId);
                var id = await command.SelectSingle<long>("ListingId");
                var result = id != null && id != 0;
                return result;
            }
        }
    }

}
