﻿using MicroserviceBackendListing.Dtos;
using MicroserviceContract.Dtos.Editor;
using Microsoft.Extensions.Caching.Memory;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Collections.Specialized;

namespace MicroserviceBackendListing.BusinessLogic.Base
{
    /// <summary>
    /// This class supports fetching the editor Display Containers.
    /// </summary>
    public class Editor : BusinessLogicBase
    {
        private Dictionary<string, object> _queryParameters;

        public Editor(IUnitOfWork u, IMemoryCache memoryCache)
        {
            _unitOfWork = u;
            _queryParameters = new Dictionary<string, object>();
            _memoryCache = memoryCache;
        }


        /// <summary>
        /// Get attributes for each requested display container 
        /// Includes all edit details.
        /// </summary>
        /// <param name="displayContainerCodesArray"></param>
        /// <param name="returnAttributeSelections"></param>
        /// <returns>Returns display containers and all associated attributes</returns>
        /// <exception cref="Exception"></exception>
        internal async Task<List<EditorDisplayContainerCDto>> GetDisplayContainer(string?[] displayContainerCodesArray, bool? returnAttributeSelections = true)
        {
            List<EditorDisplayContainerCDto> DisplayContainers = new List<EditorDisplayContainerCDto>();
            string displayContainerCodesString = string.Join(",", displayContainerCodesArray);

            if (displayContainerCodesArray.Length > 0)
            {
                foreach (var dcc in displayContainerCodesArray)
                {
                    if (dcc != null)
                    {
                        var DisplayContainer = await GetDisplayContainer(dcc);
                        if (DisplayContainer.Enabled == true)
                        {
                            DisplayContainers.Add(DisplayContainer);
                        }
                        else
                        {
                            throw new HttpRequestException($"DisplayContainerCode '{dcc}' is no longer available.", null, System.Net.HttpStatusCode.UnprocessableEntity);
                        }
                    }
                }
                
            }
            else
            {
                DisplayContainers = await GetDisplayContainers();
            }

            foreach (var rec in DisplayContainers)
            {
                if (rec.Enabled == true && rec.DisplayContainerCode != null)
                {
                    rec.Attributes = await GetDisplayContainerAtributes(rec.DisplayContainerCode);
                    foreach (var attr in rec.Attributes)
                    {
                        if (returnAttributeSelections == true && attr.AttributeCode != null)
                        {
                            attr.SelectionValues = await GetListSelectionValues(attr.AttributeCode);
                        }
                    }
                }
            }

            return DisplayContainers;
        }

        internal async Task<List<EditorDisplayContainerCDto>> GetDisplayContainersStartingWith(string displayContainerCodesStartWith, bool? returnAttributeSelections = true)
        {
            var containers = await GetDisplayContainersStarting(displayContainerCodesStartWith);
            string?[] displayContainers = containers.Select(x => x.DisplayContainerCode).ToArray();
            return await GetDisplayContainer(displayContainers, returnAttributeSelections);
        }



        private async Task<EditorDisplayContainerCDto> GetDisplayContainer(string DisplayContainerCode)
        {
            DisplayContainerCode = DisplayContainerCode.Trim();
            string cacheKey = "EditorDisplayContainer" + DisplayContainerCode;

            if (_memoryCache.TryGetValue(cacheKey, out EditorDisplayContainerCDto? cacheValue))
            {
                return cacheValue.DeepClone();
            }

            string sql = @"
                        SELECT  
                        [DisplayContainer].[Title], 
                        [DisplayContainer].[DisplayContainerCode],                         
                        [DisplayContainer].[Enabled],
                        [DisplayContainer].[IsShowTitle],
                        [DisplayContainer].[Icon],
                        [DisplayContainer].[HelpText]
                        FROM [listing].[DisplayContainer] [DisplayContainer] 
                        WHERE [DisplayContainer].[DisplayContainerCode] = @DisplayContainerCode";
            var result = new EditorDisplayContainerCDto();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("DisplayContainerCode", DisplayContainerCode);
                result = await command.SelectSingle<EditorDisplayContainerCDto>();
            }
            if (result == null)
            {
                throw new HttpRequestException($"DisplayContainerCode '{DisplayContainerCode}' does not exist", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (result.Enabled == false)
            {
                throw new HttpRequestException($"DisplayContainerCode '{DisplayContainerCode}' is not enabled", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            _memoryCache.Set(cacheKey, result, CacheOptions());

            return result;
        }

        

        private async Task<List<EditorDisplayContainerCDto>> GetDisplayContainers()
        {
            string cacheKey = "EditorDisplayContainers";

            if (_memoryCache.TryGetValue(cacheKey, out List<EditorDisplayContainerCDto>? cacheValue) && cacheValue != null)
            {
                var cacheResp = new List<EditorDisplayContainerCDto>();
                foreach (var rec in cacheValue)
                {
                    cacheResp.Add(rec.DeepClone());
                }
                return cacheResp;
            }

            string sql = @"
                        SELECT  
                        [DisplayContainer].[Title], 
                        [DisplayContainer].[DisplayContainerCode],                         
                        [DisplayContainer].[Enabled],
                        [DisplayContainer].[IsShowTitle],
                        [DisplayContainer].[Icon],
                        [DisplayContainer].[HelpText]
                        FROM [listing].[DisplayContainer] [DisplayContainer] 
                        WHERE [DisplayContainer].[Enabled] = 1 ";
            var result = new List<EditorDisplayContainerCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                result = await command.SelectMany<EditorDisplayContainerCDto>();
            }

            _memoryCache.Set(cacheKey, result, CacheOptions());

            return result;
        }

        private async Task<List<EditorDisplayContainerCDto>> GetDisplayContainersStarting(string displayContainerCodeStartsWith)
        {
            string cacheKey = "EditorDisplayContainers" + displayContainerCodeStartsWith;

            if (_memoryCache.TryGetValue(cacheKey, out List<EditorDisplayContainerCDto>? cacheValue) && cacheValue != null)
            {
                var cacheResp = new List<EditorDisplayContainerCDto>();
                foreach (var rec in cacheValue)
                {
                    cacheResp.Add(rec.DeepClone());
                }
                return cacheResp;
            }

            string sql = @"
                        SELECT  
                        [DisplayContainer].[Title], 
                        [DisplayContainer].[DisplayContainerCode],                         
                        [DisplayContainer].[Enabled],
                        [DisplayContainer].[IsShowTitle],
                        [DisplayContainer].[Icon],
                        [DisplayContainer].[HelpText]
                        FROM [listing].[DisplayContainer] [DisplayContainer] 
                        WHERE [DisplayContainer].[Enabled] = 1
                        AND [DisplayContainer].[DisplayContainerCode] like @DisplayContainerCode + '%' ";
            var result = new List<EditorDisplayContainerCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("DisplayContainerCode", displayContainerCodeStartsWith);
                result = await command.SelectMany<EditorDisplayContainerCDto>();
            }

            _memoryCache.Set(cacheKey, result, CacheOptions());

            return result;
        }

        private async Task<List<EditorDisplayContainerAttributeCDto>> GetDisplayContainerAtributes(string DisplayContainerCode)
        {
            DisplayContainerCode = DisplayContainerCode.Trim();
            string cacheKey = "EditorDisplayContainersAttrs" + DisplayContainerCode;

            if (_memoryCache.TryGetValue(cacheKey, out List<EditorDisplayContainerAttributeCDto>? cacheValue) && cacheValue != null)
            {
                var cacheResp = new List<EditorDisplayContainerAttributeCDto>();
                foreach (var rec in cacheValue)
                {
                    rec.SelectionValues = new List<EditorSelectionValues>();
                    cacheResp.Add(rec.DeepClone());
                }
                return cacheResp;
            }

            string sql = @"
                        SELECT  
                        [Attribute].[AttributeCode],
                        [Attribute].[Label],                        
                        [Attribute].[AttributeGroupCode],
                        [Attribute].[AttributeValueTypeCode],
                        CASE
                            WHEN [DisplayContainerAttribute].[InputTypeCode] IS NULL THEN [Attribute].[InputTypeCode]
                            ELSE [DisplayContainerAttribute].[InputTypeCode]
                        END as [InputTypeCode],
                        [Attribute].[IsEnabled],
                         [Attribute].[IsManyAllowed],
                         [Attribute].[Icon],
                         [Attribute].[ReadAccessClaim],
                         [Attribute].[WriteAccessClaim],
                         [Attribute].[NumericDecimalPlaces],
                         [Attribute].[IsRequired],
                         [Attribute].[NumericMinValue],
                         [Attribute].[NumericMaxValue],
                         [Attribute].[TextMinCharacters],
                         [Attribute].[TextMaxCharacters],
                         [Attribute].[MinDateDaysFromToday],
                         [Attribute].[MaxDateDaysFromToday],
                         [Attribute].[MinSelectionsRequired],
                         [Attribute].[MaxSelectionsAllowed],
                         [Attribute].[MaxStars],
                         [Attribute].[HelpText],
                         [DisplayContainerAttribute].[DefaultTextValue],
                         [DisplayContainerAttribute].[DefaultNumericValue],
                         [DisplayContainerAttribute].[DefaultDateAddDays],
                         [DisplayContainerAttribute].[DefaultDateAddMonths],
                         [DisplayContainerAttribute].[DefaultTimeAddMinutes],
                         [DisplayContainerAttribute].[DisplayContainerCode],
                        [DisplayContainerAttribute].[DisplayContainerAttributeId]
                        FROM [listing].[DisplayContainerAttribute] [DisplayContainerAttribute] 
                        INNER JOIN [listing].[Attribute] [Attribute] ON [Attribute].[AttributeCode] = [DisplayContainerAttribute].[AttributeCode]
                        WHERE [DisplayContainerAttribute].[Deleted] = 0 AND [Attribute].[IsEnabled] = 1 AND [DisplayContainerAttribute].[DisplayContainerCode] = @DisplayContainerCode
                        ORDER BY [DisplayContainerAttribute].[SortOrder], [Attribute].[SortOrder]";

            var result = new List<EditorDisplayContainerAttributeCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("DisplayContainerCode", DisplayContainerCode);
                result = await command.SelectMany<EditorDisplayContainerAttributeCDto>();
            }

            _memoryCache.Set(cacheKey, result, CacheOptions());

            return result;
        }

        internal virtual async Task<List<EditorSelectionValues>> GetListSelectionValues(String AttributeCode)
        {
            string cacheKey = "EditorSelectionValues" + AttributeCode;
            if (_memoryCache.TryGetValue(cacheKey, out List<EditorSelectionValues>? cacheValue) && cacheValue != null)
            {
                return cacheValue.DeepClone();
            }

            string sql = @"
                        SELECT  
                        [AttributeDropdownValue].[Value], 
                        [AttributeDropdownValue].[SortOrder],
                        [AttributeDropdownValue].[DisplayValue] 
                        FROM [listing].[AttributeDropdownValue][AttributeDropdownValue]
                        WHERE [AttributeCode] = @AttributeCode
                        ORDER BY [AttributeDropdownValue].[SortOrder]";
            var result = new List<EditorSelectionValues>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("AttributeCode", AttributeCode);
                result = await command.SelectMany<EditorSelectionValues>();
            }

            _memoryCache.Set(cacheKey, result, CacheOptions());

            return result;
        }


    }
}
