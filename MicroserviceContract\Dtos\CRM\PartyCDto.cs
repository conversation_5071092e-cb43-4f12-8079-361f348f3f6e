﻿using Redi.Prime3.MicroService.BaseLib;
using Sql;

namespace MicroserviceContract.Dtos.CRM

{
    [Mappable(nameof(PartyId))]
    public class BasePartyCDto : DtoBase
    {
        public Guid? PartyId { get; set; }
        /// <summary>
        /// The Persons Fullname, or Organisations Name
        /// </summary>
        public string? DisplayName { get; set; }
        /// <summary>
        /// Alias for person/org that can be displayed publicly
        /// </summary>
        public string? AliasName { get; set; }
        /// <summary>
        /// Short Name that can be used to show who a user is on small lists
        /// </summary>
        public string? ShortName { get; set; }
        public string? StatusCode { get; set; }
        public Guid? AvatarImageId { get; set; }
        public Guid? UserId { get; set; }
        //[IgnoreDbMapping]
        public string? PartyType { get; set; }
        public string? AvatarImageUrl { get; set; }
        public string? ReferenceCode { get; set; }
    }

    public class PartyCDto : BasePartyCDto
    {
        public string? RoleTypeCode { get; set; }

        /// <summary>
        /// The person party object
        /// Only returned if the Party is a Person
        /// </summary>
        public GetPersonCDto? Person { get; set; }

        /// <summary>
        /// The organisation party object
        /// Only returned if the Party if an Organisation.
        /// Organisation can cover: Company/Business, Team, Department, Division, Location, Shop 
        /// </summary>
        public GetOrganisationCDto? Organisation { get; set; }
    }

    public class ExtendedPartyCDto : PartyCDto
    {
        /// <summary>
        /// Fields represent dynamic attributes (custom fields) that are associated with a Party
        /// </summary>
        public Dictionary<string, object>? Fields { get; set; }
        /// <summary>
        /// Response object only, returning various statistics relevant to the party.
        /// Each statistic must be requested
        /// </summary>
        public Dictionary<string, object>? Statistics { get; set; }
        /// <summary>
        /// Parent relation fields represent parent Parties related to this party (eg. Team, Department, Manager)
        /// </summary>
        public Dictionary<string, object>? ParentRelationFields { get; set; }
        /// <summary>
        /// Child relation fields represent child Parties related to this party (eg. members of a team)
        /// NOTE! This is generally not returned by default as it could be large,
        /// </summary>
        public Dictionary<string, object>? ChildRelationFields { get; set; }
        /// <summary>
        /// Contact fields. Dynamic.
        /// Email_1, Email_2, Phone_1, Phone_2, 
        /// </summary>
        public Dictionary<string, object>? ContactFields { get; set; }
        /// <summary>
        /// Address 1
        /// </summary>
        public PartyAddressCDto? Address_1 { get; set; }
        /// <summary>
        /// Address 2
        /// </summary>
        public PartyAddressCDto? Address_2 { get; set; }
    }

    public class ListPartyCDto : ExtendedPartyCDto
    {

    }

    public class PartyAddressCDto
    {
        public string? Lines { get; set; }
        public string? City { get; set; }
        public string? StateOrProvince { get; set; }
        public string? PostalCode { get; set; }
        public string? CountryCode { get; set; }
        public string? CountryName { get; set; }
        public string? FullAddress { get; set; }
        /// <summary>(Optional) Lattitude of the address.</summary>
        public decimal? Latitude { get; set; }
        /// <summary>(Optional) Longitude of the address.</summary>
        public decimal? Longitude { get; set; }
    }

    [Mappable(nameof(PartyId))]
    public class GetForAvatarPartyCDto
    {
        public Guid PartyId { get; set; }
        public string? Name { get; set; }
        public string? AvatarImageUrl { get; set; }
        /// <summary>
        /// Attribute Value is requested
        /// </summary>
        public object? Attr1Val { get; set; }
    }

    public class PartyUserCDto : PartyCDto
    {
        public int? TenantId { get; set; }
    }
}
