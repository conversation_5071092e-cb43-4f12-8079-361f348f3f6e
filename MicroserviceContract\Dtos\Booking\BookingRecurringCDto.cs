﻿using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MicroserviceContract.Dtos.Booking
{
    /// <summary>
    /// A Recurring Booking Request
    /// Support creation and update of a recurring booking.
    /// </summary>
    [Mappable(nameof(BookingRecurringId))]
    public class BookingRecurringRequestCDto
    {
        /// <summary>
        /// The Unique Booking Recurring Database Id
        /// Auto assigned by <PERSON> on creation of new recurring booking
        /// </summary>
        public int? BookingRecurringId { get; set; }
        /// <summary>
        /// The date from which the recurring booking commenced
        /// </summary>
        public DateTimeOffset? StartsFrom { get; set; }
        /// <summary>
        /// Optional end date when the recurring booking will stop. 
        /// If empty the recurring booking will continue forever
        /// </summary>
        public DateTimeOffset? EndsOn { get; set; }
        /// <summary>
        /// The time the booking commcences
        /// </summary>
        public TimeOnly? FromTime {  get; set; }
        /// <summary>
        /// The Time the booking ends
        /// </summary>
        public TimeOnly? ToTime { get; set; }
        /// <summary>
        /// When true indicates this is a full day booking
        /// </summary>
        public bool? IsAllDay { get; set; }
        /// <summary>
        /// For a booking that has a duration longer than 1 day this tracks the total number of full days.
        /// </summary>
        public int? NumberOfFullDays { get; set; }
        /// <summary>
        /// Booking Recurrence Frequency (pattern). 1 - Daily,2 - Weekly, 3 - Monthly, 4 - Yearly.
        /// </summary>
        public BookingRecurranceFrequencyEnum? BookingRecurranceFrequencyId { get; set; }
        /// <summary>
        /// If a frequency requires a count. Eg. Every X Days, or Every X Weeks. 
        /// This count is the X value.
        /// </summary>
        public short? RecurEveryX { get; set; }
        /// <summary>
        /// The Day of the month that a Monthly or Yearly frequency recurring booking is to occur on.
        /// </summary>
        public short? RecurOnDayOfMonth { get; set; }
        /// <summary>
        /// The Month (1 to 12) that a Yearly recurring booking is to occur on
        /// </summary>
        public short? RecurOnMonth {  get; set; }
        /// <summary>
        /// When a booking needs to occur on the 1st, 2nd, etc Tuesday (weekday) of a month.
        /// 1 - First, 2 - Second, 3 - Third, 4 - Fourth, 9 - Last
        /// </summary>
        public BookingRecurOnPositionEnum? BookingRecurOnPositionId { get; set; }
        /// <summary>
        /// When a booking will recur on.
        /// 1 - Day of Month, 2 -  Week Day, 3 - Weekend Day, 11 - Monday, 12 - Tuesday, 
        /// 13 - Wednesday, 14 - Thursday, 15 - Friday, 16 - Saturday, 17 - Sunday.
        /// </summary>
        public BookingRecurOnEnum? BookingRecurOnId { get; set; }
        /// <summary>
        /// When true indicates item recurs on Monday
        /// </summary>
        public bool? IncludeMonday { get; set; }
        /// <summary>
        /// When true indicates item recurs on a Tuesday
        /// </summary>
        public bool? IncludeTuesday { get; set; }
        /// <summary>
        /// When true indicates item recurs on a Wednesday
        /// </summary>
        public bool? IncludeWednesday { get; set; }
        /// <summary>
        /// When true indicates item recurs on a Thursday
        /// </summary>
        public bool? IncludeThursday { get; set; }
        /// <summary>
        /// When true indicates item recurs on a Friday
        /// </summary>
        public bool? IncludeFriday { get; set; }
        /// <summary>
        /// When true indicates item recurs on a Saturday
        /// </summary>
        public bool? IncludeSaturday { get; set; }
        /// <summary>
        /// When true indicates item recurs on a Sunday
        /// </summary>
        public bool? IncludeSunday { get; set; }
        /// <summary>
        /// When true indicates item recurs on Public Holidays
        /// </summary>
        public bool? IncludePublicHolidays { get; set; }

        /// <summary>
        /// Identifies the type of Entity that owns the recurring booking
        /// This could be Party, User, etc
        /// </summary>
        public string? BookingOwnerParentEntityType { get; set; }
        /// <summary>
        /// The parent entity id that owns this recurring booking record
        /// </summary>
        public Guid? BookingOwnerParentEntityId { get; set; }
        public long? BookingOwnerParentEntityIntId { get; set; }
        public int? PickupFromHoldingLocationId { get; set; }
        public int? ReturnToHoldingLocationId { get; set; }
        /// <summary>
        /// Optional note for the recurring booking
        /// </summary>
        public string? Note {  get; set; }

        /// <summary>
        /// Comma separated list of booking items ids
        /// From BookingRecurringItem Table
        /// </summary>
        public string? BookingItemIds { get; set; }
    }

    [Mappable(nameof(BookingRecurringId))]
    public class BookingRecurringResponseCDto : DtoBase
    {
        /// <summary>
        /// The Unique Booking Recurring Database Id
        /// Auto assigned by DB on creation of new recurring booking
        /// </summary>
        public int? BookingRecurringId { get; set; }
        /// <summary>
        /// The date from which the recurring booking commenced
        /// </summary>
        public DateTimeOffset? StartsFrom { get; set; }
        /// <summary>
        /// Optional end date when the recurring booking will stop. 
        /// If empty the recurring booking will continue forever
        /// </summary>
        public DateTimeOffset? EndsOn { get; set; }
        /// <summary>
        /// The time the booking commcences
        /// </summary>
        public TimeOnly? FromTime { get; set; }
        /// <summary>
        /// The Time the booking ends
        /// </summary>
        public TimeOnly? ToTime { get; set; }
        /// <summary>
        /// When true indicates this is a full day booking
        /// </summary>
        public bool? IsAllDay { get; set; }
        /// <summary>
        /// For a booking that has a duration longer than 1 day this tracks the total number of full days.
        /// </summary>
        public int? NumberOfFullDays { get; set; }
        /// <summary>
        /// Booking Recurrence Frequency (pattern). 1 - Daily,2 - Weekly, 3 - Monthly, 4 - Yearly.
        /// </summary>
        public BookingRecurranceFrequencyEnum? BookingRecurranceFrequencyId { get; set; }
        /// <summary>
        /// If a frequency requires a count. Eg. Every X Days, or Every X Weeks. 
        /// This count is the X value.
        /// </summary>
        public short? RecurEveryX { get; set; }
        /// <summary>
        /// The Day of the month that a Monthly or Yearly frequency recurring booking is to occur on.
        /// </summary>
        public short? RecurOnDayOfMonth { get; set; }
        /// <summary>
        /// The Month (1 to 12) that a Yearly recurring booking is to occur on
        /// </summary>
        public short? RecurOnMonth { get; set; }
        /// <summary>
        /// When a booking needs to occur on the 1st, 2nd, etc Tuesday (weekday) of a month.
        /// 1 - First, 2 - Second, 3 - Third, 4 - Fourth, 9 - Last
        /// </summary>
        public BookingRecurOnPositionEnum? BookingRecurOnPositionId { get; set; }
        /// <summary>
        /// When a booking will recur on.
        /// 1 - Day of Month, 2 -  Week Day, 3 - Weekend Day, 11 - Monday, 12 - Tuesday, 
        /// 13 - Wednesday, 14 - Thursday, 15 - Friday, 16 - Saturday, 17 - Sunday.
        /// </summary>
        public BookingRecurOnEnum? BookingRecurOnId { get; set; }
        /// <summary>
        /// When true indicates item recurs on Monday
        /// </summary>
        public bool? IncludeMonday { get; set; }
        /// <summary>
        /// When true indicates item recurs on a Tuesday
        /// </summary>
        public bool? IncludeTuesday { get; set; }
        /// <summary>
        /// When true indicates item recurs on a Wednesday
        /// </summary>
        public bool? IncludeWednesday { get; set; }
        /// <summary>
        /// When true indicates item recurs on a Thursday
        /// </summary>
        public bool? IncludeThursday { get; set; }
        /// <summary>
        /// When true indicates item recurs on a Friday
        /// </summary>
        public bool? IncludeFriday { get; set; }
        /// <summary>
        /// When true indicates item recurs on a Saturday
        /// </summary>
        public bool? IncludeSaturday { get; set; }
        /// <summary>
        /// When true indicates item recurs on a Sunday
        /// </summary>
        public bool? IncludeSunday { get; set; }
        /// <summary>
        /// When true indicates item recurs on Public Holidays
        /// </summary>
        public bool? IncludePublicHolidays { get; set; }

        /// <summary>
        /// Identifies the type of Entity that owns the recurring booking
        /// This could be Party, User, etc
        /// </summary>
        public string? BookingOwnerParentEntityType { get; set; }
        /// <summary>
        /// The parent entity id that owns this recurring booking record
        /// </summary>
        public Guid? BookingOwnerParentEntityId { get; set; }
        public long? BookingOwnerParentEntityIntId { get; set; }
        public string? BookingOwnerName { get; set; }
        public int? PickupFromHoldingLocationId { get; set; }
        public string? PickupFromHoldingLocationName { get; set; }
        public int? ReturnToHoldingLocationId { get; set; }
        public string? ReturnToHoldingLocationName { get; set; }
        /// <summary>
        /// Optional note for the recurring booking
        /// </summary>
        public string? Note { get; set; }

        public string? BookingRecurOnLabel { get; set; }
        public string? BookingRecurOnPositionLabel { get; set; }
        public string? BookingRecurranceFrequencyLabel { get; set; }

        /// <summary>
        /// Comma separated list of booking items ids
        /// </summary>
        public string? BookingItemIds { get; set; }
        /// <summary>
        /// Comma separated list of booking item names
        /// </summary>
        public string? BookingItemNames { get; set; }

        /// <summary>
        /// This is the datetime of the most future record that has been created for the recurring booking in the BookingRecord table.
        /// </summary>
        public DateTimeOffset? ForwardRecordsCreatedToDate { get; set; }
        public int? TenantId { get; set; }
    }

    public enum BookingRecurranceFrequencyEnum
    { 
        Daily = 1,
        Weekly = 2,
        Monthly = 3,
        Yearly = 4
    }

    public enum BookingRecurOnPositionEnum
    {
        First = 1,
        Second = 2,
        Third = 3,
        Fourth = 4,
        Last = 9
    }

    public enum BookingRecurOnEnum
    {
        DayOfMonth = 1,
        WeekDay = 2,
        WeekendDay = 3,
        Monday = 11,
        Tuesday = 12,
        Wednesday = 13,
        Thursday = 14,
        Friday = 15,
        Saturday = 16,
        Sunday = 17
    }

}
