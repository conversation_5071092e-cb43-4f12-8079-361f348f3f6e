using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Collections.Specialized;
using Microsoft.Extensions.Caching.Memory;

namespace MicroserviceBackendWorkflow.BusinessLogic
{
    public class WorkflowStepType : BusinessLogicBase
    {
        public WorkflowStepType(IUnitOfWork unitOfWork, IMemoryCache memoryCache)
        {
            _unitOfWork = unitOfWork;
            _memoryCache = memoryCache;
        }

        /// <summary>
        /// Get a single WorkflowStepType by ID
        /// </summary>
        /// <param name="workflowStepTypeId">The workflow step type ID</param>
        /// <param name="ignoreErrorIfNotExists">If false, throws exception when not found</param>
        /// <returns>WorkflowStepTypeDto or null if not found</returns>
        internal async Task<WorkflowStepTypeDto?> GetAsync(short workflowStepTypeId, bool ignoreErrorIfNotExists = false)
        {
            string cacheKey = "WorkflowStepType" + workflowStepTypeId;

            if (_memoryCache.TryGetValue(cacheKey, out WorkflowStepTypeDto? cacheValue))
            {
                return cacheValue?.DeepClone();
            }

            string sql = @"
                SELECT [WorkflowStepTypeId]
                      ,[Label]
                      ,[IsEnabled]
                      ,[SortOrder]
                      ,[Description]
                FROM [workflow].[WorkflowStepType]
                WHERE [WorkflowStepTypeId] = @workflowStepTypeId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("workflowStepTypeId", workflowStepTypeId);
                var result = await command.SelectSingle<WorkflowStepTypeDto>();

                if (result == null && !ignoreErrorIfNotExists)
                {
                    throw new HttpRequestException($"WorkflowStepType with ID '{workflowStepTypeId}' not found", null, System.Net.HttpStatusCode.NotFound);
                }

                if (result != null)
                {
                    _memoryCache.Set(cacheKey, result.DeepClone(), CacheOptions());
                }

                return result;
            }
        }

        /// <summary>
        /// Create a new WorkflowStepType
        /// </summary>
        /// <param name="dto">The WorkflowStepType data</param>
        internal async Task CreateAsync(WorkflowStepTypeDto dto)
        {
            if (dto.WorkflowStepTypeId <= 0)
            {
                throw new HttpRequestException("WorkflowStepTypeId must be greater than 0", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (string.IsNullOrWhiteSpace(dto.Label))
            {
                throw new HttpRequestException("Label cannot be null or empty", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            var exists = await GetAsync(dto.WorkflowStepTypeId);
            if (exists != null)
            {
                throw new HttpRequestException($"WorkflowStepType with ID '{dto.WorkflowStepTypeId}' already exists", null, System.Net.HttpStatusCode.Conflict);
            }

            string sql = @"
                INSERT INTO [workflow].[WorkflowStepType]
                ([WorkflowStepTypeId], [Label], [IsEnabled], [SortOrder], [Description])
                VALUES
                (@WorkflowStepTypeId, @Label, @IsEnabled, @SortOrder, @Description)";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArguments(dto);
                await command.Execute();
            }
        }

        /// <summary>
        /// Update an existing WorkflowStepType
        /// </summary>
        /// <param name="dto">The WorkflowStepType data</param>
        internal async Task UpdateAsync(WorkflowStepTypeDto dto)
        {
            if (dto.WorkflowStepTypeId <= 0)
            {
                throw new HttpRequestException("WorkflowStepTypeId must be greater than 0", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (string.IsNullOrWhiteSpace(dto.Label))
            {
                throw new HttpRequestException("Label cannot be null or empty", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            var exists = await GetAsync(dto.WorkflowStepTypeId);
            if (exists == null)
            {
                throw new HttpRequestException($"WorkflowStepType with ID '{dto.WorkflowStepTypeId}' not found", null, System.Net.HttpStatusCode.NotFound);
            }

            string sql = @"
                UPDATE [workflow].[WorkflowStepType]
                SET [Label] = @Label,
                    [IsEnabled] = @IsEnabled,
                    [SortOrder] = @SortOrder,
                    [Description] = @Description
                WHERE [WorkflowStepTypeId] = @WorkflowStepTypeId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArguments(dto);
                await command.Execute();
            }
        }

        /// <summary>
        /// Delete a WorkflowStepType
        /// </summary>
        /// <param name="workflowStepTypeId">The workflow step type ID</param>
        internal async Task DeleteAsync(short workflowStepTypeId)
        {
            var exists = await GetAsync(workflowStepTypeId);
            if (exists == null)
            {
                throw new HttpRequestException($"WorkflowStepType with ID '{workflowStepTypeId}' not found", null, System.Net.HttpStatusCode.NotFound);
            }

            string sql = @"
                DELETE FROM [workflow].[WorkflowStepType]
                WHERE [WorkflowStepTypeId] = @workflowStepTypeId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Delete, sql))
            {
                command.AddArgument("workflowStepTypeId", workflowStepTypeId);
                await command.Execute();
            }
        }

        /// <summary>
        /// Get a list of WorkflowStepTypes with filtering and paging
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="showDisabled">Include disabled records</param>
        /// <returns>List of WorkflowStepTypes</returns>
        internal async Task<ListResponseDto<WorkflowStepTypeListDto>> GetListAsync(StandardListParameters standardListParameters, bool showDisabled = false)
        {
            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "WorkflowStepTypeId", "[WorkflowStepType].[WorkflowStepTypeId]" },
                { "Label", "[WorkflowStepType].[Label]" },
                { "SortOrder", "[WorkflowStepType].[SortOrder]" },
                { "IsEnabled", "[WorkflowStepType].[IsEnabled]" }
            };

            string sql = @"
                SELECT [WorkflowStepTypeId]
                      ,[Label]
                      ,[IsEnabled]
                      ,[SortOrder]
                      ,[Description]
                FROM [workflow].[WorkflowStepType]
                WHERE 1=1";

            if (!showDisabled)
            {
                sql += " AND [IsEnabled] = 1";
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "SortOrder");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<WorkflowStepTypeListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);
                result.List = await command.SelectMany<WorkflowStepTypeListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[WorkflowStepType]
                WHERE 1=1";

            if (!showDisabled)
            {
                countSql += " AND [IsEnabled] = 1";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }
    }
}
