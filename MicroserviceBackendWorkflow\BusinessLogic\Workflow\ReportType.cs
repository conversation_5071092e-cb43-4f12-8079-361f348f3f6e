using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Collections.Specialized;

namespace MicroserviceBackendWorkflow.BusinessLogic
{
    public class ReportType : BusinessLogicBase
    {
        public ReportType(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a single ReportType by ID
        /// </summary>
        /// <param name="reportTypeId">The report type ID</param>
        /// <returns>ReportTypeDto or null if not found</returns>
        internal async Task<ReportTypeDto?> GetAsync(short reportTypeId)
        {
            string sql = @"
                SELECT [ReportTypeId]
                      ,[Label]
                      ,[IsEnabled]
                FROM [workflow].[ReportType]
                WHERE [ReportTypeId] = @reportTypeId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("reportTypeId", reportTypeId);
                return await command.SelectSingle<ReportTypeDto>();
            }
        }

        /// <summary>
        /// Get a list of ReportTypes with filtering and paging
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="showDisabled">Include disabled records</param>
        /// <returns>List of ReportTypes</returns>
        internal async Task<ListResponseDto<ReportTypeListDto>> GetListAsync(StandardListParameters? standardListParameters = null, bool showDisabled = false)
        {
            if (standardListParameters == null)
            {
                standardListParameters = new StandardListParameters();
            }

            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "ReportTypeId", "[ReportType].[ReportTypeId]" },
                { "Label", "[ReportType].[Label]" },
                { "IsEnabled", "[ReportType].[IsEnabled]" }
            };

            string sql = @"
                SELECT [ReportTypeId]
                      ,[Label]
                      ,[IsEnabled]
                FROM [workflow].[ReportType]
                WHERE 1=1";

            if (!showDisabled)
            {
                sql += " AND [IsEnabled] = 1";
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "ReportTypeId");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<ReportTypeListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);
                result.List = await command.SelectMany<ReportTypeListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[ReportType]
                WHERE 1=1";

            if (!showDisabled)
            {
                countSql += " AND [IsEnabled] = 1";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }
    }
}
