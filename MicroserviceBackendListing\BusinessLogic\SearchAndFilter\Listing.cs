﻿using MicroserviceBackendListing.Dtos;
using MicroserviceBackendListing.Enums;

using MicroserviceContract.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using MicroserviceContract.Dtos.Listing;
using Microsoft.Extensions.Caching.Memory;
using Sql;
using System.Collections.Specialized;
using MicroserviceContract.Dtos.Common;

namespace MicroserviceBackendListing.BusinessLogic.Base
{
    /// <summary>
    /// This is the main Listing logic that is responsible for searching, fetching and filtering Listings.
    /// Updating and creating Listings is managed by the ManageListing class.
    /// CDto's associated with this class are to be used only here and not in the ManageListing class.
    /// </summary>
    public class Listing : BusinessLogicBase
    {
        private Dictionary<string, object> _queryParameters;
        private readonly Func<ListingType> _listingType;
        private UtilityFunctions _utils;
        private readonly Favourite _favourite;

        public Listing(IUnitOfWork u, IMemoryCache memoryCache, Func<ListingType> listingType, UtilityFunctions utils, Favourite favourite)
        {
            _unitOfWork = u;
            _queryParameters = new Dictionary<string, object>();
            _memoryCache = memoryCache;
            _listingType = listingType;
            _utils = utils;
            _favourite = favourite;
        }

        internal async Task<Int64> GetListingId(string? referenceNo)
        {

            if (string.IsNullOrEmpty(referenceNo))
            { 
                throw new ApiErrorException($"Listing referenceNo '{referenceNo}' is empty");
            }

            string cacheKey = "GetListingIdByRef" + referenceNo;

            if (_memoryCache.TryGetValue(cacheKey, out Int64 cacheValue))
            {
                return cacheValue;
            }

            string sql = @"SELECT [Listing].[ListingId]
                        FROM [listing].[Listing] [Listing]
                        Inner Join [listing].[ListingType] ON [ListingType].[ListingTypeId] = [Listing].[ListingTypeId]
                                                           AND ([ListingType].[IsTenantBased] = 0 OR [Listing].[TenantId] = @TenantId )
                        WHERE [Listing].[ReferenceNo] = @ReferenceNo";
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ReferenceNo", referenceNo);
                command.AddArgument("TenantId", _utils.TenantId);
                Int64 listingId = await command.SelectSingle<Int64>("ListingId");

                _memoryCache.Set(cacheKey, listingId, CacheOptions());
                return listingId;
            }
            throw new ApiErrorException($"Listing for referenceNo '{referenceNo}'");
        }

        /// <summary>
        /// Get the base listing details.
        /// </summary>
        /// <param name="listingId">The listing Id.</param>
        /// <param name="includeAttributes">Default False. When true attributes for the requested DisplayContainerCodes are returned</param>
        /// <param name="displayContainerCodesArray">List of displayContainerCodes that attributes should be returned for. Optional. If not supplied all related displayContainer attributes are returned.</param>
        /// <param name="includeMedia">Default True. When true returns the list of media associated with the listing.</param>
        /// <param name="excludeAttributesWithNoData">Default True. When true Any attributes that do not have a value recorded will be excluded from the response. </param>
        /// <param name="mediaCategoryCode">Optional parameter to filter the media returned.</param>
        /// <param name="displayGroupCode"></param>
        /// <param name="excludeDeleted"></param>
        /// <param name="includeTagsInResponse"></param>
        /// <returns>ListingWithDataAndMediaCDto</returns>
        internal async Task<ListingWithDataAndMediaCDto> GetSingleListing(long listingId, bool? includeMedia = true, bool includeAttributes = false, string[]? displayContainerCodesArray = null, bool? excludeAttributesWithNoData = true, string? mediaCategoryCode = null, string displayGroupCode = "", bool excludeDeleted = false, bool includeTagsInResponse = true)
        {
            string sql = @"SELECT [Listing].[ListingId], 
                        [Listing].[ReferenceNo], 
                        [Listing].[Subject], 
                        [Listing].[StatusCode], 
                        [Listing].[ListingTypeId], 
                        [Listing].[ProfileListingMediaId], 
                        [ListingMedia].[MediaUrl] as ProfileListingMediaUrl,
                        [Listing].[ParentEntityIntId],
                        [Listing].[ParentEntityId],
                        [Listing].[ParentEntityType],    
                        [Listing].[ParentEntityId2],
                        [Listing].[ParentEntityType2], 
                        [Listing].[ParentListingId],
                        [Listing].[SortOrder],
                        [Listing].[FromDate], 
                        [Listing].[ToDate],
                        [Listing].[Description],
                        [Listing].[CreatedOn],
                        [Listing].[CreatedByName],
                        [Listing].[ModifiedOn],
                        [Listing].[ModifiedByName]
                        FROM [listing].[Listing] [Listing]
                        Inner Join [listing].[ListingType] ON [ListingType].[ListingTypeId] = [Listing].[ListingTypeId]
                                    AND ([ListingType].[IsTenantBased] = 0 OR [Listing].[TenantId] = @TenantId )
                        LEFT JOIN [listing].[ListingMedia] ON [Listing].[ProfileListingMediaId] = [ListingMedia].[ListingMediaId]
                        WHERE [Listing].[ListingId] = @ListingId";

            if (excludeDeleted)
            {
                sql += @"
 AND [Listing].[Deleted] = 0
";
            }

            var dto = new ListingWithDataAndMediaCDto();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ListingId", listingId);
                command.AddArgument("TenantId", _utils.TenantId);
                dto = await command.SelectSingle<ListingWithDataAndMediaCDto>();
            }

            if (dto == null)
            {
                // Listing not found.
                return dto;
            }

            dto.IsUserFavourite = await _favourite.IsListingFavouriteForLoggedInUser(dto.ListingId);

            if (includeMedia == true)
            {
                List<ListingMediaCDto> media = await GetListingMedia(dto.ListingId, mediaCategoryCode);
                dto.Media = media;
            }

            if (includeAttributes == true)
            {
                List<ListingDisplayContainerCDto> DisplayContainers = await GetAttributeDataAsync(dto.ListingId, displayContainerCodesArray, excludeAttributesWithNoData, displayGroupCode);
                dto.Attributes = DisplayContainers;
            }

            if (includeTagsInResponse == true)
            {
                // Get all Tags linked to the listing and return
                var tags = await GetListingTags(dto.ListingId);
                dto.Tags = tags;
            }

            return dto;
        }

        /// <summary>
        /// Get the base listing details.
        /// </summary>
        /// <param name="referenceNo">The listing reference number.</param>
        /// <param name="includeAttributes">Default False. When true attributes for the requested DisplayContainerCodes are returned</param>
        /// <param name="displayContainerCodesArray">List of displayContainerCodes that attributes should be returned for. Optional. If not supplied all related displayContainer attributes are returned.</param>
        /// <param name="includeMedia">Default True. When true returns the list of media associated with the listing.</param>
        /// <param name="excludeAttributesWithNoData">Default True. When true Any attributes that do not have a value recorded will be excluded from the response. </param>
        /// <param name="mediaCategoryCode">Optional parameter to filter the media returned.</param>
        /// <param name="displayGroupCode"></param>
        /// <param name="excludeDeleted"></param>
        /// <param name="includeTagsInResponse"></param>
        /// <returns>ListingWithDataAndMediaCDto</returns>
        internal async Task<ListingWithDataAndMediaCDto> GetSingleListing(string referenceNo, bool? includeMedia = true, bool includeAttributes = false, string[]? displayContainerCodesArray = null, bool? excludeAttributesWithNoData = true, string? mediaCategoryCode = null, string? displayGroupCode = "", bool excludeDeleted = false, bool includeTagsInResponse = true)
        {
            string sql = @"SELECT [Listing].[ListingId], 
                        [Listing].[ReferenceNo], 
                        [Listing].[Subject], 
                        [Listing].[StatusCode], 
                        [Listing].[ListingTypeId], 
                        [Listing].[ProfileListingMediaId], 
                        [ListingMedia].[MediaUrl] as ProfileListingMediaUrl,
                        [Listing].[ParentEntityIntId],
                        [Listing].[ParentEntityId],
                        [Listing].[ParentEntityType],    
                        [Listing].[ParentEntityId2],
                        [Listing].[ParentEntityType2], 
                        [Listing].[ParentListingId],
                        [Listing].[SortOrder],
                        [Listing].[FromDate], 
                        [Listing].[ToDate],
                        [Listing].[Description],
                        [Listing].[CreatedOn],
                        [Listing].[CreatedByName],
                        [Listing].[ModifiedOn],
                        [Listing].[ModifiedByName]
                        FROM [listing].[Listing] [Listing]
                        Inner Join [listing].[ListingType] ON [ListingType].[ListingTypeId] = [Listing].[ListingTypeId]
                                    AND ([ListingType].[IsTenantBased] = 0 OR [Listing].[TenantId] = @TenantId )
                        LEFT JOIN [listing].[ListingMedia] ON [Listing].[ProfileListingMediaId] = [ListingMedia].[ListingMediaId]
                        WHERE [Listing].[ReferenceNo] = @ReferenceNo";

            if (excludeDeleted)
            {
                sql += @"
 AND [Listing].[Deleted] = 0
";
            }

            var dto = new ListingWithDataAndMediaCDto();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ReferenceNo", referenceNo);
                command.AddArgument("TenantId", _utils.TenantId);
                dto = await command.SelectSingle<ListingWithDataAndMediaCDto>();
            }

            if (dto == null)
            {
                // Listing not found.
                return dto;
            }

            dto.IsUserFavourite = await _favourite.IsListingFavouriteForLoggedInUser(dto.ListingId);

            if (includeMedia == true)
            {
                List<ListingMediaCDto> media = await GetListingMedia(dto.ListingId, mediaCategoryCode);
                dto.Media = media;
            }

            if (includeAttributes == true)
            {
                List<ListingDisplayContainerCDto> DisplayContainers = await GetAttributeDataAsync(dto.ListingId, displayContainerCodesArray, excludeAttributesWithNoData, displayGroupCode);
                dto.Attributes = DisplayContainers;
            }

            if (includeTagsInResponse == true)
            {
                // Get all Tags linked to the listing and return
                var tags = await GetListingTags(dto.ListingId);
                dto.Tags = tags;
            }

            return dto;
        }

        private async Task<List<ListingMediaCDto>> GetListingMedia(Int64 ListingId, string? mediaCategoryCode)
        {
            string sql = @$"
                        SELECT 
                        [ListingMedia].[ListingMediaId], 
                        [ListingMedia].[MediaUrl], 
                        [ListingMedia].[Title],
                        [ListingMedia].[MediaTypeCode],
                        [ListingMedia].[MediaCategoryCode], 
                        [ListingMedia].[ListingId], 
                        [MediaCategory].[Label] AS MediaCategoryLabel, 
                        [MediaType].[Label] AS MediaTypeLabel
                        FROM [listing].[ListingMedia] [ListingMedia]
                        INNER JOIN [listing].[Listing] ON [Listing].[ListingId] = [ListingMedia].[ListingId]
                        Inner Join [listing].[ListingType] ON [ListingType].[ListingTypeId] = [Listing].[ListingTypeId]
                        LEFT JOIN [listing].[MediaCategory] ON [MediaCategory].[MediaCategoryCode] = [ListingMedia].[MediaCategoryCode]
                        LEFT JOIN [listing].[MediaType] ON [MediaType].[MediaTypeCode] = [ListingMedia].[MediaTypeCode]
                        WHERE [ListingMedia].[ListingId] = @ListingId
                        AND [ListingMedia].[Deleted] = 0 
                        {(!string.IsNullOrEmpty(mediaCategoryCode) ? "AND [ListingMedia].[MediaCategoryCode] = @mediaCategoryCode " : "")}";
            var result = new List<ListingMediaCDto>();
            sql += $" ORDER BY [ListingMedia].[SortOrder], [ListingMedia].[ListingMediaId] ";
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ListingId", ListingId);
                command.AddArgument("mediaCategoryCode", mediaCategoryCode);
                result = await command.SelectMany<ListingMediaCDto>();
            }
            return result;
        }

        public async Task<List<ListingTagCDto>> GetListingTags(Int64 ListingId)
        {
            string sql = @$"
                        SELECT 
                        [ListingTag].[ListingTagId], 
                        [ListingTag].[TagId], 
                        [ListingTag].[SortOrder],
                        [Tag].[Label] AS TagLabel,
                        [Tag].[Icon] AS TagIcon,
                        [Tag].[Colour] AS TagColour,
                        [Tag].[Note] AS TagNote,
                        [Tag].[ImageUrl] AS TagImageUrl,
                        [TagSet].[TagSetId], 
                        [TagSet].[Label] AS TagSetLabel, 
                        [TagSet].[Icon] AS TagSetIcon, 
                        [TagSet].[Note] AS TagSetNote, 
                        [ListingTag].[CreatedOn],
                        [ListingTag].[CreatedByName]
                        FROM [listing].[ListingTag] [ListingTag]
                        INNER JOIN [listing].[Tag] ON [ListingTag].[TagId] = [Tag].[TagId] AND [Tag].[IsEnabled] = 1
                        LEFT JOIN [listing].[TagSet] ON [TagSet].[TagSetId] = [Tag].[TagSetId]
                        WHERE [ListingTag].[ListingId] = @ListingId
                        AND [ListingTag].[Deleted] = 0 
                        AND ([Tag].[TagSetId] IS NULL OR [TagSet].[IsEnabled] = 1)
                        ";
            var result = new List<ListingTagCDto>();
            sql += $" ORDER BY [ListingTag].[SortOrder], [TagSet].[SortOrder], [Tag].[SortOrder] ";
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ListingId", ListingId);
                result = await command.SelectMany<ListingTagCDto>();
            }
            return result;
        }

        private async Task<List<ListingMediaCDto>> GetListingMediaForManyListings(List<Int64> listingIds, string? mediaCategoryCode)
        {
            string sql = @$"
                        SELECT 
                        [ListingMedia].[ListingMediaId], 
                        [ListingMedia].[MediaUrl], 
                        [ListingMedia].[Title],
                        [ListingMedia].[MediaTypeCode],
                        [ListingMedia].[MediaCategoryCode], 
                        [ListingMedia].[ListingId], 
                        [MediaCategory].[Label] AS MediaCategoryLabel, 
                        [MediaType].[Label] AS MediaTypeLabel
                        FROM [listing].[ListingMedia] [ListingMedia]
                        INNER JOIN [listing].[Listing] ON [Listing].[ListingId] = [ListingMedia].[ListingId]
                        Inner Join [listing].[ListingType] ON [ListingType].[ListingTypeId] = [Listing].[ListingTypeId]
                        LEFT JOIN [listing].[MediaCategory] ON [MediaCategory].[MediaCategoryCode] = [ListingMedia].[MediaCategoryCode]
                        LEFT JOIN [listing].[MediaType] ON [MediaType].[MediaTypeCode] = [ListingMedia].[MediaTypeCode]
                        WHERE [ListingMedia].[ListingId] IN ( @ListingId )
                        AND [ListingMedia].[Deleted] = 0 
                        {(!string.IsNullOrEmpty(mediaCategoryCode) ? "AND [ListingMedia].[MediaCategoryCode] = @mediaCategoryCode " : "")}";
            var result = new List<ListingMediaCDto>();
            sql += $" ORDER BY [ListingMedia].[ListingId], [ListingMedia].[SortOrder], [ListingMedia].[ListingMediaId] ";
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ListingId", string.Join(",", listingIds));
                command.AddArgument("mediaCategoryCode", mediaCategoryCode);
                result = await command.SelectMany<ListingMediaCDto>();
            }
            return result;
        }

        /// <summary>
        /// Get Listing Attribute Data
        /// </summary>
        /// <param name="listingId"></param>
        /// <param name="displayContainerCodesArray"></param>
        /// <param name="excludeAttributesWithNoData"></param>
        /// <param name="displayGroupCode"></param>
        /// <returns>Returns Display Sets with associated attributes for each. Any display sets with no attributes will be excluded.</returns>
        /// <exception cref="Exception"></exception>
        internal async Task<List<ListingDisplayContainerCDto>> GetAttributeDataAsync(Int64 listingId, string[]? displayContainerCodesArray, bool? excludeAttributesWithNoData = true, string? displayGroupCode = "")
        {

            if (string.IsNullOrEmpty(displayGroupCode))
            {
                throw new ArgumentException($"'{nameof(displayGroupCode)}' cannot be null or empty.", nameof(displayGroupCode));
            }

            List<ListingDisplayContainerCDto> DisplayContainers = new List<ListingDisplayContainerCDto>();
            string displayContainerCodesString = displayContainerCodesArray != null ? string.Join(",", displayContainerCodesArray) : "";
            if (!string.IsNullOrEmpty(displayGroupCode) || displayContainerCodesArray?.Length == 0)
            {
                DisplayContainers = await GetDisplayContainers(displayGroupCode);
            }
            if (displayContainerCodesArray?.Length > 0)
            {
                foreach (var dcc in displayContainerCodesArray)
                {
                    var DisplayContainer = await GetDisplayContainer(dcc);
                    if (DisplayContainer.Enabled == true && !DisplayContainers.Exists(x => x.DisplayContainerCode == DisplayContainer.DisplayContainerCode))
                    {
                        DisplayContainers.Add(DisplayContainer);
                    }
                }
            }

            string sql = @$"
                        SELECT 
                        [ListingAttribute].[ListingAttributeId], 
                        [ListingAttribute].[AttributeCode], 
                        [ListingAttribute].[ValueString] AS ValueString,
                        [ListingAttribute].[ValueStringMax] AS ValueStringMax,
                        [ListingAttribute].[ValueNumeric] AS ValueNumeric,
                        [ListingAttribute].[ValueDateTime] AS ValueDateTime,
                        [ListingAttribute].[ValueGeography].[Lat] AS Latitude,
                        [ListingAttribute].[ValueGeography].[Long] AS Longitude,
                        [DisplayContainerAttribute].[DisplayContainerAttributeId],
                        [DisplayContainerAttribute].[DisplayContainerCode],
                        [DisplayContainerAttribute].[IsReadOnly],
                        [Attribute].[Label],
                        [Attribute].[SortOrder],
                        [Attribute].[AttributeGroupCode], 
                        [Attribute].[AttributeValueTypeCode],
                        [Attribute].[Icon],
                        [Attribute].[ReadAccessClaim],
                        [Attribute].[WriteAccessClaim],
                        [Attribute].[IsManyAllowed],
                        [Attribute].[NumericDecimalPlaces],
                        CASE 
                            WHEN [DisplayContainerAttribute].[InputTypeCode] IS NULL THEN [Attribute].[InputTypeCode]
                            ELSE [DisplayContainerAttribute].[InputTypeCode]
                        END as [InputTypeCode]
                        FROM [listing].[DisplayContainerAttribute] [DisplayContainerAttribute]
                        INNER JOIN [listing].[Attribute] ON [DisplayContainerAttribute].[AttributeCode] = [Attribute].[AttributeCode]
                        {(excludeAttributesWithNoData == true ? "INNER JOIN" : "LEFT JOIN")} 
                        [listing].[ListingAttribute] [ListingAttribute] ON [ListingAttribute].[AttributeCode] = [Attribute].[AttributeCode]
                        WHERE [DisplayContainerAttribute].[Deleted] = 0 AND [Attribute].[IsEnabled] = 1
                        AND [ListingAttribute].[ListingId] = @ListingId
                        {(displayContainerCodesArray?.Length > 0 ? "AND [DisplayContainerAttribute].[DisplayContainerCode] in (@DisplayContainerCodes)" : "")}
                        ORDER BY [DisplayContainerAttribute].[SortOrder], [DisplayContainerAttribute].[DisplayContainerCode], [Attribute].[SortOrder], [Attribute].[Label] ";


            var data = new List<ListingDataCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ListingId", listingId);
                if (displayContainerCodesArray?.Length > 0)
                {
                    command.AddArgument("DisplayContainerCodes", displayContainerCodesArray.ToList<string>());
                }
                using (var reader = await command.SelectRaw())
                {
                    while (reader.Read())
                    {
                        ListingDataCDto rec = new ListingDataCDto();
                        rec.ListingAttributeId = (reader.GetInt64(reader.GetOrdinal("ListingAttributeId")));
                        rec.AttributeCode = (reader.GetString(reader.GetOrdinal("AttributeCode")));
                        rec.ValueString = !reader.IsDBNull(reader.GetOrdinal("ValueString")) ? (reader.GetString(reader.GetOrdinal("ValueString"))) : null;
                        rec.ValueStringMax = !reader.IsDBNull(reader.GetOrdinal("ValueStringMax")) ? (reader.GetString(reader.GetOrdinal("ValueStringMax"))) : null;
                        rec.ValueNumeric = !reader.IsDBNull(reader.GetOrdinal("ValueNumeric")) ? (reader.GetDecimal(reader.GetOrdinal("ValueNumeric"))) : null;
                        rec.ValueDateTime = !reader.IsDBNull(reader.GetOrdinal("ValueDateTime")) ? (reader.GetDateTimeOffset(reader.GetOrdinal("ValueDateTime"))) : null;
                        rec.NumericDecimalPlaces = !reader.IsDBNull(reader.GetOrdinal("NumericDecimalPlaces")) ? (reader.GetByte(reader.GetOrdinal("NumericDecimalPlaces"))) : null;
                        rec.DisplayContainerAttributeId = (reader.GetInt32(reader.GetOrdinal("DisplayContainerAttributeId")));
                        rec.DisplayContainerCode = (reader.GetString(reader.GetOrdinal("DisplayContainerCode")));
                        rec.IsReadOnly = !reader.IsDBNull(reader.GetOrdinal("IsReadOnly")) ? (reader.GetBoolean(reader.GetOrdinal("IsReadOnly"))) : false;
                        rec.Label = !reader.IsDBNull(reader.GetOrdinal("Label")) ? (reader.GetString(reader.GetOrdinal("Label"))) : null;
                        rec.SortOrder = !reader.IsDBNull(reader.GetOrdinal("SortOrder")) ? (reader.GetInt16(reader.GetOrdinal("SortOrder"))) : null;
                        rec.AttributeGroupCode = !reader.IsDBNull(reader.GetOrdinal("AttributeGroupCode")) ? (reader.GetString(reader.GetOrdinal("AttributeGroupCode"))) : null;
                        rec.AttributeValueTypeCode = (reader.GetString(reader.GetOrdinal("AttributeValueTypeCode")));
                        rec.Icon = !reader.IsDBNull(reader.GetOrdinal("Icon")) ? (reader.GetString(reader.GetOrdinal("Icon"))) : null;
                        rec.InputTypeCode = !reader.IsDBNull(reader.GetOrdinal("InputTypeCode")) ? (reader.GetString(reader.GetOrdinal("InputTypeCode"))) : null;
                        rec.ReadAccessClaim = !reader.IsDBNull(reader.GetOrdinal("ReadAccessClaim")) ? (reader.GetString(reader.GetOrdinal("ReadAccessClaim"))) : null;
                        rec.WriteAccessClaim = !reader.IsDBNull(reader.GetOrdinal("WriteAccessClaim")) ? (reader.GetString(reader.GetOrdinal("WriteAccessClaim"))) : null;
                        rec.IsManyAllowed = !reader.IsDBNull(reader.GetOrdinal("IsManyAllowed")) ? (reader.GetBoolean(reader.GetOrdinal("IsManyAllowed"))) : false;
                        if (!reader.IsDBNull(reader.GetOrdinal("Latitude")) && !reader.IsDBNull(reader.GetOrdinal("Longitude")))
                        {
                            rec.ValueGeography = new GpslocationCDto()
                            {
                                Latitude = (decimal)reader.GetDouble(reader.GetOrdinal("Latitude"))
                                                                     ,
                                Longitude = (decimal)reader.GetDouble(reader.GetOrdinal("Longitude"))
                            };
                        }

                        rec.ValueNumeric = Extensions.DecimalTrimPlaces(rec.ValueNumeric, rec.NumericDecimalPlaces);

                        data.Add(rec);
                    }
                }
            }

            if (data == null)
            {
                return null;
            }

            foreach (var rec in data)
            {
                var DisplayContainer = DisplayContainers.FirstOrDefault(x => x.DisplayContainerCode == rec.DisplayContainerCode);
                if (DisplayContainer != null)
                {
                    if (DisplayContainer.Attributes == null)
                    {
                        DisplayContainer.Attributes = new List<ListingDataCDto>();
                    }
                    DisplayContainer.Attributes.Add(rec);
                }
            }

            // Remove Display Sets that do not have any attributes
            for (int i = DisplayContainers.Count - 1; i >= 0; i--)
            {
                if (DisplayContainers[i].Attributes == null || DisplayContainers[i]?.Attributes?.Count == 0)
                {
                    DisplayContainers.RemoveAt(i);
                }
            }

            return DisplayContainers;
        }



#pragma warning disable CS1998 // Async method lacks 'await' operators and will run synchronously
        internal async Task ValidateQueryModel(ListingQueryModelDto queryModel, bool includeMedia = false, bool includeAttributes = false, bool mustHaveDisplayContainer = false)
#pragma warning restore CS1998 // Async method lacks 'await' operators and will run synchronously
        {
            if (queryModel == null)
            {
                throw new ApiErrorException($"queryModel must not be null. ");
            }

            if ((queryModel.DisplayContainerCodesArray == null || queryModel.DisplayContainerCodesArray.Length == 0) && mustHaveDisplayContainer)
            {
                throw new ApiErrorException($"A displayContainerCode must be provided when fetching filters. ");
            }

            if (queryModel.SearchDistanceKm > 0 && queryModel.FromLocation == null)
            {
                throw new ApiErrorException($"fromLocationLat and fromLocationFrom are required when providing searchDistanceKm parameter. ");
            }

            if (queryModel.AvailableToDateTime != null)
            {
                if (queryModel.AvailableFromDateTime == null)
                {
                    queryModel.AvailableFromDateTime = DateTimeOffset.UtcNow;
                }

                if (queryModel.AvailableFromDateTime > queryModel.AvailableToDateTime)
                {
                    throw new ApiErrorException($"availableFromDateTime must be before availableToDateTime parameter. ");
                }
            }
            else
            {
                if (queryModel.AvailableFromDateTime != null)
                {
                    throw new ApiErrorException($"availableToDateTime is required when providing availableFromDateTime parameter. ");

                }
            }
        }

        /// <summary>
        /// Get List of Listings
        /// Supports full filtering
        /// Supports returning media and attribute data.
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="includeMedia">Default true. Returns the media url's and titles.</param>
        /// <param name="mediaCategoryCode">when specified only returns media with this CategoryCode. Otherwise returns all for the listing</param>
        /// <param name="includeAttributes">Default false. When true each listing row will include any attributes that are part of a requested DisplayContainer</param>
        /// <param name="includeTenantConnectorOnConnectorListing"></param>
        /// <param name="includeTagsInResponse"></param>
        /// <param name="includeTagsInResponseCount"></param>
        /// <returns></returns>
        internal async Task<ListResponseDto<ListingWithDataAndMediaCDto>> GetListAsync(ListingQueryModelDto queryModel, bool includeMedia = true, string? mediaCategoryCode = null, bool includeAttributes = false, bool includeTenantConnectorOnConnectorListing = false, bool includeTagsInResponse = true, short includeTagsInResponseCount = 5)
        {
            (string sqlFrom, string sqlJoin, string sqlWhere, string sqlOrder, string preSelect, string extraCols) = await BuildQuery(queryModel);
            bool attributesInMainSelect = false;
            bool includeMediaInMainSelect = includeMedia;
            List<ListingDisplayContainerCDto> DisplayContainers = new List<ListingDisplayContainerCDto>();


            List<ListingDataCDto> attrs = new List<ListingDataCDto>();
            if (includeAttributes == true)
            {
                if (!string.IsNullOrEmpty(queryModel.DisplayGroupCode) || queryModel.DisplayContainerCodesArray == null || queryModel.DisplayContainerCodesArray.Length == 0)
                {
                    DisplayContainers = await GetDisplayContainers(queryModel.DisplayGroupCode);
                }
                if (queryModel.DisplayContainerCodesArray?.Length > 0)
                {
                    foreach (var dcc in queryModel.DisplayContainerCodesArray)
                    {
                        var DisplayContainer = await GetDisplayContainer(dcc);
                        if (DisplayContainer.Enabled == true && !DisplayContainers.Exists(x => x.DisplayContainerCode == DisplayContainer.DisplayContainerCode))
                        {
                            DisplayContainers.Add(DisplayContainer);
                        }
                    }
                }
            }
            if (DisplayContainers.Count > 0)
            {
                foreach (var dcc in DisplayContainers)
                {
                    if (dcc.DisplayContainerCode != null)
                    {
                        dcc.Attributes = await GetDisplayContainerAttributes(dcc.DisplayContainerCode);
                    }
                    if (dcc.Attributes != null)
                    {
                        attrs.AddRange(dcc.Attributes);
                    }
                }
                if (attrs.Count > 0 && attrs.Count <= 40)
                {
                    // Include the requested attributes (via Display Set) in the sql as Left Joins or Sub-Selects.
                    // Once the number exceeds 40 we instead fetch the attributes as a separate call.
                    int i = 0;
                    attributesInMainSelect = true;
                    foreach (var att in attrs)
                    {
                        i++;
                        if (att.AttributeCode != null)
                        {
                            if (att.IsManyAllowed == false)
                            {
                                if (att.AttributeValueTypeCode == "ValueGeography")
                                {
                                    extraCols += $",[Attr{i}].[{att.AttributeValueTypeCode}].[Lat] AS [Attr{att.AttributeCode}ValueLat] ";
                                    extraCols += $",[Attr{i}].[{att.AttributeValueTypeCode}].[Long] AS [Attr{att.AttributeCode}ValueLong] ";
                                }
                                else
                                {
                                    extraCols += $",[Attr{i}].[{att.AttributeValueTypeCode}] AS [Attr{att.AttributeCode}Value] ";
                                }
                                sqlJoin += $" LEFT JOIN [listing].[ListingAttribute] [Attr{i}] ON [Attr{i}].[ListingId] = [Listing].[ListingId] AND [Attr{i}].[AttributeCode] = @AttributeCode{i} ";
                            }
                            else
                            {
                                extraCols += $",STUFF((Select ',' + CAST(AA.[{att.AttributeValueTypeCode}] AS VARCHAR(MAX)) From [Listing].[ListingAttribute] [AA] Where [Listing].ListingId = AA.ListingId AND AA.[AttributeCode] = @AttributeCode{i}  FOR XML PATH('')),1,1,'') AS [Attr{att.AttributeCode}Value] ";
                            }
                            _queryParameters.Add($"AttributeCode{i}", att.AttributeCode);
                        }
                    }
                }
            }

            if (includeMediaInMainSelect)
            {
                extraCols += $",STUFF((Select '^' + LM.[MediaUrl] + '|' + LM.[Title] + '|' + LM.[MediaTypeCode] From [Listing].[ListingMedia] [LM] Where [Listing].ListingId = LM.ListingId AND LM.[Deleted] = 0 {(!string.IsNullOrEmpty(mediaCategoryCode) ? " AND LM.[MediaCategoryCode] = @MediaCategoryCode " : "")} ORDER By LM.[SortOrder]  FOR XML PATH('')),1,1,'') AS [MediaSet] ";
                if (!string.IsNullOrEmpty(mediaCategoryCode))
                {
                    _queryParameters.Add($"MediaCategoryCode", mediaCategoryCode);
                }
            }

            // Include Tags that have been linked to the Listing in the response.
            if (includeTagsInResponse)
            {
                // Get all the tags that linked to this listing.
                // The number of tags returned is 5 unless overriden in the query request
                // Only Tags that are enabled are returned (and if part of a TagSet the TagSet must also be enabled)
                extraCols += $",STUFF((Select TOP({includeTagsInResponseCount}) '^' + [Tag].[Label] + '|' + [Tag].[Icon] + '|' + [Tag].[Colour] + '|' + [Tag].[ImageUrl] + '|' + [Tag].[Note] + '|' + STR([Tag].[TagId]) + '|' + [TagSet].[Label] + '|' + [TagSet].[TagSetId] From [Listing].[ListingTag] [LT] INNER JOIN [Listing].[Tag] ON [Tag].[TagId] = LT.[TagId] AND [Tag].[IsEnabled] = 1 LEFT JOIN [Listing].[TagSet] ON [TagSet].[TagSetId] = [Tag].[TagSetId] Where [Listing].ListingId = LT.ListingId AND LT.[Deleted] = 0 AND ([Tag].TagSetId IS NULL OR [TagSet].[IsEnabled] = 1) ORDER By LT.[SortOrder]  FOR XML PATH('')),1,1,'') AS [ListingTags] ";
            }

            if (queryModel.ListingTypeId == 6 && includeTenantConnectorOnConnectorListing)
            {
                extraCols += $@", (CASE WHEN EXISTS (
	                               	SELECT 1
	                               	FROM [connector].[TenantConnector] [TenantConnector]
	                               	INNER JOIN 
	                               	    [listing].[ListingAttribute] [ListingAttribute]
	                               	ON  [ListingAttribute].[ListingId]=[Listing].[ListingId] 
	                               	AND [ListingAttribute].[AttributeCode]='ConnectorCode'
	                               	AND [ListingAttribute].[ValueString]=[TenantConnector].[ConnectorCode]
	                               	WHERE 
	                               		[TenantConnector].[StatusCode] != 'Expired' AND [TenantConnector].[TenantId] = @tenantId
	                               	)
	                               THEN CAST(1 AS BIT) ELSE CAST(0 AS BIT)
	                               END) AS [TenantConnectorExists]";
            }

            string sql = $@"
                            SELECT [Listing].[ListingId], 
                                    [Listing].[ReferenceNo],
                                    [Listing].[Subject], 
                                    [Listing].[StatusCode], 
                                    [Listing].[ListingTypeId],
                                    [Listing].[ProfileListingMediaId], 
                                    [ListingMedia].[MediaUrl] as ProfileListingMediaUrl,
                                    [Listing].[ParentEntityIntId],
                                    [Listing].[ParentEntityId],
                                    [Listing].[ParentEntityType],  
                                    [Listing].[ParentEntityId2],
                                    [Listing].[ParentEntityType2], 
                                    [Listing].[ParentListingId],
                                    [Listing].[SortOrder],
                                    [Listing].[Icon],
                                    [Listing].[FromDate], 
                                    [Listing].[ToDate],
                                    [Listing].[CreatedOn],
                                    [Listing].[CreatedByName],
                                    [Listing].[ModifiedOn],
                                    [Listing].[ModifiedByName],
                                    [Listing].[Description],
                                    [Listing].[GpsLocation].[Lat] AS [Latitude],
                                    [Listing].[GpsLocation].[Long] AS [Longitude]
                                    {extraCols}
                         {sqlFrom}
                         {sqlJoin}
                         {sqlWhere}
                         {sqlOrder}
";
            sql += $" OFFSET @offset ROWS";
            sql += $" FETCH NEXT @limit ROWS ONLY";
            var result = new ListResponseDto<ListingWithDataAndMediaCDto>() { List = new List<ListingWithDataAndMediaCDto>(), TotalNumOfRows = 0 };
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                if (_queryParameters.Count > 0)
                {
                    foreach (var qp in _queryParameters)
                    {
                        command.AddArgument(qp.Key, qp.Value);
                    }
                }
                command.AddArgument("offset", queryModel.StandardListParameters?.Offset);
                command.AddArgument("limit", queryModel.StandardListParameters?.Limit);
         

                using (var reader = await command.SelectRaw())
                {
                    while (reader.Read())
                    {
                        ListingWithDataAndMediaCDto rec = new ListingWithDataAndMediaCDto();
                        rec.ListingId = (reader.GetInt64(reader.GetOrdinal("ListingId")));
                        rec.ListingTypeId = (reader.GetByte(reader.GetOrdinal("ListingTypeId")));
                        rec.ReferenceNo = (reader.GetString(reader.GetOrdinal("ReferenceNo")));
                        rec.Subject = (reader.GetString(reader.GetOrdinal("Subject")));
                        rec.StatusCode = (reader.GetString(reader.GetOrdinal("StatusCode")));
                        rec.ProfileListingMediaId = !reader.IsDBNull(reader.GetOrdinal("ProfileListingMediaId")) ? (reader.GetInt64(reader.GetOrdinal("ProfileListingMediaId"))) : null;
                        rec.ProfileListingMediaUrl = !reader.IsDBNull(reader.GetOrdinal("ProfileListingMediaUrl")) ? (reader.GetString(reader.GetOrdinal("ProfileListingMediaUrl"))) : null;
                        rec.ParentEntityIntId = !reader.IsDBNull(reader.GetOrdinal("ParentEntityIntId")) ? (reader.GetInt64(reader.GetOrdinal("ParentEntityIntId"))) : null;
                        rec.ParentEntityId = !reader.IsDBNull(reader.GetOrdinal("ParentEntityId")) ? (reader.GetGuid(reader.GetOrdinal("ParentEntityId"))) : null;
                        rec.ParentEntityType = !reader.IsDBNull(reader.GetOrdinal("ParentEntityType")) ? (reader.GetString(reader.GetOrdinal("ParentEntityType"))) : null;
                        rec.ParentEntityId2 = !reader.IsDBNull(reader.GetOrdinal("ParentEntityId2")) ? (reader.GetGuid(reader.GetOrdinal("ParentEntityId2"))) : null;
                        rec.ParentEntityType2 = !reader.IsDBNull(reader.GetOrdinal("ParentEntityType2")) ? (reader.GetString(reader.GetOrdinal("ParentEntityType2"))) : null;
                        rec.ParentListingId = !reader.IsDBNull(reader.GetOrdinal("ParentListingId")) ? (reader.GetInt64(reader.GetOrdinal("ParentListingId"))) : null;
                        rec.Icon = !reader.IsDBNull(reader.GetOrdinal("Icon")) ? (reader.GetString(reader.GetOrdinal("Icon"))) : null;
                        rec.SortOrder = !reader.IsDBNull(reader.GetOrdinal("SortOrder")) ? (reader.GetInt32(reader.GetOrdinal("SortOrder"))) : null;
                        rec.FromDate = (reader.GetDateTimeOffset(reader.GetOrdinal("FromDate")));
                        rec.ToDate = !reader.IsDBNull(reader.GetOrdinal("ToDate")) ? (reader.GetDateTimeOffset(reader.GetOrdinal("ToDate"))) : null;
                        rec.CreatedOn = (reader.GetDateTimeOffset(reader.GetOrdinal("CreatedOn")));
                        rec.CreatedByName = !reader.IsDBNull(reader.GetOrdinal("CreatedByName")) ? (reader.GetString(reader.GetOrdinal("CreatedByName"))) : null;
                        rec.ModifiedOn = !reader.IsDBNull(reader.GetOrdinal("ModifiedOn")) ? (reader.GetDateTimeOffset(reader.GetOrdinal("ModifiedOn"))) : null;
                        rec.ModifiedByName = !reader.IsDBNull(reader.GetOrdinal("ModifiedByName")) ? (reader.GetString(reader.GetOrdinal("ModifiedByName"))) : null;
                        rec.Description = !reader.IsDBNull(reader.GetOrdinal("Description")) ? (reader.GetString(reader.GetOrdinal("Description"))) : null;
                        rec.BookingItemId = !reader.IsDBNull(reader.GetOrdinal("BookingItemId")) ? (reader.GetInt32(reader.GetOrdinal("BookingItemId"))) : null;
                        if (!reader.IsDBNull(reader.GetOrdinal("Latitude")) && !reader.IsDBNull(reader.GetOrdinal("Longitude")))
                        {
                            rec.GpsLocation = new GpslocationCDto()
                            {
                                Latitude = (decimal)reader.GetDouble(reader.GetOrdinal("Latitude"))
                                                                     ,
                                Longitude = (decimal)reader.GetDouble(reader.GetOrdinal("Longitude"))
                            };
                        }

                        if (queryModel.SearchDistanceKm > 0)
                        {
                            rec.DistanceAwayInKm = !reader.IsDBNull(reader.GetOrdinal("DistanceAwayMeters")) ? (decimal)(reader.GetDouble(reader.GetOrdinal("DistanceAwayMeters"))) / 1000 : null;
                        }

                        if (includeMediaInMainSelect)
                        {
                            // ListingMedia was included in the select concatenated into a single result per Listing Row. Split out to rows in result.
                            rec.Media = new List<ListingMediaCDto>();
                            string? mediaSet = !reader.IsDBNull(reader.GetOrdinal("MediaSet")) ? (reader.GetString(reader.GetOrdinal("MediaSet"))) : null;
                            if (mediaSet != null)
                            {
                                var mediaRecs = mediaSet.Split('^');
                                foreach (var m in mediaRecs)
                                {
                                    var recSplit = m.Split('|');
                                    rec.Media.Add(new ListingMediaCDto() { Title = recSplit[1], MediaUrl = recSplit[0], MediaTypeCode = recSplit[2] });
                                }
                            }

                        }

                        if (includeTagsInResponse)
                        {
                            // ListingTags was included in the select concatenated into a single result per Listing Row. Split out to rows in result.
                            rec.Tags = new List<ListingTagCDto>();
                            string? listingTags = !reader.IsDBNull(reader.GetOrdinal("ListingTags")) ? (reader.GetString(reader.GetOrdinal("ListingTags"))) : null;
                            if (!string.IsNullOrEmpty(listingTags))
                            {
                                var tagRecs = listingTags.Split('^');
                                foreach (var t in tagRecs)
                                {
                                    var recSplit = t.Split('|');
                                    var tag = new ListingTagCDto() { TagLabel = recSplit[0], TagIcon = recSplit[1], TagColour = recSplit[2], TagImageUrl = recSplit[3], TagNote = recSplit[4], TagId = Convert.ToInt32(recSplit[5]), TagSetLabel = recSplit[6], TagSetId = Convert.ToInt32(recSplit[7]) };
                                    if (tag.TagSetId == 0) { tag.TagSetId = null; }
                                    rec.Tags.Add(tag);
                                }
                            }

                        }

                        if (attributesInMainSelect)
                        {
                            if (rec.Attributes == null)
                            {
                                rec.Attributes = new List<ListingDisplayContainerCDto>();
                            }
                            // Attributes were included in the main listing select with each attribute read as its own column.
                            rec.Attributes.AddRange(DisplayContainers.DeepClone());
                            int i = 0;
                            foreach (var att in attrs)
                            {
                                i++;
                                var theAttrRec = rec.Attributes.FirstOrDefault(x => x.Attributes != null && x.Attributes.Exists(z => z.AttributeCode == att.AttributeCode))?.Attributes?.FirstOrDefault(x => x.AttributeCode == att.AttributeCode);
                                if (theAttrRec != null)
                                {
                                    switch (att.AttributeValueTypeCode)
                                    {
                                        case "ValueString":
                                            theAttrRec.ValueString = !reader.IsDBNull(reader.GetOrdinal($"Attr{att.AttributeCode}Value")) ? (reader.GetString(reader.GetOrdinal($"Attr{att.AttributeCode}Value"))) : null;
                                            break;
                                        case "ValueStringMax":
                                            theAttrRec.ValueStringMax = !reader.IsDBNull(reader.GetOrdinal($"Attr{att.AttributeCode}Value")) ? (reader.GetString(reader.GetOrdinal($"Attr{att.AttributeCode}Value"))) : null;
                                            break;
                                        case "ValueNumeric":
                                            var stringNumeric = !reader.IsDBNull(reader.GetOrdinal($"Attr{att.AttributeCode}Value")) ? (reader.GetString(reader.GetOrdinal($"Attr{att.AttributeCode}Value"))) : null;
                                            if (!string.IsNullOrEmpty(stringNumeric))
                                            {
                                                if (Decimal.TryParse(stringNumeric, out decimal numeric))
                                                {
                                                    theAttrRec.ValueNumeric = Extensions.DecimalTrimPlaces(numeric, theAttrRec.NumericDecimalPlaces);
                                                }
                                            }
                                            theAttrRec.ValueNumeric = theAttrRec.ValueNumeric ?? 0;
                                            break;
                                        case "ValueDateTime":
                                            theAttrRec.ValueDateTime = !reader.IsDBNull(reader.GetOrdinal($"Attr{att.AttributeCode}Value")) ? (reader.GetDateTimeOffset(reader.GetOrdinal($"Attr{att.AttributeCode}Value"))) : null;
                                            break;
                                        case "ValueGeography":
                                            if (!reader.IsDBNull(reader.GetOrdinal($"Attr{att.AttributeCode}ValueLat")) && !reader.IsDBNull(reader.GetOrdinal($"Attr{att.AttributeCode}ValueLong")))
                                            {
                                                theAttrRec.ValueGeography = new GpslocationCDto()
                                                {
                                                    Latitude = (decimal)reader.GetDouble(reader.GetOrdinal($"Attr{att.AttributeCode}ValueLat"))
                                                                                                   ,
                                                    Longitude = (decimal)reader.GetDouble(reader.GetOrdinal($"Attr{att.AttributeCode}ValueLong"))
                                                };
                                            }
                                            break;
                                        default:
                                            break;
                                    }
                                }
                            }

                            if (queryModel.ListingTypeId == 6 && includeTenantConnectorOnConnectorListing)
                            {
                                var listingDetails = rec.Attributes.Find(a => a.DisplayContainerCode == "ListingDetail");
                                bool connected = !reader.IsDBNull(reader.GetOrdinal("TenantConnectorExists")) && reader.GetBoolean(reader.GetOrdinal("TenantConnectorExists"));
                                if(connected && listingDetails != null)
                                {
                                    if (listingDetails.Attributes == null) { listingDetails.Attributes = []; }
                                    listingDetails.Attributes.Add(new ListingDataCDto
                                    {
                                        AttributeCode = "CardPill",
                                        ValueString = "Connected",
                                        DisplayContainerCode = "ListingDetail",
                                        AttributeValueTypeCode = "ValueString",
                                    });
                                }
                            }
                        }
                        rec.IsUserFavourite = await _favourite.IsListingFavouriteForLoggedInUser(rec.ListingId);
                        result.List.Add(rec);
                    }
                }
            }

            if (includeMedia && !includeMediaInMainSelect && result.List?.Count > 0)
            {
                // Get all media records for the listings been returned. IF not already done in the main listing select
                var medias = await GetListingMediaForManyListings(result.List.Select(x => x.ListingId).ToList(), mediaCategoryCode);
                foreach (var rec in result.List)
                {
                    if (rec.Media == null) { rec.Media = []; }
                    var toAdd = medias.Where(x => x.ListingId == rec.ListingId).ToList();
                    if (toAdd?.Count > 0)
                    {
                        rec.Media.AddRange(toAdd);
                    }
                }
            }

            if (includeAttributes && !attributesInMainSelect && result.List?.Count > 0)
            {
                // Get all required attributes for the listings been returned. IF not already done in the main listing select
                foreach (var rec in result.List)
                {
                    rec.Attributes = await GetAttributeDataAsync(rec.ListingId, queryModel.DisplayContainerCodesArray, excludeAttributesWithNoData: true, displayGroupCode: queryModel.DisplayGroupCode);
                }
            }

            sql = $@"   SELECT COUNT(*) AS totalNumOfRows
                         {sqlFrom}
                         {sqlJoin}
                         {sqlWhere}";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                if (_queryParameters.Count > 0)
                {
                    foreach (var qp in _queryParameters)
                    {
                        command.AddArgument(qp.Key, qp.Value);
                    }
                }

                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }
            return result;
        }

        internal ListResponseDto<ListingWithDataAndMediaCDto> FlattenAttributesAndReturnAsDictionaryOfFields(ListResponseDto<ListingWithDataAndMediaCDto> responseDto)
        {
            if (responseDto.List == null) { return responseDto; }

            foreach (var liRec in responseDto.List)
            {
                FlattenAttributesAndReturnAsDictionaryOfFields(liRec);
            }
            return responseDto;
        }

        internal void FlattenAttributesAndReturnAsDictionaryOfFields(ListingWithDataAndMediaCDto liRec)
        {
            if (liRec == null) { return; }

            liRec.Fields = new Dictionary<string, object?>();
            if (liRec.Attributes != null)
            {
                foreach (var displayContainer in liRec.Attributes)
                {
                    if (displayContainer != null && displayContainer.Attributes != null)
                    {
                        foreach (var att in displayContainer.Attributes)
                        {
                            if (att != null && att.AttributeCode != null)
                            {
                                string attName = $"{Char.ToLowerInvariant(att.AttributeCode[0]) + att.AttributeCode.Substring(1)}";
                                object? val = null;
                                switch (att.AttributeValueTypeCode)
                                {
                                    case "ValueString":
                                        val = att.ValueString;
                                        break;
                                    case "ValueStringMax":
                                        val = att.ValueStringMax;
                                        break;
                                    case "ValueNumeric":
                                        val = att.ValueNumeric;
                                        break;
                                    case "ValueDateTime":
                                        val = att.ValueDateTime;
                                        break;
                                    case "ValueGeography":
                                        val = att.ValueGeography;
                                        break;
                                    default:
                                        break;
                                }
                                if (!liRec.Fields.ContainsKey(attName))
                                {
                                    liRec.Fields.Add(attName, val);
                                }
                            }
                        }
                    }
                }
                liRec.Attributes = null; // Don't return the nested display container and attributes
            }
        }

        internal async Task<(string, string, string, string, string, string)> BuildQuery(ListingQueryModelDto queryModel, bool bypassSort = false, int? tenantId = null)
        {
            _queryParameters = new Dictionary<string, object>();
            string sqlWhere = "WHERE [Listing].[Deleted] = 0 AND [Listing].[VisibilityId] = @visibilityId AND [listing].[ListingTypeId] = @listingTypeId ";
            _queryParameters.Add("visibilityId", queryModel.Visibility);
            _queryParameters.Add("listingTypeId", queryModel.ListingTypeId);
            string sqlFrom = "FROM [listing].[Listing] [Listing] ";
            string sqlJoin = $@" LEFT JOIN [Listing].[ListingMedia] [ListingMedia] ON [ListingMedia].[ListingMediaId] = [Listing].[ProfileListingMediaId] AND [ListingMedia].[Deleted] = 0 
                                 Inner Join [listing].[ListingType] ON [ListingType].[ListingTypeId] = [Listing].[ListingTypeId]" + 
                                    (tenantId != null ? "AND([ListingType].[IsTenantBased] = 0 OR[Listing].[TenantId] = @TenantId)" : "");
            string sqlOrder = "";
            string preSelect = "";
            string extraCols = "";

            // Check user has required Claim if VisibilityClaimId is set
            if (ConfigBase.Schemas.ContainsKey("users"))
            {
                sqlWhere += " AND ([Listing].[VisibilityClaimId] IS NULL OR EXISTS (SELECT 1 FROM [users].[UserClaim] UC WHERE UC.ClaimId = [Listing].[VisibilityClaimId])) ";
            }

            if (tenantId != null)
            {
                _queryParameters.Add("TenantId", tenantId);
            }

            if (queryModel.ParentListingId != null)
            {
                sqlWhere += " AND [Listing].[ParentListingId] = @ParentListingId ";
                _queryParameters.Add("ParentListingId", queryModel.ParentListingId);
            }
            if (!string.IsNullOrEmpty(queryModel.Subject))
            {
                sqlWhere += " AND [Listing].[Subject] like '%' + @Subject + '%'";
                _queryParameters.Add("Subject", queryModel.Subject);
            }
            if (!string.IsNullOrEmpty(queryModel.Description))
            {
                sqlWhere += " AND [Listing].[Description] like '%' + @Description + '%'";
                _queryParameters.Add("Description", queryModel.Description);
            }
            if (!string.IsNullOrEmpty(queryModel.StatusCode))
            {
                sqlWhere += " AND [Listing].[statusCode] = @StatusCode";
                _queryParameters.Add("StatusCode", queryModel.StatusCode);
            }
            if (queryModel.BeforeDate != null)
            {
                sqlWhere += " AND [Listing].[FromDate] < @BeforeDate";
                _queryParameters.Add("BeforeDate", queryModel.BeforeDate);
            }
            if (queryModel.AfterDate != null)
            {
                sqlWhere += " AND [Listing].[ToDate] > @AfterDate";
                _queryParameters.Add("AfterDate", queryModel.AfterDate);
            }
            if (queryModel.AvailableFromDateTime != null && queryModel.AvailableToDateTime != null)
            {
                // Check listing (booking item) is available to be booked in this period
                sqlJoin += @$"INNER JOIN [booking].[BookingItem] ON [BookingItem].[ParentEntityIntId] = [Listing].[ListingId] AND [BookingItem].[ParentEntityType] = 'Listing' AND [BookingItem].[Deleted] = 0 AND [BookingItem].[IsEnabled] = 1 ";
                extraCols += ",[BookingItem].[BookingItemId] ";

                if (queryModel.PickupFromHoldingLocationId != null)
                {
                    sqlWhere += @$" AND EXISTS (SELECT 1 FROM [booking].[BookingItemHoldingLocation]
                                      WHERE [BookingItemHoldingLocation].[Deleted] = 0
                                      AND [BookingItemHoldingLocation].[BookingItemId] = [BookingItem].[BookingItemId]
                                      AND [BookingItemHoldingLocation].[FromDateTime] <= @AvailableFromDateTime
                                      AND ([BookingItemHoldingLocation].[ToDateTime] IS NULL OR [BookingItemHoldingLocation].[ToDateTime] > @AvailableToDateTime ) ) ";
                }

                sqlWhere += @$" AND NOT EXISTS (SELECT 1 FROM [booking].[BookingRecordItem] 
                                    INNER JOIN [booking].[BookingRecord] ON [BookingRecord].[BookingRecordId] = [BookingRecordItem].[BookingRecordId] AND [BookingRecord].[Deleted] = 0 AND [BookingRecord].[BookingRecordStatusId] != {(short)BookingRecordStatusEnum.Cancelled}
                                    WHERE [BookingRecordItem].[Deleted] = 0
                                      AND [BookingRecordItem].[BookingItemId] = [BookingItem].[BookingItemId]
                                      AND [BookingRecord].[FromDateTime] <= @AvailableToDateTime
                                      AND [BookingRecord].[ToDateTime] > @AvailableFromDateTime ) ";
                // Now check Not Available Rows for the Booking Item
                var dayOfWeek = queryModel.AvailableFromDateTime.Value.ToString("dddd");
                sqlWhere += @$" AND NOT EXISTS (SELECT 1 FROM [booking].[BookingItemAvailability] 
                                    WHERE [BookingItemAvailability].[Deleted] = 0
                                      AND [BookingItemAvailability].[BookingItemId] = [BookingItem].[BookingItemId]
                                      AND [BookingItemAvailability].[AvailabilityModeId] = 0
                                      AND ([BookingItemAvailability].[FromDate] is NULL 
                                           OR [BookingItemAvailability].[FromDate] <= @AvailableToDateTime)
                                      AND ([BookingItemAvailability].[ToDate] is NULL 
                                           OR [BookingItemAvailability].[ToDate] > @AvailableFromDateTime) 
                                      AND ([BookingItemAvailability].[FromTime] IS NULL
                                           OR [BookingItemAvailability].[FromTime] <= CAST(@AvailableToDateTime AS TIME))
                                      AND ([BookingItemAvailability].[ToTime] IS NULL
                                           OR [BookingItemAvailability].[ToTime] > CAST(@AvailableFromDateTime AS TIME))
                                      AND Include{dayOfWeek} = 1
                                        ) ";
                // Now check Available Rows for the Booking Item
                // If there are no Availability Rows then Booking Item is always available
                sqlWhere += @$" AND (EXISTS (SELECT 1 FROM [booking].[BookingItemAvailability] 
                                    WHERE [BookingItemAvailability].[Deleted] = 0
                                      AND [BookingItemAvailability].[BookingItemId] = [BookingItem].[BookingItemId]
                                      AND [BookingItemAvailability].[AvailabilityModeId] = 1
                                      AND ([BookingItemAvailability].[FromDate] is NULL 
                                           OR [BookingItemAvailability].[FromDate] <= @AvailableToDateTime)
                                      AND ([BookingItemAvailability].[ToDate] is NULL 
                                           OR [BookingItemAvailability].[ToDate] > @AvailableFromDateTime) 
                                      AND ([BookingItemAvailability].[FromTime] IS NULL
                                           OR [BookingItemAvailability].[FromTime] <= CAST(@AvailableToDateTime AS TIME))
                                      AND ([BookingItemAvailability].[ToTime] IS NULL
                                           OR [BookingItemAvailability].[ToTime] > CAST(@AvailableFromDateTime AS TIME))
                                      AND Include{dayOfWeek} = 1
                                        ) 
                                      OR NOT EXISTS (SELECT 1 FROM [booking].[BookingItemAvailability] 
                                    WHERE [BookingItemAvailability].[Deleted] = 0
                                      AND [BookingItemAvailability].[BookingItemId] = [BookingItem].[BookingItemId]
                                      AND [BookingItemAvailability].[AvailabilityModeId] = 1 ) ) ";

                _queryParameters.Add("AvailableToDateTime", queryModel.AvailableToDateTime);
                _queryParameters.Add("AvailableFromDateTime", queryModel.AvailableFromDateTime);
            }
            else
            {
                extraCols += ",NULL AS [BookingItemId] ";
            }

            if (queryModel.ParentEntityId != null)
            {
                _queryParameters.Add("ParentEntityId", queryModel.ParentEntityId);
                if (string.IsNullOrEmpty(queryModel.ParentEntityType))
                {
                    sqlWhere += " AND ([Listing].[ParentEntityId] = @ParentEntityId OR [Listing].[ParentEntityId2] = @ParentEntityId)";
                }
                else
                {
                    sqlWhere += " AND (([Listing].[ParentEntityId] = @ParentEntityId AND [Listing].[ParentEntityType] = @ParentEntityType) OR ([Listing].[ParentEntityId2] = @ParentEntityId AND [Listing].[ParentEntityType2] = @ParentEntityType))";
                    _queryParameters.Add("ParentEntityType", queryModel.ParentEntityType);
                }
            }
            else if (queryModel.ParentEntityIntId != null)
            {
                _queryParameters.Add("ParentEntityIntId", queryModel.ParentEntityIntId);
                if (string.IsNullOrEmpty(queryModel.ParentEntityType))
                {
                    sqlWhere += " AND ([Listing].[ParentEntityIntId] = @ParentEntityIntId)";
                }
                else
                {
                    sqlWhere += " AND ([Listing].[ParentEntityIntId] = @ParentEntityIntId AND [Listing].[ParentEntityType] = @ParentEntityType)";
                    _queryParameters.Add("ParentEntityType", queryModel.ParentEntityType);
                }
            }
            else
            {
                if (!string.IsNullOrEmpty(queryModel.ParentEntityType))
                {
                    sqlWhere += " AND ([Listing].[ParentEntityType] = @ParentEntityType OR [Listing].[ParentEntityType2] = @ParentEntityType)";
                    _queryParameters.Add("ParentEntityType", queryModel.ParentEntityType);
                }
            }

            if (queryModel.SearchDistanceKm > 0)
            {
                if (queryModel.FromLocation == null)
                {
                    throw new ApiErrorException($"fromLocationLat and fromLocationFrom are required when providing searchDistanceKm parameter. ");
                }
                _queryParameters.Add("fromLat", queryModel.FromLocation.Latitude);
                _queryParameters.Add("fromLong", queryModel.FromLocation.Longitude);
                _queryParameters.Add("searchDistanceMeters", queryModel.SearchDistanceKm * 1000);

                sqlWhere += " AND geography::STGeomFromText('POINT(' + CAST(@fromLong AS VARCHAR(MAX)) + ' ' + CAST(@fromLat AS VARCHAR(MAX)) + ')', 4326).STDistance([Listing].[GpsLocation]) < @searchDistanceMeters ";
                extraCols += ", geography::STGeomFromText('POINT(' + CAST(@fromLong AS VARCHAR(MAX)) + ' ' + CAST(@fromLat AS VARCHAR(MAX)) + ')', 4326).STDistance([Listing].[GpsLocation]) AS [DistanceAwayMeters]";
            }

            if (queryModel.IncludeTagIdsArray != null && queryModel.IncludeTagIdsArray.Length > 0)
            {
                sqlWhere += @$" AND 1 = (Select 1 From [listing].[ListingTag] Where [ListingTag].[ListingId] = [Listing].[ListingId] 
                                                AND [ListingTag].[TagId] in (@IncludeTagIds)  AND [ListingTag].[Deleted] = 0 )";
                _queryParameters.Add("IncludeTagIds", queryModel.IncludeTagIdsArray.ToList());
            }

            if (queryModel.ExcludeTagIdsArray != null && queryModel.ExcludeTagIdsArray.Length > 0)
            {
                sqlWhere += @$" AND 1 != (Select 1 From [listing].[ListingTag] Where [ListingTag].[ListingId] = [Listing].[ListingId] 
                                                AND [ListingTag].[TagId] in (@ExcludeTagIds)  AND [ListingTag].[Deleted] = 0 )";
                _queryParameters.Add("ExcludeTagIds", queryModel.ExcludeTagIdsArray.ToList());
            }

            int filterCounter = 0;
            (filterCounter, string where1) = await ProcessFilters(queryModel.Filter, false, filterCounter, "AND");
            sqlWhere += where1;

            (filterCounter, string where2) = await ProcessFilters(queryModel.Exclude, true, filterCounter, "AND");
            sqlWhere += where2;

            (filterCounter, string where3) = await ProcessFilters(queryModel.OrFilter, false, filterCounter, "OR");
            sqlWhere += where3;

            // Available sorting options are driven by the SortOption table.
            if (bypassSort != true)
            {
                StringDictionary validSortCols = new StringDictionary();
                var validSortOptionsDb = await GetSortOption();
                foreach (var sr in validSortOptionsDb)
                {
                    validSortCols.Add(sr.SortOptionId.ToString(), sr.SqlSortStmt);
                }
                try
                {
#pragma warning disable CS8602 // Dereference of a possibly null reference.
                    sqlOrder = queryModel.StandardListParameters.EvaluateSortToSqlOrderBy(validSortCols, "-ListingId");
#pragma warning restore CS8602 // Dereference of a possibly null reference.
                }
                catch (Exception)
                {
                    throw new ApiErrorException($"SortBy configuration failed. Available sort options must be configured in SortOptions table, and sortOptionId used as the sortBy.");
                }
            }

            // Add SQL Hint
            // to use conditional index that is filtered by statusCode, deleted, and listingTypeId
            //if (queryModel.StatusCode == "Active")
            //{
            //    string withIndex = "";
            //    switch (queryModel.ListingTypeId)
            //    {
            //        case 0: withIndex = "[IX_Listing_FI_StatusActive_Type0]";
            //            break;
            //        case 1:
            //            withIndex = "[IX_Listing_FI_StatusActive_Type1]";
            //            break;
            //        case 2:
            //            withIndex = "[IX_Listing_FI_StatusActive_Type2]";
            //            break;
            //        case 3:
            //            withIndex = "[IX_Listing_FI_StatusActive_Type3]";
            //            break;
            //        case 4:
            //            withIndex = "[IX_Listing_FI_StatusActive_Type4]";
            //            break;
            //        default:
            //            break;
            //    }
            //    if (!string.IsNullOrEmpty(withIndex))
            //    {
            //        sqlFrom += $@" WITH (INDEX({withIndex})) ";
            //    }
            //}

            return (sqlFrom, sqlJoin, sqlWhere, sqlOrder, preSelect, extraCols);
        }

        /// <summary>
        /// Process the Query Filter and Exclude request parameters (these come in as a dictionary)
        /// Convert them into the required where clauses.
        /// </summary>
        /// <param name="filters">filters to be processed</param>
        /// <param name="exclude">set to true to exclude rows that match, default is false.</param>
        /// <param name="filterCounter">count the number of attribute filters applied</param>
        /// <param name="logicOperator"></param>
        /// <returns></returns>
        private async Task<(int, string)> ProcessFilters(Dictionary<string, string>? filters, bool exclude, int filterCounter, string logicOperator = "AND")
        {
            string where = "";
            int expressionsThisTime = 0;
            if (filters != null && filters.Count > 0)
            {
                // Attribute filters - apply each requested filter
                // Left side (key) is AttributeCode and ExpressionType. Use | split code and expression type.
                // Right side (value) is the value to match to. Use | to split range or multiple values 
                // Expression Types:
                // - range : requires right to have a from and to value (split by |)
                // - gt
                // - lt
                // - gte
                // - lte
                // - eq (default)
                foreach (var filter in filters)
                {
                    filterCounter++;
                    expressionsThisTime++;
                    string valueMatch = "";
                    string parameterSide = "";

                    (string attributeCode, ExpressionType expression) = DetermineFilterExpression(filter.Key);


                    _queryParameters.Add($"AttributeCodeF{filterCounter}", attributeCode);
                    var attributeConfig = await GetAttribute(attributeCode);

                    if (attributeConfig == null)
                    {
                        throw new ApiErrorException($"Requested {(exclude ? "exclude" : "filter")} attribute '{attributeCode}' does not exist.");
                    }

                    var values = filter.Value.Split('|');

                    switch (expression)
                    {
                        case ExpressionType.eq:
                            parameterSide = $" = @AttributeValue{filterCounter}";
                            _queryParameters.Add($"AttributeValue{filterCounter}", filter.Value);
                            break;
                        case ExpressionType.gt:
                            parameterSide = $" > @AttributeValue{filterCounter}";
                            _queryParameters.Add($"AttributeValue{filterCounter}", filter.Value);
                            break;
                        case ExpressionType.lt:
                            parameterSide = $" < @AttributeValue{filterCounter}";
                            _queryParameters.Add($"AttributeValue{filterCounter}", filter.Value);
                            break;
                        case ExpressionType.gte:
                            parameterSide = $" >= @AttributeValue{filterCounter}";
                            _queryParameters.Add($"AttributeValue{filterCounter}", filter.Value);
                            break;
                        case ExpressionType.lte:
                            parameterSide = $" <= @AttributeValue{filterCounter}";
                            _queryParameters.Add($"AttributeValue{filterCounter}", filter.Value);
                            break;
                        case ExpressionType.ne:
                            parameterSide = $" != @AttributeValue{filterCounter}";
                            _queryParameters.Add($"AttributeValue{filterCounter}", filter.Value);
                            break;
                        case ExpressionType.inset:
                            parameterSide = $" in (@AttributeValue{filterCounter})";
                            _queryParameters.Add($"AttributeValue{filterCounter}", values.ToList());
                            break;
                        case ExpressionType.range:
                            parameterSide = $" BETWEEN @AttributeValue{filterCounter}A AND @AttributeValue{filterCounter}B ";
                            _queryParameters.Add($"AttributeValue{filterCounter}A", values[0]);
                            _queryParameters.Add($"AttributeValue{filterCounter}B", values[1]);
                            break;
                        case ExpressionType.startswith:
                            parameterSide = $" like @AttributeValue{filterCounter} + '%' ";
                            _queryParameters.Add($"AttributeValue{filterCounter}", values[0]);
                            break;
                        case ExpressionType.endswith:
                            parameterSide = $" like '%' + @AttributeValue{filterCounter} ";
                            _queryParameters.Add($"AttributeValue{filterCounter}", values[0]);
                            break;
                        case ExpressionType.contains:
                            parameterSide = $" like '%' + @AttributeValue{filterCounter} + '%' ";
                            _queryParameters.Add($"AttributeValue{filterCounter}", values[0]);
                            break;
                    }

                    switch (attributeConfig.AttributeValueTypeCode)
                    {
                        case "ValueString":
                            valueMatch = $"[ValueString] {parameterSide}";
                            break;
                        case "ValueStringMax":
                            valueMatch = $"[ValueStringMax] {parameterSide}";
                            break;
                        case "ValueNumeric":
                            valueMatch = $"[ValueNumeric] {parameterSide}";
                            break;
                        case "ValueDateTime":
                            valueMatch = $"[ValueDateTime] {parameterSide}";
                            break;
                        case "ValueGeography":
                            throw new ApiErrorException("Filtering on a Geography Attribute is not currently supported.");
                            //valueMatch = $"[ValueGeography] {parameterSide}";
                        default:
                            valueMatch = $"[ValueString] {parameterSide}";
                            break;
                    }

                    if (expressionsThisTime == 1)
                    {
                        // Wrap all expressions for this set in brackets and use AND on the outside.
                        where += @$" AND (";
                    }
                    else
                    {
                        where += @$" {logicOperator} ";
                    }
                    where += @$" 1 {(exclude ? "!=" : "=")} (Select 1 From [listing].[ListingAttribute] Where [ListingId] = [Listing].[ListingId] 
                                                AND [AttributeCode] = @AttributeCodeF{filterCounter} 
                                                AND {valueMatch} )";
                }
            }

            if (expressionsThisTime > 0)
            {
                where += " ) ";
            }

            return (filterCounter, where);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="leftSide"></param>
        /// <returns>(AttributeCode,filter expression)</returns>
        private (string, ExpressionType) DetermineFilterExpression(string leftSide)
        {
            string splitChar = "|";
            if (!leftSide.Contains(splitChar)) return (leftSide, ExpressionType.eq);

            var vals = leftSide.Split(splitChar);

            if (System.Enum.TryParse<ExpressionType>(vals[1], true, out ExpressionType resExpressionType))
            {
                return (vals[0], resExpressionType);
            }

            throw new ApiErrorException($"Invalid filter expression provided. Expression '{vals[1]}' not recognised. Valid expressions are {String.Join(",", System.Enum.GetNames<ExpressionType>())}");

        }

        /// <summary>
        /// Get the available filters for the requested container or page.
        /// For each filter returns the available values and the number of records with each available value.
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="returnCountsPerAttribute">When true returns a count per attribute of the number of listings with the attribute based on applied filters</param>
        /// <param name="onlyReturnContainersWithFilterableAttributes"></param>
        /// <param name="returnCountOfAvailableListings"></param>
        /// <returns></returns>
        internal async Task<ListingFiltersResponseCDto> GetFiltersAsync(ListingQueryModelDto queryModel, bool returnCountsPerAttribute, bool onlyReturnContainersWithFilterableAttributes, bool returnCountOfAvailableListings)
        {
            int? tenantId = null;
            if(!_utils.HasClaim("List Parts"))
            {
                tenantId = _utils.TenantId;
            }
            ListingFiltersResponseCDto response = new ListingFiltersResponseCDto();
            (string sqlFrom, string sqlJoin, string sqlWhere, string sqlOrder, string preSelect, string extraCols) = await BuildQuery(queryModel, true);

            // 1) Get the Attribute Display Sets for filtering.
            List<ListingFiltersResponseDisplayContainerCDto> DisplayContainers = new List<ListingFiltersResponseDisplayContainerCDto>();

            if (!string.IsNullOrEmpty(queryModel.DisplayGroupCode))
            {
                DisplayContainers = await GetDisplayContainersForFilters(queryModel.DisplayGroupCode);
            }

            if (queryModel.DisplayContainerCodesArray?.Length > 0)
            {
                foreach (var dcc in queryModel.DisplayContainerCodesArray)
                {
                    // Check the Display Container exists.
                    var DisplayContainer = await GetDisplayContainerForFilters(dcc);
                    if (DisplayContainer.Enabled == true && !DisplayContainers.Exists(x => x.DisplayContainerCode == DisplayContainer.DisplayContainerCode))
                    {
                        DisplayContainers.Add(DisplayContainer);
                    }
                }
            }

            foreach (var ds in DisplayContainers)
            {
                ds.Attributes = await GetDisplayContainerAtributesForFilters(ds.DisplayContainerCode);
            }

            string sqlJoin2 = "";
            string sql = "";
            response.DisplayContainers = DisplayContainers;

            // Get All possible distinct values for filters
            var allRequestedDistinctAttributes = DisplayContainers.SelectMany(x => x.Attributes).Where(x => x.FilterGetModeCode == "Distinct").Select(x => x.AttributeCode).ToList();
            if (allRequestedDistinctAttributes.Count > 0)
            {
                sqlJoin2 = $@" INNER JOIN [Listing].[ListingAttribute] [ListingAttribute] ON [ListingAttribute].[ListingId] = [Listing].[ListingId] AND [ListingAttribute].[AttributeCode] IN (@AttributeCodes) ";
                sql = $@"{preSelect}
                            SELECT Distinct [ListingAttribute].[AttributeCode], [ListingAttribute].[ValueString], [ListingAttribute].[ValueNumeric], [ListingAttribute].[ValueDateTime]
                            {(returnCountsPerAttribute == true ? ",Count([Listing].[ListingId]) " : "")}
                         {sqlFrom}
                         {sqlJoin}
                         {sqlJoin2}
                         {sqlWhere}
                         GROUP By [ListingAttribute].[AttributeCode], [ListingAttribute].[ValueString], [ListingAttribute].[ValueNumeric], [ListingAttribute].[ValueDateTime]
                         ORDER BY [ListingAttribute].[AttributeCode], [ListingAttribute].[ValueString], [ListingAttribute].[ValueNumeric], [ListingAttribute].[ValueDateTime]
                         ";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
                {
                    if (allRequestedDistinctAttributes.Count > 0) command.AddArgument("AttributeCodes", allRequestedDistinctAttributes);
                    if (_queryParameters.Count > 0)
                    {
                        // Add the attribute filter parameters
                        foreach (var qp in _queryParameters)
                        {
                            command.AddArgument(qp.Key, qp.Value);
                        }
                    }

                    using (var reader = await command.SelectRaw())
                    {
                        while (reader.Read())
                        {
                            string? attrCode = (reader.GetString(0));
                            string? valueString = !reader.IsDBNull(1) ? (reader.GetString(1)) : null;
                            decimal? valueNumeric = !reader.IsDBNull(2) ? (reader.GetDecimal(2)) : null;
                            DateTimeOffset? valueDateTime = !reader.IsDBNull(3) ? (reader.GetDateTimeOffset(3)) : null;
                            int countValues = !reader.IsDBNull(4) ? (reader.GetInt32(4)) : 0;
                            var dsAttr = response.DisplayContainers.FirstOrDefault(x => x.Attributes != null && x.Attributes.Any(x => x.AttributeCode == attrCode));
                            if (dsAttr != null)
                            {
                                var attr = dsAttr.Attributes?.FirstOrDefault(X => X.AttributeCode == attrCode);
                                if (attr != null)
                                {
                                    if (attr.AvailableValues == null) attr.AvailableValues = new List<AvailableValuesWithStats>();
#pragma warning disable CS8601 // Possible null reference assignment.
                                    attr.AvailableValues.Add(new AvailableValuesWithStats()
                                    {
                                        Value = attr.AttributeValueTypeCode == "ValueString" ? valueString :
                                                             attr.AttributeValueTypeCode == "ValueNumeric" ? valueNumeric :
                                                             attr.AttributeValueTypeCode == "ValueDateTime" ? valueDateTime : null,
                                        Count = countValues
                                    });
                                }
#pragma warning restore CS8601 // Possible null reference assignment.
                            }
                        }
                    }
                }
            }

            // Get All possible Min/Max values for filters
            // ********************************************

            var allRequestedMinMaxAttributes = DisplayContainers.SelectMany(x => x.Attributes).Where(x => x.FilterGetModeCode == "MinMax").Select(x => x.AttributeCode).ToList();
            if (allRequestedMinMaxAttributes.Count > 0)
            {
                sqlJoin2 = $@" INNER JOIN [Listing].[ListingAttribute] [ListingAttribute] ON [ListingAttribute].[ListingId] = [Listing].[ListingId] AND [ListingAttribute].[AttributeCode] IN (@AttributeCodes) ";
                sql = $@"{preSelect}
                            SELECT Distinct [ListingAttribute].[AttributeCode], MIN([ListingAttribute].[ValueNumeric]), MAX([ListingAttribute].[ValueNumeric]), MIN([ListingAttribute].[ValueDateTime]), MAX([ListingAttribute].[ValueDateTime])
                         {sqlFrom}
                         {sqlJoin}
                         {sqlJoin2}
                         {sqlWhere}
                         GROUP By [ListingAttribute].[AttributeCode]
                         ORDER BY [ListingAttribute].[AttributeCode]
";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
                {
                    if (allRequestedMinMaxAttributes.Count > 0) command.AddArgument("AttributeCodes", allRequestedMinMaxAttributes);
                    if (_queryParameters.Count > 0)
                    {
                        // Add the attribute filter parameters
                        foreach (var qp in _queryParameters)
                        {
                            command.AddArgument(qp.Key, qp.Value);
                        }
                    }

                    using (var reader = await command.SelectRaw())
                    {
                        while (reader.Read())
                        {
                            string? attrCode = (reader.GetString(0));
                            decimal? valueNumericMin = !reader.IsDBNull(1) ? (reader.GetDecimal(1)) : null;
                            decimal? valueNumericMax = !reader.IsDBNull(2) ? (reader.GetDecimal(2)) : null;
                            DateTimeOffset? valueDateTimeMin = !reader.IsDBNull(3) ? (reader.GetDateTimeOffset(3)) : null;
                            DateTimeOffset? valueDateTimeMax = !reader.IsDBNull(4) ? (reader.GetDateTimeOffset(4)) : null;
                            var dsAttr = response.DisplayContainers.FirstOrDefault(x => x.Attributes != null && x.Attributes.Any(x => x.AttributeCode == attrCode));
                            if (dsAttr != null)
                            {
                                var attr = dsAttr.Attributes?.FirstOrDefault(X => X.AttributeCode == attrCode);
                                if (attr != null)
                                {
                                    if (attr.AvailableValues == null) attr.AvailableValues = new List<AvailableValuesWithStats>();
#pragma warning disable CS8601 // Possible null reference assignment.
                                    attr.AvailableValues.Add(new AvailableValuesWithStats()
                                    {
                                        Value = attr.AttributeValueTypeCode == "ValueNumeric" ? valueNumericMin :
                                                             attr.AttributeValueTypeCode == "ValueDateTime" ? valueDateTimeMin : null
                                    });
                                    attr.AvailableValues.Add(new AvailableValuesWithStats()
                                    {
                                        Value = attr.AttributeValueTypeCode == "ValueNumeric" ? valueNumericMax :
                                                             attr.AttributeValueTypeCode == "ValueDateTime" ? valueDateTimeMax : null
                                    });
#pragma warning restore CS8601 // Possible null reference assignment.
                                }
                            }
                        }
                    }
                }
            }

            if (onlyReturnContainersWithFilterableAttributes == true)
            {
                // Remove any containers with no filterable attributes
                response.DisplayContainers = response.DisplayContainers.Where(x => x.Attributes != null && x.Attributes.Any(x => x.FilterGetModeCode == "None"
                                                                        || x.AvailableValues?.Count > 0)).ToList();
            }

            // Count total available records
            // ******************************
            if (returnCountOfAvailableListings == true)
            {
                sql = $@"{preSelect}
                            SELECT COUNT([Listing].[ListingId])
                         {sqlFrom}
                         {sqlJoin}
                         {sqlWhere} ";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
                {
                    if (allRequestedMinMaxAttributes.Count > 0) command.AddArgument("AttributeCodes", allRequestedMinMaxAttributes);
                    if (_queryParameters.Count > 0)
                    {
                        // Add the attribute filter parameters
                        foreach (var qp in _queryParameters)
                        {
                            command.AddArgument(qp.Key, qp.Value);
                        }
                    }

                    using (var reader = await command.SelectRaw())
                    {
                        if (reader.Read())
                        {
                            response.CountOfListingsFound = reader.GetInt32(0);
                        }
                    }
                }
            }

            return response;
        }

        /// <summary>
        /// Take a Listing Filters Response dto and flatten it returing a simple dictionary of Attribute Codes and Values.
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="returnCountsPerAttribute"></param>
        /// <returns></returns>
        internal ListingFiltersFlattenedResponseCDto FlattenFilters(ListingFiltersResponseCDto dto, bool returnCountsPerAttribute)
        {
            ListingFiltersFlattenedResponseCDto resp = new ListingFiltersFlattenedResponseCDto();
            resp.CountOfListingsFound = dto.CountOfListingsFound;
            resp.Fields = new Dictionary<string, object?>();
            if (dto == null || dto.DisplayContainers == null) { return resp; }
            foreach (var displayContainer in dto.DisplayContainers)
            {
                if (displayContainer != null && displayContainer.Attributes != null)
                {
                    foreach (var att in displayContainer.Attributes)
                    {
                        if (att != null && att.AttributeCode != null)
                        {
                            string attName = $"{Char.ToLowerInvariant(att.AttributeCode[0]) + att.AttributeCode.Substring(1)}";
                            if (!resp.Fields.ContainsKey(attName))
                            {
                                if (att.AvailableValues != null && att.AvailableValues.Count > 0)
                                {
                                    if (returnCountsPerAttribute)
                                    {
                                        resp.Fields.Add(attName, att.AvailableValues);
                                    }
                                    else
                                    {
                                        resp.Fields.Add(attName, att.AvailableValues.Select(x => x.Value).ToArray());
                                    }
                                }
                                else
                                {
                                    resp.Fields.Add(attName, new int[0]);
                                }
                            }
                        }
                    }
                }
            }

            return resp;
        }

        private async Task<ListingDisplayContainerCDto> GetDisplayContainer(string DisplayContainerCode)
        {
            DisplayContainerCode = DisplayContainerCode.Trim();
            string cacheKey = "DisplayContainer" + DisplayContainerCode;

            if (_memoryCache.TryGetValue(cacheKey, out ListingDisplayContainerCDto? cacheValue) && cacheValue != null)
            {
                return cacheValue.DeepClone();
            }

            string sql = @"
                        SELECT  
                        [DisplayContainer].[Title], 
                        [DisplayContainer].[DisplayContainerCode],                         
                        [DisplayContainer].[Enabled],
                        [DisplayContainer].[IsShowTitle],
                        [DisplayContainer].[Icon],
                        [DisplayContainer].[HelpText]
                        FROM [listing].[DisplayContainer] [DisplayContainer] 
                        WHERE [DisplayContainer].[DisplayContainerCode] = @DisplayContainerCode";
            var result = new ListingDisplayContainerCDto();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("DisplayContainerCode", DisplayContainerCode);
                result = await command.SelectSingle<ListingDisplayContainerCDto>();
            }
            if (result == null)
            {
                throw new ApiErrorException($"DisplayContainerCode '{DisplayContainerCode}' does not exist");
            }

            if (result.Enabled == false)
            {
                throw new ApiErrorException($"DisplayContainerCode '{DisplayContainerCode}' is not enabled");
            }

            _memoryCache.Set(cacheKey, result.DeepClone(), CacheOptions());

            return result;
        }

        private async Task<ListingFiltersResponseDisplayContainerCDto> GetDisplayContainerForFilters(string DisplayContainerCode)
        {
            DisplayContainerCode = DisplayContainerCode.Trim();
            string cacheKey = "DisplayContainerForFilters" + DisplayContainerCode;

            if (_memoryCache.TryGetValue(cacheKey, out ListingFiltersResponseDisplayContainerCDto? cacheValue))
            {
                return cacheValue.DeepClone();
            }

            string sql = @"
                        SELECT  
                        [DisplayContainer].[Title], 
                        [DisplayContainer].[DisplayContainerCode],                         
                        [DisplayContainer].[Enabled],
                        [DisplayContainer].[IsShowTitle],
                        [DisplayContainer].[Icon],
                        [DisplayContainer].[HelpText]
                        FROM [listing].[DisplayContainer] [DisplayContainer] 
                        WHERE [DisplayContainer].[DisplayContainerCode] = @DisplayContainerCode";
            var result = new ListingFiltersResponseDisplayContainerCDto();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("DisplayContainerCode", DisplayContainerCode);
                result = await command.SelectSingle<ListingFiltersResponseDisplayContainerCDto>();
            }
            if (result == null)
            {
                throw new ApiErrorException($"DisplayContainerCode '{DisplayContainerCode}' does not exist");
            }

            if (result.Enabled == false)
            {
                throw new ApiErrorException($"DisplayContainerCode '{DisplayContainerCode}' is not enabled");
            }

            _memoryCache.Set(cacheKey, result.DeepClone(), CacheOptions());

            return result;
        }

        private async Task<List<ListingDisplayContainerCDto>> GetDisplayContainers(string? displayGroupCode = "")
        {
            string cacheKey = "DisplayContainers-" + displayGroupCode;

            if (_memoryCache.TryGetValue(cacheKey, out List<ListingDisplayContainerCDto>? cacheValue))
            {
                var cacheResp = new List<ListingDisplayContainerCDto>();
                if (cacheValue != null)
                {
                    foreach (var rec in cacheValue)
                    {
                        cacheResp.Add(rec.DeepClone());
                    }
                    return cacheResp;
                }
            }

            string sql = @$"
                        SELECT  
                        [DisplayContainer].[Title], 
                        [DisplayContainer].[DisplayContainerCode],                         
                        [DisplayContainer].[Enabled],
                        [DisplayContainer].[IsShowTitle],
                        [DisplayContainer].[Icon],
                        [DisplayContainer].[HelpText]
                        FROM [listing].[DisplayContainer] [DisplayContainer] 
                        {(!string.IsNullOrEmpty(displayGroupCode) ? "INNER JOIN [listing].[DisplayGroupDisplayContainer] ON [DisplayGroupDisplayContainer].[DisplayContainerCode] = [DisplayContainer].[DisplayContainerCode] " : "")}
                        {(!string.IsNullOrEmpty(displayGroupCode) ? "INNER JOIN [listing].[DisplayGroup] ON [DisplayGroup].[DisplayGroupCode] = [DisplayGroupDisplayContainer].[DisplayGroupCode] AND [DisplayGroup].[IsEnabled] = 1 " : "")}
                        WHERE [DisplayContainer].[Enabled] = 1 
                        {(!string.IsNullOrEmpty(displayGroupCode) ? " AND [DisplayGroupDisplayContainer].[DisplayGroupCode] = @displayGroupCode " : "")}
                        {(!string.IsNullOrEmpty(displayGroupCode) ? " ORDER BY [DisplayGroupDisplayContainer].[SortOrder] ASC " : "")}
                        ";
            var result = new List<ListingDisplayContainerCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("displayGroupCode", displayGroupCode);
                result = await command.SelectMany<ListingDisplayContainerCDto>();
            }

            _memoryCache.Set(cacheKey, result.DeepClone(), CacheOptions());

            return result;
        }

        /// <summary>
        /// Returns all Display Containers, or if a DisplayGroupCode is requested then just display containers related to the group.
        /// Note! if a DisplayGroup is set IsEnabled false then no Display Containers will be retuirned.
        /// </summary>
        /// <param name="displayGroupCode"></param>
        /// <returns></returns>
        private async Task<List<ListingFiltersResponseDisplayContainerCDto>> GetDisplayContainersForFilters(string displayGroupCode = "")
        {
            string cacheKey = "DisplayContainersForFilters-" + displayGroupCode;

            if (_memoryCache.TryGetValue(cacheKey, out List<ListingFiltersResponseDisplayContainerCDto>? cacheValue))
            {
                var cacheResp = new List<ListingFiltersResponseDisplayContainerCDto>();
                if (cacheValue != null)
                {
                    foreach (var rec in cacheValue)
                    {
                        cacheResp.Add(rec.DeepClone());
                    }
                    return cacheResp;
                }
            }

            string sql = @$"
                        SELECT  
                        [DisplayContainer].[Title], 
                        [DisplayContainer].[DisplayContainerCode],                         
                        [DisplayContainer].[Enabled],
                        [DisplayContainer].[IsShowTitle],
                        [DisplayContainer].[Icon],
                        [DisplayContainer].[HelpText]
                        FROM [listing].[DisplayContainer] [DisplayContainer] 
                        {(!string.IsNullOrEmpty(displayGroupCode) ? "INNER JOIN [listing].[DisplayGroupDisplayContainer] ON [DisplayGroupDisplayContainer].[DisplayContainerCode] = [DisplayContainer].[DisplayContainerCode] " : "")}
                        {(!string.IsNullOrEmpty(displayGroupCode) ? "INNER JOIN [listing].[DisplayGroup] ON [DisplayGroup].[DisplayGroupCode] = [DisplayGroupDisplayContainer].[DisplayGroupCode] AND [DisplayGroup].[IsEnabled] = 1 " : "")}
                        WHERE [DisplayContainer].[Enabled] = 1 
                        {(!string.IsNullOrEmpty(displayGroupCode) ? " AND [DisplayGroupDisplayContainer].[DisplayGroupCode] = @displayGroupCode " : "")}
                        {(!string.IsNullOrEmpty(displayGroupCode) ? " ORDER BY [DisplayGroupDisplayContainer].[SortOrder] ASC " : "")}
                        ";
            var result = new List<ListingFiltersResponseDisplayContainerCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("displayGroupCode", displayGroupCode);
                result = await command.SelectMany<ListingFiltersResponseDisplayContainerCDto>();
            }

            _memoryCache.Set(cacheKey, result.DeepClone(), CacheOptions());

            return result;
        }

        private async Task<List<ListingDisplayContainerAttributesCDto>> GetDisplayContainerAtributesForFilters(string? DisplayContainerCode)
        {
            if (DisplayContainerCode == null) { return null; }
            DisplayContainerCode = DisplayContainerCode.Trim();
            string cacheKey = "DisplayContainersAttrs" + DisplayContainerCode;

            if (_memoryCache.TryGetValue(cacheKey, out List<ListingDisplayContainerAttributesCDto>? cacheValue))
            {
                var cacheResp = new List<ListingDisplayContainerAttributesCDto>();
                if (cacheValue != null)
                {
                    foreach (var rec in cacheValue)
                    {
                        rec.AvailableValues = new List<AvailableValuesWithStats>();
                        cacheResp.Add(rec.DeepClone());
                    }
                    return cacheResp;
                }
            }

            string sql = @"
                        SELECT  
                        [Attribute].[AttributeCode],
                        [Attribute].[Label],                        
                        [Attribute].[AttributeGroupCode],
                        [Attribute].[AttributeValueTypeCode],
                        CASE
                            WHEN [DisplayContainerAttribute].[InputTypeCode] IS NULL THEN [Attribute].[InputTypeCode]
                            ELSE [DisplayContainerAttribute].[InputTypeCode]
                        END as [InputTypeCode],
                        [Attribute].[IsEnabled],
                         [Attribute].[IsManyAllowed],
                         [Attribute].[Icon],
                         [Attribute].[ReadAccessClaim],
                         [Attribute].[WriteAccessClaim],
                         [Attribute].[NumericDecimalPlaces],
                         [Attribute].[FilterGetModeCode],
                         [DisplayContainerAttribute].[DisplayContainerCode],
                        [DisplayContainerAttribute].[DisplayContainerAttributeId]
                        FROM [listing].[DisplayContainerAttribute] [DisplayContainerAttribute] 
                        INNER JOIN [listing].[Attribute] [Attribute] ON [Attribute].[AttributeCode] = [DisplayContainerAttribute].[AttributeCode]
                        WHERE [DisplayContainerAttribute].[Deleted] = 0 AND [Attribute].[IsEnabled] = 1 AND [DisplayContainerAttribute].[DisplayContainerCode] = @DisplayContainerCode
                        ORDER BY [DisplayContainerAttribute].[SortOrder], [Attribute].[SortOrder]";

            var result = new List<ListingDisplayContainerAttributesCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("DisplayContainerCode", DisplayContainerCode);
                result = await command.SelectMany<ListingDisplayContainerAttributesCDto>();
            }

            _memoryCache.Set(cacheKey, result.DeepClone(), CacheOptions());

            return result;
        }

        private async Task<List<ListingDataCDto>> GetDisplayContainerAttributes(string DisplayContainerCode)
        {
            DisplayContainerCode = DisplayContainerCode.Trim();
            string cacheKey = "DisplayContainersAttrs" + DisplayContainerCode;

            if (_memoryCache.TryGetValue(cacheKey, out List<ListingDataCDto>? cacheValue))
            {
                var cacheResp = new List<ListingDataCDto>();
                if (cacheValue != null)
                {
                    foreach (var rec in cacheValue)
                    {
                        cacheResp.Add(rec.DeepClone());
                    }
                    return cacheResp;
                }
            }

            string sql = @"
                        SELECT  
                        [Attribute].[AttributeCode], 
                        [Attribute].[Label],                         
                        [Attribute].[AttributeGroupCode],
                        [Attribute].[AttributeValueTypeCode],
                          CASE 
                            WHEN [DisplayContainerAttribute].[InputTypeCode] IS NULL THEN [Attribute].[InputTypeCode]
                            ELSE [DisplayContainerAttribute].[InputTypeCode]
                        END as [InputTypeCode],
                        [Attribute].[IsEnabled],
                         [Attribute].[IsManyAllowed],
                         [Attribute].[Icon],
                         [Attribute].[ReadAccessClaim],
                         [Attribute].[WriteAccessClaim],
                         [Attribute].[NumericDecimalPlaces],
                         [DisplayContainerAttribute].[DisplayContainerCode]
                        FROM [listing].[DisplayContainerAttribute] [DisplayContainerAttribute] 
                        INNER JOIN [listing].[Attribute] [Attribute] ON [Attribute].[AttributeCode] = [DisplayContainerAttribute].[AttributeCode]
                        WHERE [DisplayContainerAttribute].[Deleted] = 0 AND [Attribute].[IsEnabled] = 1 AND [DisplayContainerAttribute].[DisplayContainerCode] = @DisplayContainerCode 
                        ORDER BY [DisplayContainerAttribute].[SortOrder], [Attribute].[SortOrder] ";
            var result = new List<ListingDataCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("DisplayContainerCode", DisplayContainerCode);

                using (var reader = await command.SelectRaw())
                {
                    while (reader.Read())
                    {
                        ListingDataCDto rec = new ListingDataCDto();
                        rec.AttributeCode = (reader.GetString(reader.GetOrdinal("AttributeCode")));
                        rec.Label = !reader.IsDBNull(reader.GetOrdinal("Label")) ? (reader.GetString(reader.GetOrdinal("Label"))) : null;
                        rec.AttributeGroupCode = !reader.IsDBNull(reader.GetOrdinal("AttributeGroupCode")) ? (reader.GetString(reader.GetOrdinal("AttributeGroupCode"))) : null;
                        rec.AttributeValueTypeCode = (reader.GetString(reader.GetOrdinal("AttributeValueTypeCode")));
                        rec.InputTypeCode = !reader.IsDBNull(reader.GetOrdinal("InputTypeCode")) ? (reader.GetString(reader.GetOrdinal("InputTypeCode"))) : null;
                        rec.IsManyAllowed = !reader.IsDBNull(reader.GetOrdinal("IsManyAllowed")) ? (reader.GetBoolean(reader.GetOrdinal("IsManyAllowed"))) : false;
                        rec.Icon = !reader.IsDBNull(reader.GetOrdinal("Icon")) ? (reader.GetString(reader.GetOrdinal("Icon"))) : null;
                        rec.ReadAccessClaim = !reader.IsDBNull(reader.GetOrdinal("ReadAccessClaim")) ? (reader.GetString(reader.GetOrdinal("ReadAccessClaim"))) : null;
                        rec.WriteAccessClaim = !reader.IsDBNull(reader.GetOrdinal("WriteAccessClaim")) ? (reader.GetString(reader.GetOrdinal("WriteAccessClaim"))) : null;
                        rec.NumericDecimalPlaces = !reader.IsDBNull(reader.GetOrdinal("NumericDecimalPlaces")) ? (reader.GetByte(reader.GetOrdinal("NumericDecimalPlaces"))) : null;
                        rec.DisplayContainerCode = (reader.GetString(reader.GetOrdinal("DisplayContainerCode")));

                        result.Add(rec);
                    }
                }
            }

            _memoryCache.Set(cacheKey, result.DeepClone(), CacheOptions());

            return result;
        }

        internal async Task<List<ListingSortOptionCDto>> GetSortOptionsForDisplayContainers(string[]? displayContainerCodesArray = null)
        {
            string cacheKey = "SortOptionsForDisplayContainers" + (displayContainerCodesArray != null ? string.Join(',', displayContainerCodesArray) : "");

            if (_memoryCache.TryGetValue(cacheKey, out List<ListingSortOptionCDto>? cacheValue))
            {
                var cacheResp = new List<ListingSortOptionCDto>();
                if (cacheValue != null)
                {
                    foreach (var rec in cacheValue)
                    {
                        cacheResp.Add(rec.DeepClone());
                    }
                    return cacheResp;
                }
            }

            string sql = @$"
                        SELECT 
                        [SortOptions].[SortOptionId], 
                        [SortOptions].[Label],                         
                        [SortOptions].[DisplayContainerCode]
                        FROM [listing].[SortOptions] [SortOptions] 
                        WHERE [SortOptions].[IsEnabled] = 1 
                        {(displayContainerCodesArray?.Length > 0 ? "AND [SortOptions].[DisplayContainerCode] IN (@DisplayContainers) " : "")} 
                        ORDER By [SortOptions].[SortOrder] ";
            var result = new List<ListingSortOptionCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                if (displayContainerCodesArray?.Length > 0) command.AddArgument("DisplayContainers", displayContainerCodesArray.ToList());
                result = await command.SelectMany<ListingSortOptionCDto>();
            }

            _memoryCache.Set(cacheKey, result.DeepClone(), CacheOptions());

            return result;
        }

        internal async Task<List<SortOptionsDto>> GetSortOption()
        {
           /* string cacheKey = "SortOptions";

            if (_memoryCache.TryGetValue(cacheKey, out List<SortOptionsDto>? cacheValue))
            {
                var cacheResp = new List<SortOptionsDto>();
                if (cacheValue != null)
                {
                    foreach (var rec in cacheValue)
                    {
                        cacheResp.Add(rec.DeepClone());
                    }
                    return cacheResp;
                }
                return cacheResp;
            }*/

            string sql = @$"
                        SELECT 
                        [SortOptions].[SortOptionId], 
                        [SortOptions].[Label],                         
                        [SortOptions].[DisplayContainerCode],
                        [SortOptions].[SqlSortStmt],
                        [SortOptions].[SortOrder],
                        [SortOptions].[IsEnabled]
                        FROM [listing].[SortOptions] [SortOptions] 
                        WHERE [SortOptions].[IsEnabled] = 1 
                        ORDER By [SortOptions].[SortOrder] ";
            var result = new List<SortOptionsDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                result = await command.SelectMany<SortOptionsDto>();
            }

           // _memoryCache.Set(cacheKey, result.DeepClone(), CacheOptions());

            return result;
        }

        private async Task<AttributeDto> GetAttribute(string attributeCode)
        {
            string cacheKey = "AllAttributes";

            if (_memoryCache.TryGetValue(cacheKey, out List<AttributeDto>? cacheValue))
            {
                if (cacheValue != null)
                {
                    var rec = cacheValue.FirstOrDefault(x => x.AttributeCode == attributeCode);
                    if (rec != null)
                    {
                        return rec.DeepClone();
                    }
                }
            }

            string sql = @$"
                        SELECT *
                        FROM [listing].[Attribute]";
            var result = new List<AttributeDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                result = await command.SelectMany<AttributeDto>();
            }

            if (result != null && result.Count > 0)
            {

                cacheValue = result.DeepClone();
                _memoryCache.Set(cacheKey, cacheValue, CacheOptions());

                var crec = cacheValue.FirstOrDefault(x => x.AttributeCode == attributeCode);
                if (crec != null)
                {
                    return crec;
                }
            }

            return null;
        }


        internal async Task<ListingGpsLocationCDto> GetListingGeolocationAsync(int ListingId)
        {
          
            string sql = @$"
                        SELECT 
Listing.[ListingId], 
Listing.[GpsLocation].[Lat] AS LocationLatitude,
Listing.[GpsLocation].[Long] AS LocationLongitude
FROM
    [listing].[Listing] Listing
WHERE
   Listing.[ListingId] = @ListingId";
            ListingGpsLocationCDto result = new ListingGpsLocationCDto();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ListingId", ListingId);
                using (var reader = await command.SelectRaw())
                {
                    while (reader.Read())
                    {
                        result.ListingId = (int)(reader.GetInt64(reader.GetOrdinal("ListingId")));
                        if(!reader.IsDBNull(reader.GetOrdinal("LocationLatitude")) && !reader.IsDBNull(reader.GetOrdinal("LocationLongitude")))
                        {
                            result.Geolocation = new GpslocationCDto()
                            {
                                Latitude = (decimal)reader.GetDouble(reader.GetOrdinal("LocationLatitude")),
                                Longitude = (decimal)reader.GetDouble(reader.GetOrdinal("LocationLongitude"))
                            };
                        }

                    }
                }
            }
            return result;
        }
    }
}
