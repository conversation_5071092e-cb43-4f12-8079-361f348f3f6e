using Autofac;
using Autofac.Extensions.DependencyInjection;
using MicroserviceBackendListing.Constants;
using MicroserviceBackendListing.Startup;

var builder = WebApplication.CreateBuilder(args);
var appName = MicroserviceConstants.AppId;
var version = MicroserviceConstants.Version;

var systemSettingCodes = new string[] { "AuthJwtKey", "AuthJwtExpireDays", "AuthJwtExpireDisabled", "AuthJwtIssuer" };

// Micro Service StartUp Processing ***************************************************************************
WebApplication? app = null;
var startup = new Startup(builder.Configuration, builder.Environment);
var startupBase = new Redi.Prime3.MicroService.BaseLib.StartupBase(builder.Configuration, builder.Environment, appName, version);
var startupLogger = startupBase.LoggerProviderRoot.CreateLogger(appName); // Used for logging during startup (prior to logger been registered)
try
{
    // 1) Apply SQL, Load System Settings, Check DB Connection String Valid
    var sqlFailedMessage = await startupBase.StartupStage1(builder, systemSettingCodes);

    // 2) Configure Autofac Container (Register Modules, Classes, etc.)
    builder.Host.UseServiceProviderFactory(new AutofacServiceProviderFactory())
        .ConfigureContainer<ContainerBuilder>(containerBuilder =>
        {
            startupBase.ConfigureContainer(containerBuilder); // Register StandardModules, Classes, etc.
            startup.ConfigureContainer(containerBuilder); // Register User Modules, Classes, etc.
        });

    // 3) Configure Services - Logging, Dapr, Azure, etc.
    var authentaticonBuilder = startupBase.ConfigureServices(builder.Services, builder.Host, builder.WebHost, builder.Configuration); // Configure Standard Services
    startup.ConfigureServices(builder.Services, authentaticonBuilder, startupBase, builder.Configuration); // Configure User Services

    // 4) Build the application
    app = builder.Build();

    // 5) Configure the application - Middleware, Routing, etc.
    startup.Configure(app, builder.Environment); // Configure User Middleware, Routing, etc.
    startupBase.Configure(app, builder.Environment); // Configure Standard Middleware, Routing, etc.

    // 6) Final stage - validate storage connections - ** Set Storage Account Names to empty if you want to bypass storage accounts locally **
    await startupBase.StartupStageFinal(app);
}
catch (Exception ex)
{
    startupLogger.LogError(ex, "");
    throw;
}
// Startup complete *********************************************************************************************

// 7) Run the application 
if (app != null) { startupBase.Run(app); }