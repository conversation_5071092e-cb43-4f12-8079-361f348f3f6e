﻿using MicroserviceBackendListing.Enums;
using MicroserviceContract.Dtos.Booking;
using Redi.Prime3.MicroService.BaseLib;
using Microsoft.Extensions.Caching.Memory;
using Sql;
using System.Collections.Specialized;

namespace MicroserviceBackendListing.BusinessLogic.Booking.Management
{
    public partial class BookingItem : BusinessLogicBase
    {
        private UtilityFunctions _utils;
        private readonly string ENTITY_TYPE = "BookingItem";
        private Dictionary<string, object?>? _queryParameters;
        private readonly Func<Statistic> _statistic;
        private Notifications _writeNotificationEvent;
        private readonly Func<HoldingLocation> _holdingLocation; 
        //private Func<DaprCommonServiceClient> _daprCommonServiceClient;
        public BookingItem(IUnitOfWork u, IMemoryCache memoryCache, UtilityFunctions utils,
            Func<Statistic> statistic,
            Notifications writeNotificationEvent,
            Func<HoldingLocation> holdingLocation
            //Func<DaprCommonServiceClient> daprCommonServiceClient
            )
        {
            _utils = utils;
            _unitOfWork = u;
            _statistic = statistic;
            _memoryCache = memoryCache;
            _writeNotificationEvent = writeNotificationEvent;
            _holdingLocation = holdingLocation;
            //_daprCommonServiceClient = daprCommonServiceClient;
        }

        public async Task<BookingItemResponseCDto> GetBookingItemAsync(int bookingItemId, string? statisticFields = null)
        {
            if (bookingItemId <= 0) { return null; }
            string extraCols = "";
            string baseSqlJoins = "";
            string extraSqlJoin = "";
            string sql = "";
            _queryParameters = [];

            (sql, baseSqlJoins, extraSqlJoin, extraCols) = await BuildBaseBookingItemQuery(statisticFields);

            sql += "WHERE [BookingItem].[BookingItemId] = @BookingItemId ";

            _queryParameters.Add("BookingItemId", bookingItemId);

            // If user belongs to a Tenant then enusre they can only request there Tenant BookingItemId's
            if (_utils.TenantId != null)
            {
                sql += " AND [BookingItem].[TenantId] = @TenantId ";
                _queryParameters.Add("TenantId", _utils.TenantId);
            }

            BookingItemResponseCDto? result = null;
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                if (_queryParameters.Count > 0)
                {
                    foreach (var qp in _queryParameters)
                    {
                        command.AddArgument(qp.Key, qp.Value);
                    }
                }
                using (var reader = await command.SelectRaw())
                {
                    if (reader.Read())
                    {
                        result = GetBookingItemRow(reader, statisticFields);
                    }
                }

            }

            return result;
        }

        internal async Task<ListResponseDto<BookingItemResponseCDto>> ListBookingItemsAsync(StandardListParameters standardListParameters,
            string? query = null,
            List<string>? timeZoneIanaIds = null,
            Int16? minimumGapBetweenBookingsMinutes = null,
            Int16? maximumGapBetweenBookingsMinutes = null,
            Int16? minimumBookingTimeInMinutes = null,
            Int16? maximumBookingTimeInMinutes = null,
            Int16? minimumBookingIntoFutureDays = null,
            Int16? maximumBookingIntoFutureDays = null,
            bool? isRecurringBookingEnabled = null,
            int? publicHolidaySetId = null,
            List<Guid>? parentEntityIds = null,
            List<long>? parentEntityIntIds = null,
            string? parentEntityType = null,
            bool? isEnabled = false,
            DateTimeOffset? createdOnDateFrom = null,
            DateTimeOffset? createdOnDateTo = null,
            DateTimeOffset? modifiedOnDateFrom = null,
            DateTimeOffset? modifiedOnDateTo = null,
            string? createdByName = null,
            string? modifiedByName = null,
            string? statisticFields = null,
            int? tenantId = null,
            bool? returnNotesCount = false,
            List<int>? holdingLocationIds = null,
            List<int>? homeHoldingLocationIds = null)
        {
            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "ParentEntityType", "[BookingItem].[ParentEntityType]" },
                { "ParentEntityName", "[ParentEntityName]" },
                { "TimeZoneIanaId","[BookingItem].[TimeZoneIanaId]" },
                { "MinimumGapBetweenBookingsMinutes", "[BookingItem].[MinimumGapBetweenBookingsMinutes]" },
                { "MinimumBookingTimeInMinutes", "[BookingItem].[MinimumBookingTimeInMinutes]" },
                { "MaximumBookingTimeInMinutes", "[BookingItem].[MaximumBookingTimeInMinutes]" },
                { "MaximumBookingIntoFutureDays", "[BookingItem].[MaximumBookingIntoFutureDays]" },
                { "IsRecurringBookingEnabled", "[BookingItem].[IsRecurringBookingEnabled]" },
                { "IsEnabled", "[BookingItem].[IsEnabled]" },
                { "PublicHolidaySetId", "[BookingItem].[PublicHolidaySetId]" },
                { "PublicHolidaySetLabel", "[PublicHolidaySet].[Label]" },
                { "CreatedOn","[BookingItem].[CreatedOn]" },
                { "CreatedByName","[BookingItem].[CreatedByName]" },
                { "ModifiedOn","[BookingItem].[ModifiedOn]" },
                { "ModifiedByName","[BookingItem].[ModifiedByName]" },
                { "Note","[BookingItem].[Note]" },
                { "HomeHoldingLocationName", "HomeHoldingLocationName" },
                { "CurrentHoldingLocationName", "CurrentHoldingLocationName" }
            };

            string extraCols = "";
            string extraSqlJoin = "";
            string sql = "";
            string baseSqlJoins = "";
            _queryParameters = [];

            (sql, baseSqlJoins, extraSqlJoin, extraCols) = await BuildBaseBookingItemQuery(statisticFields, returnNotesCount, canBeSortedBy);

            /* 
             * WHERE clause here
             */
            string whereClause = @"
            WHERE [BookingItem].[Deleted] = @deleted ";
            _queryParameters.Add("deleted", false);

            if (_utils.TenantId != null)
            {
                whereClause += @" AND [BookingItem].[TenantId] = @tenantId ";
                _queryParameters.Add("tenantId", _utils.TenantId);
                if (tenantId != null && tenantId > 0) { throw new HttpRequestException($"tenantId is an invalid parameter for this tenant based user", null, System.Net.HttpStatusCode.BadRequest); }
            }
            else if (tenantId != null && tenantId > 0)
            {
                whereClause += @" AND [BookingItem].[TenantId] = @tenantId ";
                _queryParameters.Add("tenantId", tenantId);
            }

            if(!query.IsNullOrEmpty())
            {
                /* 
                 * IF QUERY IS SLOW, IT'S PROBABLY THIS LINE AS [Note] IS 2K CHARACTERS
                 */
                whereClause += @" AND ([ParentEntityName] LIKE '%' + @query + '%' OR [BookingItem].[Note] LIKE '%' + @query + '%') ";
                _queryParameters.Add("query", query);
            }

            if (timeZoneIanaIds != null && timeZoneIanaIds.Count > 0)
            {
                whereClause += @" AND [BookingItem].[TimeZoneIanaId] IN (@timeZoneIanaIds) ";
                _queryParameters.Add("timeZoneIanaIds", timeZoneIanaIds);
            }

            if(minimumGapBetweenBookingsMinutes.HasValue)
            {
                whereClause += @" AND [BookingItem].[MinimumGapBetweenBookingsMinutes] >= @minimumGapBetweenBookingsMinutes ";
                _queryParameters.Add("minimumGapBetweenBookingsMinutes", minimumGapBetweenBookingsMinutes);
            }

            if (maximumGapBetweenBookingsMinutes.HasValue)
            {
                whereClause += @" AND [BookingItem].[MinimumGapBetweenBookingsMinutes] <= @maximumGapBetweenBookingsMinutes ";
                _queryParameters.Add("maximumGapBetweenBookingsMinutes", maximumGapBetweenBookingsMinutes);
            }

            if (minimumBookingTimeInMinutes.HasValue)
            {
                whereClause += @" AND [BookingItem].[MinimumBookingTimeInMinutes] >= @minimumBookingTimeInMinutes ";
                _queryParameters.Add("minimumBookingTimeInMinutes", minimumBookingTimeInMinutes);
            }

            if (maximumBookingTimeInMinutes.HasValue)
            {
                whereClause += @" AND [BookingItem].[MaximumBookingTimeInMinutes] <= @maximumBookingTimeInMinutes ";
                _queryParameters.Add("maximumBookingTimeInMinutes", maximumBookingTimeInMinutes);
            }

            if (minimumBookingIntoFutureDays.HasValue)
            {
                whereClause += @" AND [BookingItem].[MaximumBookingIntoFutureDays] >= @minimumBookingIntoFutureDays ";
                _queryParameters.Add("minimumBookingIntoFutureDays", minimumBookingIntoFutureDays);
            }

            if (maximumBookingIntoFutureDays.HasValue)
            {
                whereClause += @" AND [BookingItem].[MaximumBookingIntoFutureDays] <= @maximumBookingIntoFutureDays ";
                _queryParameters.Add("maximumBookingIntoFutureDays", maximumBookingIntoFutureDays);
            }

            if (isRecurringBookingEnabled.HasValue)
            {
                whereClause += @" AND [BookingItem].[IsRecurringBookingEnabled] = @isRecurringBookingEnabled ";
                _queryParameters.Add("isRecurringBookingEnabled", isRecurringBookingEnabled);
            }

            if (publicHolidaySetId.HasValue)
            {
                whereClause += @" AND [BookingItem].[PublicHolidaySetId] = @publicHolidaySetId ";
                _queryParameters.Add("publicHolidaySetId", publicHolidaySetId);
            }

            if (parentEntityIds != null && parentEntityIds.Count() > 0)
            {
                whereClause +=  @" AND [BookingItem].[ParentEntityId] IN (@parentEntityIds) ";
                _queryParameters.Add("parentEntityIds", parentEntityIds);
            }

            if (parentEntityIntIds != null && parentEntityIntIds.Count() > 0)
            {
                whereClause +=  @" AND [BookingItem].[ParentEntityIntId] IN (@parentEntityIntIds) ";
                _queryParameters.Add("parentEntityIntIds", parentEntityIntIds);
            }

            if (!parentEntityType.IsNullOrEmpty())
            {
                whereClause += @" AND [BookingItem].[ParentEntityType] = @parentEntityType ";
                _queryParameters.Add("parentEntityType", parentEntityType);
            }

            if (createdOnDateFrom.HasValue)
            {
                whereClause += @" AND [BookingItem].[CreatedOn] >= @createdOnDateFrom ";
                _queryParameters.Add("createdOnDateFrom", createdOnDateFrom);
            }

            if (createdOnDateTo.HasValue)
            {
                whereClause += @" AND [BookingItem].[CreatedOn] <= @createdOnDateTo ";
                _queryParameters.Add("createdOnDateTo", createdOnDateTo);
            }

            if (modifiedOnDateFrom.HasValue)
            {
                whereClause += @" AND [BookingItem].[ModifiedOn] >= @modifiedOnDateFrom ";
                _queryParameters.Add("modifiedOnDateFrom", modifiedOnDateFrom);
            }

            if (modifiedOnDateTo.HasValue)
            {
                whereClause += @" AND [BookingItem].[ModifiedOn] <= @modifiedOnDateTo ";
                _queryParameters.Add("modifiedOnDateTo", modifiedOnDateTo);
            }

            if (!createdByName.IsNullOrEmpty())
            {
                whereClause += @" AND [BookingItem].[CreatedByName] = @createdByName ";
                _queryParameters.Add("createdByName", createdByName);
            }

            if (!modifiedByName.IsNullOrEmpty())
            {
                whereClause += @" AND [BookingItem].[ModifiedByName] = @modifiedByName ";
                _queryParameters.Add("modifiedByName", modifiedByName);
            }

            if (holdingLocationIds != null && holdingLocationIds.Count > 0)
            {
                whereClause += @" AND [BookingItemHoldingLocation].[HoldingLocationId] IN (@holdingLocationIds) ";
                _queryParameters.Add("holdingLocationIds", holdingLocationIds);
            }

            if (homeHoldingLocationIds != null && homeHoldingLocationIds.Count > 0)
            {
                whereClause += @" AND [BookingItem].[HomeHoldingLocationId] IN (@homeHoldingLocationIds) ";
                _queryParameters.Add("homeHoldingLocationIds", homeHoldingLocationIds);
            }

            sql += whereClause;

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "ParentEntityName");

            sql += $"OFFSET @offset ROWS";
            sql += $" FETCH NEXT @limit ROWS ONLY";

            _queryParameters.Add("offset", standardListParameters.Offset ?? 0);
            _queryParameters.Add("limit", standardListParameters.Limit ?? 100);

            List<BookingItemResponseCDto> result = new List<BookingItemResponseCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {

                if (_queryParameters.Count > 0)
                {
                    foreach (var qp in _queryParameters)
                    {
                        command.AddArgument(qp.Key, qp.Value);
                    }
                }

                using (var reader = await command.SelectRaw())
                {
                    while (reader.Read())
                    {
                        var rec = GetBookingItemRow(reader, statisticFields);
                        result.Add(rec);
                    }
                }
            }

            sql = @$"
            SELECT COUNT(*) AS totalNumOfRows
            
            {baseSqlJoins}
            {extraSqlJoin}
            ";

            sql += whereClause;
            var response = new ListResponseDto<BookingItemResponseCDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                if (_queryParameters.Count > 0)
                {
                    foreach (var qp in _queryParameters)
                    {
                        command.AddArgument(qp.Key, qp.Value);
                    }
                }

                response.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }
            response.List = result;
            return response;
        }

        internal async Task<(string, string, string, string)> BuildBaseBookingItemQuery(string? statisticFields = null, bool? returnNotesCount = false, StringDictionary? canBeSortedBy = null, bool? forUpdate = false)
        {
            string extraCols = "";
            string extraSqlJoin = "";
            string baseSqlJoins = "";
            string sql = "";
            if (canBeSortedBy == null) { canBeSortedBy = []; }

            // Add Statistic Fields to query.
            if (!string.IsNullOrEmpty(statisticFields))
            {
                var statFields = await _statistic().GetList();
                int i = 0;
                foreach (var statFld in statisticFields.Split(','))
                {
                    if (statFields.ContainsKey(statFld))
                    {
                        canBeSortedBy.Add($"{statFld}", $"[Stat{statFld}Value]");
                        i++;
                        extraCols += $",[Stat{i}].[DValue] AS [Stat{statFld}Value] ";
                        extraSqlJoin += $" LEFT JOIN [common].[StatisticRecord] [Stat{i}] ON [Stat{i}].[ParentEntityIntId] = [BookingItem].[BookingItemId] AND [Stat{i}].[StatisticId] = @StatId{i} AND [Stat{i}].[SourceStatisticEntityTypeId] = {StatisticEntityTypeEnum.BookingItem} ";
#pragma warning disable CS8602 // Dereference of a possibly null reference.
                        _queryParameters.Add($"StatId{i}", statFields[statFld]);
#pragma warning restore CS8602 // Dereference of a possibly null reference.
                    }
                }
            }

            if (returnNotesCount == true)
            {
                extraCols += " ,(Select  COUNT(*) From [common].[Note] Where [Note].[Deleted] = 0 AND [Note].[ParentEntityIntId] = [BookingItem].[BookingItemId] AND [Note].[ParentEntityType] = 'BookingItem'  ) AS NotesCount ";
            }
            else
            {
                extraCols += " ,null AS NotesCount ";
            }

            sql = @$"
            SELECT   
                    [BookingItem].[BookingItemId],
                    [BookingItem].[ParentEntityType],
                    [BookingItem].[ParentEntityId],
                    [BookingItem].[ParentEntityIntId],
                    [BookingItem].[TimeZoneIanaId],
                    [BookingItem].[MinimumGapBetweenBookingsMinutes],
                    [BookingItem].[MinimumBookingTimeInMinutes],
                    [BookingItem].[MaximumBookingTimeInMinutes],
                    [BookingItem].[MaximumBookingIntoFutureDays],
                    [BookingItem].[IsRecurringBookingEnabled],
                    [BookingItem].[IsEnabled],
                    [BookingItem].[Note],
                    [BookingItem].[PublicHolidaySetId],
                    [BookingItem].[CreatedOn],
                    [BookingItem].[CreatedByName],
                    [BookingItem].[ModifiedOn],
                    [BookingItem].[ModifiedByName],
                    [BookingItem].[Deleted],
                    [BookingItem].[HomeHoldingLocationId],
    `               [BookingItemHoldingLocation].[HoldingLocationId] AS [CurrentHoldingLocationId],
                    CASE
                     {(ConfigBase.Schemas.ContainsKey("users") ? "WHEN [BookingItem].[ParentEntityType] = 'User' THEN [BookingItemOwnerUser].[FullName]" : "")}
                     {(ConfigBase.Schemas.ContainsKey("crm") ? "WHEN [BookingItem].[ParentEntityType] = 'Party' THEN [BookingItemOwnerParty].[DisplayName]" : "")}
                     {(ConfigBase.Schemas.ContainsKey("listing") ? "WHEN [BookingItem].[ParentEntityType] = 'Listing' THEN [BookingItemOwnerListing].[Subject]" : "")}
                     {(ConfigBase.Schemas.ContainsKey("order") ? "WHEN [BookingItem].[ParentEntityType] = 'Order' THEN [BookingItemOwnerOrder].[OrderReference]" : "")}
                     ELSE [BookingItem].[CreatedByName]
                    END AS [ParentEntityName],
                     CASE
                     {(ConfigBase.Schemas.ContainsKey("users") ? "WHEN [HoldingLocation].[LocationParentEntityType] = 'User' THEN [HoldingLocationUser].[FullName]" : "")}
                     {(ConfigBase.Schemas.ContainsKey("crm") ? "WHEN [HoldingLocation].[LocationParentEntityType] = 'Party' THEN [HoldingLocationParty].[DisplayName]" : "")}
                     {(ConfigBase.Schemas.ContainsKey("listing") ? "WHEN [HoldingLocation].[LocationParentEntityType] = 'Listing' THEN [HoldingLocationListing].[Subject]" : "")}
                     {(ConfigBase.Schemas.ContainsKey("order") ? "WHEN [HoldingLocation].[LocationParentEntityType] = 'Order' THEN [HoldingLocationOrder].[OrderReference]" : "")}
                     ELSE 'Unknown Location'
                    END AS [CurrentHoldingLocationName],
                    CASE
                     {(ConfigBase.Schemas.ContainsKey("users") ? "WHEN [HomeHoldingLocation].[LocationParentEntityType] = 'User' THEN [HomeLocationUser].[FullName]" : "")}
                     {(ConfigBase.Schemas.ContainsKey("crm") ? "WHEN [HomeHoldingLocation].[LocationParentEntityType] = 'Party' THEN [HomeLocationParty].[DisplayName]" : "")}
                     {(ConfigBase.Schemas.ContainsKey("listing") ? "WHEN [HomeHoldingLocation].[LocationParentEntityType] = 'Listing' THEN [HomeLocationListing].[Subject]" : "")}
                     {(ConfigBase.Schemas.ContainsKey("order") ? "WHEN [HomeHoldingLocation].[LocationParentEntityType] = 'Order' THEN [HomeLocationOrder].[OrderReference]" : "")}
                     ELSE 'Unknown Location'
                    END AS [HomeHoldingLocationName],
                    [PublicHolidaySet].[Label] AS [PublicHolidaySetLabel]
                    {extraCols} ";

            baseSqlJoins = $@"
            FROM    [booking].[BookingItem] {(forUpdate == true ? "WITH (UPDLOCK)" : "")}
            LEFT JOIN [common].[PublicHolidaySet] ON [BookingItem].[PublicHolidaySetId] = [PublicHolidaySet].[PublicHolidaySetId]
            {(ConfigBase.Schemas.ContainsKey("crm") ? "LEFT JOIN [crm].[Party] [BookingItemOwnerParty] ON [BookingItemOwnerParty].[PartyId] = [BookingItem].[ParentEntityId] AND [BookingItem].[ParentEntityType] = 'Party'" : "")}
            {(ConfigBase.Schemas.ContainsKey("users") ? "LEFT JOIN [users].[User] [BookingItemOwnerUser] ON [BookingItemOwnerUser].[UserId] = [BookingItem].[ParentEntityId] AND [BookingItem].[ParentEntityType] = 'User'" : "")} 
            {(ConfigBase.Schemas.ContainsKey("listing") ? "LEFT JOIN [listing].[Listing] [BookingItemOwnerListing] ON [BookingItemOwnerListing].[ListingId] = [BookingItem].[ParentEntityIntId] AND [BookingItem].[ParentEntityType] = 'Listing'" : "")}
            {(ConfigBase.Schemas.ContainsKey("order") ? "LEFT JOIN [order].[Order] [BookingItemOwnerOrder] ON [BookingItemOwnerOrder].[OrderId] = [BookingItem].[ParentEntityId] AND [BookingItem].[ParentEntityType] = 'Order'" : "")}
            LEFT JOIN [booking].[HoldingLocation] [HomeHoldingLocation] ON [HomeHoldingLocation].[HoldingLocationId] = [BookingItem].[HomeHoldingLocationId]
            {(ConfigBase.Schemas.ContainsKey("crm") ? "LEFT JOIN [crm].[Party] [HomeLocationParty] ON [HomeLocationParty].[PartyId] = [HomeHoldingLocation].[LocationParentEntityId] AND [HomeHoldingLocation].[LocationParentEntityType] = 'Party'" : "")}
            {(ConfigBase.Schemas.ContainsKey("users") ? "LEFT JOIN [users].[User] [HomeLocationUser] ON [HomeLocationUser].[UserId] = [HomeHoldingLocation].[LocationParentEntityId] AND [HomeHoldingLocation].[LocationParentEntityType] = 'User'" : "")} 
            {(ConfigBase.Schemas.ContainsKey("listing") ? "LEFT JOIN [listing].[Listing] [HomeLocationListing] ON [HomeLocationListing].[ListingId] = [HomeHoldingLocation].[LocationParentEntityIntId] AND [HomeHoldingLocation].[LocationParentEntityType] = 'Listing'" : "")}
            {(ConfigBase.Schemas.ContainsKey("order") ? "LEFT JOIN [order].[Order] [HomeLocationOrder] ON [HomeLocationOrder].[OrderId] = [HomeHoldingLocation].[LocationParentEntityId] AND [HomeHoldingLocation].[LocationParentEntityType] = 'Order'" : "")}

            LEFT JOIN [booking].[BookingItemHoldingLocation] ON [BookingItemHoldingLocation].[BookingItemId] = [BookingItem].[BookingItemId] AND [BookingItemHoldingLocation].[Deleted] = 0 AND [BookingItemHoldingLocation].[FromDate] <= GetDate() 
                                                                AND ([BookingItemHoldingLocation].[ToDateTime] IS NULL OR [BookingItemHoldingLocation].[ToDateTime] > GetDate())
            LEFT JOIN [booking].[HoldingLocation] ON [HoldingLocation].[HoldingLocationId] = [BookingItemHoldingLocation].[HoldingLocationId]
            {(ConfigBase.Schemas.ContainsKey("crm") ? "LEFT JOIN [crm].[Party] [HoldingLocationParty] ON [HoldingLocationParty].[PartyId] = [HoldingLocation].[LocationParentEntityId] AND [HoldingLocation].[LocationParentEntityType] = 'Party'" : "")}
            {(ConfigBase.Schemas.ContainsKey("users") ? "LEFT JOIN [users].[User] [HoldingLocationUser] ON [HoldingLocationUser].[UserId] = [HoldingLocation].[LocationParentEntityId] AND [HoldingLocation].[LocationParentEntityType] = 'User'" : "")} 
            {(ConfigBase.Schemas.ContainsKey("listing") ? "LEFT JOIN [listing].[Listing] [HoldingLocationListing] ON [HoldingLocationListing].[ListingId] = [HoldingLocation].[LocationParentEntityIntId] AND [HoldingLocation].[LocationParentEntityType] = 'Listing'" : "")}
            {(ConfigBase.Schemas.ContainsKey("order") ? "LEFT JOIN [order].[Order] [HoldingLocationOrder] ON [HoldingLocationOrder].[OrderId] = [HoldingLocation].[LocationParentEntityId] AND [HoldingLocation].[LocationParentEntityType] = 'Order'" : "")}

";

            sql += baseSqlJoins;
            sql += extraSqlJoin;

            return (sql, baseSqlJoins, extraSqlJoin, extraCols);
        }

        internal BookingItemResponseCDto GetBookingItemRow(Microsoft.Data.SqlClient.SqlDataReader reader, string? statisticFields)
        {
            BookingItemResponseCDto rec = new BookingItemResponseCDto();
            rec.BookingItemId = !reader.IsDBNull(reader.GetOrdinal("BookingItemId")) ? (reader.GetInt32(reader.GetOrdinal("BookingItemId"))) : null;
            rec.ParentEntityType = !reader.IsDBNull(reader.GetOrdinal("ParentEntityType")) ? (reader.GetString(reader.GetOrdinal("ParentEntityType"))) : null;
            rec.ParentEntityId = !reader.IsDBNull(reader.GetOrdinal("ParentEntityId")) ? (reader.GetGuid(reader.GetOrdinal("ParentEntityId"))) : null;
            rec.ParentEntityIntId = !reader.IsDBNull(reader.GetOrdinal("ParentEntityIntId")) ? (reader.GetInt64(reader.GetOrdinal("ParentEntityIntId"))) : null;
            rec.ParentEntityName = !reader.IsDBNull(reader.GetOrdinal("ParentEntityName")) ? (reader.GetString(reader.GetOrdinal("ParentEntityName"))) : null;
            rec.TimeZoneIanaId = !reader.IsDBNull(reader.GetOrdinal("TimeZoneIanaId")) ? (reader.GetString(reader.GetOrdinal("TimeZoneIanaId"))) : null;
            rec.MinimumGapBetweenBookingsMinutes = !reader.IsDBNull(reader.GetOrdinal("MinimumGapBetweenBookingsMinutes")) ? (reader.GetInt16(reader.GetOrdinal("MinimumGapBetweenBookingsMinutes"))) : null;
            rec.MinimumBookingTimeInMinutes = !reader.IsDBNull(reader.GetOrdinal("MinimumBookingTimeInMinutes")) ? (reader.GetInt16(reader.GetOrdinal("MinimumBookingTimeInMinutes"))) : null;
            rec.MaximumBookingTimeInMinutes = !reader.IsDBNull(reader.GetOrdinal("MaximumBookingTimeInMinutes")) ? (reader.GetInt16(reader.GetOrdinal("MaximumBookingTimeInMinutes"))) : null;
            rec.MaximumBookingIntoFutureDays = !reader.IsDBNull(reader.GetOrdinal("MaximumBookingIntoFutureDays")) ? (reader.GetInt16(reader.GetOrdinal("MaximumBookingIntoFutureDays"))) : null;
            rec.IsRecurringBookingEnabled = !reader.IsDBNull(reader.GetOrdinal("IsRecurringBookingEnabled")) ? (reader.GetBoolean(reader.GetOrdinal("IsRecurringBookingEnabled"))) : null;
            rec.IsEnabled = !reader.IsDBNull(reader.GetOrdinal("IsEnabled")) ? (reader.GetBoolean(reader.GetOrdinal("IsEnabled"))) : null;
            rec.Note = !reader.IsDBNull(reader.GetOrdinal("Note")) ? (reader.GetString(reader.GetOrdinal("Note"))) : null;
            rec.PublicHolidaySetId = !reader.IsDBNull(reader.GetOrdinal("PublicHolidaySetId")) ? (reader.GetInt32(reader.GetOrdinal("PublicHolidaySetId"))) : null;
            rec.PublicHolidaySetLabel = !reader.IsDBNull(reader.GetOrdinal("PublicHolidaySetLabel")) ? (reader.GetString(reader.GetOrdinal("PublicHolidaySetLabel"))) : null;
            rec.CurrentHoldingLocationId = !reader.IsDBNull(reader.GetOrdinal("CurrentHoldingLocationId")) ? (reader.GetInt32(reader.GetOrdinal("CurrentHoldingLocationId"))) : null;
            rec.CurrentHoldingLocationName = !reader.IsDBNull(reader.GetOrdinal("CurrentHoldingLocationName")) ? (reader.GetString(reader.GetOrdinal("CurrentHoldingLocationName"))) : null;
            rec.HomeHoldingLocationId = !reader.IsDBNull(reader.GetOrdinal("HomeHoldingLocationId")) ? (reader.GetInt32(reader.GetOrdinal("HomeHoldingLocationId"))) : null;
            rec.HomeHoldingLocationName = !reader.IsDBNull(reader.GetOrdinal("HomeHoldingLocationName")) ? (reader.GetString(reader.GetOrdinal("HomeHoldingLocationName"))) : null;

            //rec.NotesCount = !reader.IsDBNull(reader.GetOrdinal("NotesCount")) ? (reader.GetInt32(reader.GetOrdinal("NotesCount"))) : null;
            rec.ModifiedByName = !reader.IsDBNull(reader.GetOrdinal("ModifiedByName")) ? (reader.GetString(reader.GetOrdinal("ModifiedByName"))) : null;
            rec.ModifiedOn = !reader.IsDBNull(reader.GetOrdinal("ModifiedOn")) ? (reader.GetDateTimeOffset(reader.GetOrdinal("ModifiedOn"))) : null;
            rec.CreatedByName = !reader.IsDBNull(reader.GetOrdinal("CreatedByName")) ? (reader.GetString(reader.GetOrdinal("CreatedByName"))) : null;
            rec.CreatedOn = !reader.IsDBNull(reader.GetOrdinal("CreatedOn")) ? (reader.GetDateTimeOffset(reader.GetOrdinal("CreatedOn"))) : DateTime.UtcNow;
            rec.Deleted = !reader.IsDBNull(reader.GetOrdinal("Deleted")) ? (reader.GetBoolean(reader.GetOrdinal("Deleted"))) : false;

            if (!string.IsNullOrEmpty(statisticFields))
            {
                rec.Statistics = [];
                foreach (var statFld in statisticFields.Split(','))
                {
                    if (!reader.IsDBNull(reader.GetOrdinal($"Stat{statFld}Value")))
                    {
                        rec.Statistics.Add(_utils.LowercaseFirstLetter(statFld), !reader.IsDBNull(reader.GetOrdinal($"Stat{statFld}Value")) ? (reader.GetDecimal(reader.GetOrdinal($"Stat{statFld}Value"))) : null);
                    }
                }
            }

            return rec;

        }
    }
}
