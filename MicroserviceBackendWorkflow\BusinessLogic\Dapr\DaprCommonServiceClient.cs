using MicroserviceContract.Dtos;
using Microsoft.AspNetCore.Mvc;
using Redi.Prime3.MicroService.BaseLib;
using System.Net.Http.Headers;

namespace MicroserviceBackendWorkflow.BusinessLogic
{
    /// <summary>
    /// Delete and replace with Microservice intended for service-to-service invocation
    /// </summary>
    public class DaprCommonServiceClient : HttpClientBase
    {
        private readonly UtilityFunctions _utils;
        private readonly ILogger _logger;
        public DaprCommonServiceClient(HttpClient httpClient, ILogger logger, UtilityFunctions utils)
        {
            _httpClient = httpClient;
            _logger = logger;
            _utils = utils;
        }

        public async Task<string> GetNextNumber(string nextNumberTypeCode, string? entityId = null, string? formatString = null, bool prefixWithCurrentYear = true)
        {
            try
            {
                var response = await _httpClient.GetAsync($"api/NextNumber/GetNextNumber?nextNumberTypeCode={nextNumberTypeCode}&entityId={entityId}&formatString={formatString}&prefixWithCurrentYear={prefixWithCurrentYear}");
                var result = await response.EnsureSuccessStatusCode().Content.ReadAsStringAsync();
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message, ex);
            }
            return "NotAvail";

        }
    }
}
