﻿using Microsoft.Extensions.Caching.Memory;
using Redi.Prime3.MicroService.BaseLib;
using Sql;

namespace MicroserviceBackendListing.BusinessLogic
{
    public class Statistic : BusinessLogicBase
    {

        public Statistic(IUnitOfWork u, IMemoryCache memoryCache)
        {
            _unitOfWork = u;
            _memoryCache = memoryCache;
        }

        internal async Task<Dictionary<string, Int16>> GetList(bool bypassCache = false)
        {
            string cacheKey = "StatisticList" ;
            if (_memoryCache.TryGetValue(cacheKey, out Dictionary<string, Int16>? cacheValue) && !bypassCache)
            {
                return cacheValue;
            }
            string sql = $@"
SELECT [StatisticId]
      ,[FieldName]
FROM [common].[Statistic] [statistic]
";
            var result = new Dictionary<string, Int16>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                using (var reader = await command.SelectRaw())
                {
                    while (reader.Read())
                    {
                        result.Add(reader.GetString(1), reader.GetInt16(0));
                    }
                }
            }

            _memoryCache.Set(cacheKey, result, CacheOptions());
            return result;
        }

    }
}
