﻿using MicroserviceContract.Dtos.Booking;
using Sql;
using MicroserviceContract.Dtos.ListingManagement;
using Redi.Prime3.MicroService.BaseLib;

namespace MicroserviceBackendListing.BusinessLogic.Booking.Management
{
    public partial class HoldingLocation : BusinessLogicBase
    {
        internal async Task<long> CreateListingAndHoldingLocationPair(RequestCDto<HoldingLocationRequestCDto> requestCDto)
        {
            var dto = requestCDto.Data;
            if (dto == null)
            {
                throw new HttpRequestException($"holdingLocation Dto is empty.", null, System.Net.HttpStatusCode.NotFound);
            }

            // Step 1: Create listing
            var listingLogic = _manageListing;
            ManageListingWithDisplayGroupedDataAndMediaCDto listingDto = new ManageListingWithDisplayGroupedDataAndMediaCDto() {
                Subject = dto.HoldingLocationName,
                ListingTypeId = 81
            };
            long newListingId = await listingLogic().CreateNewListing(listingDto, false);

            dto.LocationParentEntityType = "Listing";
            dto.LocationParentEntityIntId = newListingId;

            int holdingLocationId = (int)await CreateHoldingLocation(requestCDto);

            return holdingLocationId;
        }

        internal async Task<int?> CreateHoldingLocation(RequestCDto<HoldingLocationRequestCDto> requestCDto)
        {
            if (requestCDto == null || requestCDto.Data == null) { return null; }
            HoldingLocationRequestCDto dto = requestCDto.Data;
            string sql = @"
INSERT INTO [booking].[HoldingLocation](
       [LocationParentEntityType]
      ,[LocationParentEntityId]
      ,[LocationParentEntityIntId]
      ,[TenantId]
      ,[Deleted]
)
VALUES
(
      @LocationParentEntityType,
      @LocationParentEntityId,
      @LocationParentEntityIntId,
      @TenantId,
      @Deleted
);";
            int result = 0;
            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArgument("LocationParentEntityType", dto.LocationParentEntityType);
                command.AddArgument("LocationParentEntityId", dto.LocationParentEntityId);
                command.AddArgument("LocationParentEntityIntId", dto.LocationParentEntityIntId);
                command.AddArgument("TenantId", _utils.TenantId);
                command.AddArgument("Deleted", false);

                result = await command.ExecuteAndReturnIdentity();
            }

            return result;
        }

        internal async Task UpdateListingAndHoldingLocationPair(int holdingLocationId, RequestCDto<HoldingLocationRequestCDto> requestCDto)
        {
            if (requestCDto == null || requestCDto.Data == null) { throw new HttpRequestException("Missing dto Data"); }
            HoldingLocationRequestCDto dto = requestCDto.Data;
            // Validate order exists in db
            var exists = await GetHoldingLocationAsync(holdingLocationId, forUpdate: true);

            if (exists == null)
            {
                throw new HttpRequestException($"holdingLocationId not found {(_utils.TenantId != null ? "for Tenant" : "")}.", null, System.Net.HttpStatusCode.NotFound);
            }
            if (exists.HoldingLocationName == dto.HoldingLocationName)
            {
                // nothing has changed.
                return;
            }

            if (exists.LocationParentEntityType == "Listing" && exists.LocationParentEntityIntId !=null)
            {
                var listing = await _manageListing().GetListingByListingId((long)exists.LocationParentEntityIntId);
                listing.Subject = dto.HoldingLocationName;
                await _manageListing().UpdateListingWithDisplayContainers(listing);
            }
            else
            {
                throw new HttpRequestException($"holdingLocationId update not supported for non-isting.", null, System.Net.HttpStatusCode.NotFound);
            }

        }

        internal async Task DeleteHoldingLocation(int holdingLocationId)
        {
            string sql = @"
            UPDATE   [booking].[HoldingLocation]
            SET      [Deleted] = @Deleted
            WHERE    [HoldingLocationId] = @holdingLocationId AND [Deleted] = 0 AND [TenantId] = @tenantId ";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("holdingLocationId", holdingLocationId);
                command.AddArgument("tenantId", _utils.TenantId);

                await command.Execute();
            }
        }
    }
}
