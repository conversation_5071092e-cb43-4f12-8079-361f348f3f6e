﻿using Sql;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MicroserviceContract.Dtos
{
    [Mappable(nameof(ListingAuditId))]
    public class ListingAuditCDto
    {
        public Int64 ListingAuditId { get; set; }
        public string? Description { get; set; }
        public Int64 ListingId { get; set; }
        public DateTimeOffset CreatedOn { get; set; }
        public string? CreatedByName { get; set; }
    }
}
