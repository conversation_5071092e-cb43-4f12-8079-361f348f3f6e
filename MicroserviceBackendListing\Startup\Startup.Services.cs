using Azure.Storage;
using Microsoft.Extensions.Azure;
using Microsoft.AspNetCore.Authentication;
using Redi.Prime3.MicroService.BaseLib;
using MicroserviceBackendListing.Policies;
using Microsoft.AspNetCore.Authorization;

namespace MicroserviceBackendListing.Startup
{
	public partial class Startup
	{
        /// <summary>
        /// This method gets called by the runtime. Use this method to add services to the container.
        /// </summary>
        /// <param name="services"></param>
        /// <param name="authentaticonBuilder"></param>
        /// <param name="startupBase"></param>
        /// <param name="configuration"></param>
        public void ConfigureServices(IServiceCollection services, AuthenticationBuilder authentaticonBuilder, Redi.Prime3.MicroService.BaseLib.StartupBase startupBase, ConfigurationManager configuration)
        {
            //services.AddAzureClients(cb => AddAzureClients(cb, startupBase));

            services.AddScoped<IAuthorizationHandler, ListingBodyPermissionHandler>();
            services.AddScoped<IAuthorizationHandler, ListingQueryPermissionHandler>();

            services.AddAuthorization(options =>
            {
                Policy.Register(options, PolicyType.List);
            });
        }
	}
}
