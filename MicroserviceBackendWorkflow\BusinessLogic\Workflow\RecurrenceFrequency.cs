using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Collections.Specialized;

namespace MicroserviceBackendWorkflow.BusinessLogic
{
    public class RecurrenceFrequency : BusinessLogicBase
    {
        public RecurrenceFrequency(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a single RecurrenceFrequency by ID
        /// </summary>
        /// <param name="recurrenceFrequencyId">The recurrence frequency ID</param>
        /// <returns>RecurrenceFrequencyDto or null if not found</returns>
        internal async Task<RecurrenceFrequencyDto?> GetAsync(short recurrenceFrequencyId)
        {
            string sql = @"
                SELECT [RecurrenceFrequencyId]
                      ,[Label]
                      ,[IsEnabled]
                      ,[SortOrder]
                FROM [workflow].[RecurrenceFrequency]
                WHERE [RecurrenceFrequencyId] = @recurrenceFrequencyId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("recurrenceFrequencyId", recurrenceFrequencyId);
                return await command.SelectSingle<RecurrenceFrequencyDto>();
            }
        }

        /// <summary>
        /// Get a list of RecurrenceFrequencies with filtering and paging
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="showDisabled">Include disabled records</param>
        /// <returns>List of RecurrenceFrequencies</returns>
        internal async Task<ListResponseDto<RecurrenceFrequencyListDto>> GetListAsync(StandardListParameters? standardListParameters = null, bool showDisabled = false)
        {
            if (standardListParameters == null)
            {
                standardListParameters = new StandardListParameters();
            }

            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "RecurrenceFrequencyId", "[RecurrenceFrequency].[RecurrenceFrequencyId]" },
                { "Label", "[RecurrenceFrequency].[Label]" },
                { "SortOrder", "[RecurrenceFrequency].[SortOrder]" },
                { "IsEnabled", "[RecurrenceFrequency].[IsEnabled]" }
            };

            string sql = @"
                SELECT [RecurrenceFrequencyId]
                      ,[Label]
                      ,[IsEnabled]
                      ,[SortOrder]
                FROM [workflow].[RecurrenceFrequency]
                WHERE 1=1";

            if (!showDisabled)
            {
                sql += " AND [IsEnabled] = 1";
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "SortOrder");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<RecurrenceFrequencyListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);
                result.List = await command.SelectMany<RecurrenceFrequencyListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[RecurrenceFrequency]
                WHERE 1=1";

            if (!showDisabled)
            {
                countSql += " AND [IsEnabled] = 1";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }
    }
}
