﻿using Redi.Prime3.MicroService.BaseLib;
using Sql;

namespace MicroserviceContract.Dtos.CRM
{
    [Mappable(nameof(PersonId))]
    public class BasePersonCDto : DtoBase
    {
        public Guid PersonId { get; set; }
        public string? FullName { get; set; }
        public string? FirstName { get; set; }
        public string? FamilyName { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public bool IsYearOfBirth { get; set; }
        public int? YearOfBirth { get; set; }
        public int? Age { get; set; }
        public string? Prefix { get; set; }
        public string? Suffix { get; set; }
        public Guid? MergedIntoPersonId { get; set; }
        public bool IsMerged { get; set; }
        public string? Gender { get; set; }
    }

    public class GetPersonCDto : BasePersonCDto
    {
        public Guid PartyId { get; set; }
    }

    public class GetListPersonCDto : BasePersonCDto {
        // Party
        public Guid PartyId { get; set; }
        public string? StatusCode { get; set; }
        public string? PartyType { get; set; }
        public string? Name { get;set; }
        public Guid? AvatarImageId { get; set; }
        public string? AvatarImageUrl { get; set; }
        public Guid? UserId { get; set; }

        // Contact
        public string? PrimaryEmail { get; set; }
        public string? PrimaryPhone { get; set; }
        public string? PrimaryAddress { get; set; }
    }
}
