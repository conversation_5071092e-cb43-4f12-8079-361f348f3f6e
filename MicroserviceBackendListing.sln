﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.32014.148
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MicroserviceBackendListing", "MicroserviceBackendListing\MicroserviceBackendListing.csproj", "{00760754-2E41-4200-86B5-AD7195E8CBB1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MicroserviceContract", "MicroserviceContract\MicroserviceContract.csproj", "{31DE3CDC-ABFF-4C8D-8F80-43752E7E5E41}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{114F81BB-62EB-42B4-8C1B-014933E4ABEB}"
	ProjectSection(SolutionItems) = preProject
		service.bicep = service.bicep
	EndProjectSection
EndProject
Project("{E53339B2-1760-4266-BCC7-CA923CBCF16C}") = "docker-compose", "local-deploy\docker-compose.dcproj", "{EB15ED5E-C733-4101-9BD0-E892E16C67F1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MicroserviceDashboardContract", "MicroserviceDashboardContract\MicroserviceDashboardContract.csproj", "{654E6B82-3FF3-4234-81B9-9E9B8B78F3C2}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Doco Root", "Doco Root", "{4BB9F886-6E36-455F-88D9-D9521E90C1A9}"
	ProjectSection(SolutionItems) = preProject
		README.md = README.md
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{00760754-2E41-4200-86B5-AD7195E8CBB1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{00760754-2E41-4200-86B5-AD7195E8CBB1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{00760754-2E41-4200-86B5-AD7195E8CBB1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{00760754-2E41-4200-86B5-AD7195E8CBB1}.Release|Any CPU.Build.0 = Release|Any CPU
		{31DE3CDC-ABFF-4C8D-8F80-43752E7E5E41}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{31DE3CDC-ABFF-4C8D-8F80-43752E7E5E41}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{31DE3CDC-ABFF-4C8D-8F80-43752E7E5E41}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{31DE3CDC-ABFF-4C8D-8F80-43752E7E5E41}.Release|Any CPU.Build.0 = Release|Any CPU
		{EB15ED5E-C733-4101-9BD0-E892E16C67F1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EB15ED5E-C733-4101-9BD0-E892E16C67F1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EB15ED5E-C733-4101-9BD0-E892E16C67F1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EB15ED5E-C733-4101-9BD0-E892E16C67F1}.Release|Any CPU.Build.0 = Release|Any CPU
		{654E6B82-3FF3-4234-81B9-9E9B8B78F3C2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{654E6B82-3FF3-4234-81B9-9E9B8B78F3C2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{654E6B82-3FF3-4234-81B9-9E9B8B78F3C2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{654E6B82-3FF3-4234-81B9-9E9B8B78F3C2}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {1488F3A2-BBC4-4F4E-A426-1BA31DDB438E}
	EndGlobalSection
EndGlobal
