﻿using Castle.Core.Internal;
using MicroserviceBackendListing.BusinessLogic;
using MicroserviceBackendListing.Dtos;

using MicroserviceContract.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Microsoft.AspNetCore.Mvc;
using Sql;

namespace MicroserviceBackendListing.Services
{
    /// <summary>
    /// Get bid information.
    /// </summary>
    [Route("api/Bidding")]
    public class BiddingController : AppController
    {
        private readonly BusinessLogic.Bidding _bidding;
        private readonly UtilityFunctions _utils;
        public BiddingController(BusinessLogic.Bidding bidding, IUnitOfWork unitOfWork, UtilityFunctions utils)
        {
            _bidding = bidding;
            _unitOfWork = unitOfWork;
            _utils = utils;
        }

        /// <summary>
        /// Get a Single Attribute
        /// </summary>
        /// <remarks>
        /// Returns current bidding attribute information for listing
        /// </remarks>
        /// <param name="listingId">A code for a listing record</param>
        /// <response code="200">Information returned, or an empty attribute if not found</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("GetCurrentBiddingInformation")]
        [ProducesResponseType(typeof(BiddingCDto), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetCurrentBiddingInformation(long listingId)
        {
            var bidding = _bidding;
            var result = await bidding.GetCurrentBiddingInformation(listingId);
            return Ok(result);
        }

        [HttpGet]
        [Route("GetPreviouslyBidStatus")]
        [ProducesResponseType(typeof(PreviouslyBidCDto), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetPreviouslyBidStatus(long listingId)
        {
            var bidding = _bidding;
            var result = await bidding.GetPreviouslyBidStatus(listingId, _utils.PartyId);
            return Ok(result);
        }
    }
}
