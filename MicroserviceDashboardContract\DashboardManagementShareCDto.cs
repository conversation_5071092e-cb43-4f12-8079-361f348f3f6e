﻿using Sql;

namespace MicroserviceDashboardContract.Dtos
{
    [Mappable(nameof(DashboardId))]
    public class BaseDashboardManagementShareCDto
    {
        public int DashboardShareId { get; set; }
        public Guid DashboardShareExternalId { get; set; }
        public int DashboardId { get; set; }
        public string? ShareLinkUrl { get; set; }
        public DateTimeOffset? ShareLinkAcceptedAt { get; set; }
        public DateTimeOffset SharelinkExpiresAt { get; set; }
        public string? ViewLinkUrl { get; set; }
        public DateTimeOffset ViewLinkExpiresAt { get; set; }
        public string? SharedWithAddress { get; set; }
        public int DashboardShareStatusId { get; set; }
        public DateTimeOffset CreatedOn { get; set; }
        public string? CreatedByName { get; set; }
        public DateTimeOffset? ModifiedOn { get; set; }
        public string? ModifiedByName { get; set; }
    }

    public class GetDashboardManagementShareCDto : BaseDashboardManagementShareCDto {  }
}
