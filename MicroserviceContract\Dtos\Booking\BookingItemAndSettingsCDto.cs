﻿using Redi.Prime3.MicroService.BaseLib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MicroserviceContract.Dtos.Booking
{
    public class BookingItemAndSettingsCDto
    {
        public RequestCDto<BookingItemRequestCDto>? BookingItem { get; set; }
        public RequestCDto<BookingItemAvailableRequestCDto>? BookingItemAvailable { get; set; }
    }
}
