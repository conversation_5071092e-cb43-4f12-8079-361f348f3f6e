﻿/************************************************************************************************
Standard Listing Records
Note! Some of these Listing records are created as Deleted. UNDELETE to enable
**************************************************************************************************/

-- Settings Page Display Container
IF NOT EXISTS (SELECT * from listing.DisplayContainer WHERE DisplayContainerCode = 'Settings')
BEGIN
    INSERT [listing].[DisplayContainer] ([DisplayContainerCode], [Title], [Description], [Enabled], [IsShowTitle], [Icon], [HelpText]) 
    VALUES (N'Settings', N'Settings', N'For settings listings to be display ', 1, 1, NULL, NULL);

    --Create an attribute for settings group this will be Teams & Company, Billing & subscription, Account 
    INSERT INTO [listing].[attribute]
    (
           [AttributeCode]
          ,[Label]
          ,[AttributeGroupCode]
          ,[AttributeValueTypeCode]
          ,[Description]
          ,[SortOrder]
          ,[IsEnabled]
          ,[IsManyAllowed]
          ,[Icon]
          ,[ReadAccessClaim]
          ,[WriteAccessClaim]
          ,[InputTypeCode]
          ,[NumericDecimalPlaces]
          ,[FilterGetModeCode]
          ,[IsRequired]
          ,[NumericMinValue]
          ,[NumericMaxValue]
          ,[TextMinCharacters]
          ,[TextMaxCharacters]
          ,[MinDateDaysFromToday]
          ,[MaxDateDaysFromToday]
          ,[MinSelectionsRequired]
          ,[MaxSelectionsAllowed]
          ,[MaxStars]
          ,[HelpText]
    )
    VALUES
    ( 
    'SettingsGroup',
    'Settings Group',
    'Default',
    N'ValueString',
    N'Add this attribute for listing to be added to the settings page',
    1,
    1,
    0, 
    NULL,
    NULL,
    NULL, 
    N'Text',
    NULL,
    N'Distinct',
    NULL, 
    NULL, 
    NULL, 
    NULL, 
    NULL, 
    NULL, 
    NULL, 
    NULL, 
    NULL, 
    NULL, 
    NULL
    );

    --Create an attribute for the onClick route
    INSERT INTO [listing].[attribute]
    (
           [AttributeCode]
          ,[Label]
          ,[AttributeGroupCode]
          ,[AttributeValueTypeCode]
          ,[Description]
          ,[SortOrder]
          ,[IsEnabled]
          ,[IsManyAllowed]
          ,[Icon]
          ,[ReadAccessClaim]
          ,[WriteAccessClaim]
          ,[InputTypeCode]
          ,[NumericDecimalPlaces]
          ,[FilterGetModeCode]
          ,[IsRequired]
          ,[NumericMinValue]
          ,[NumericMaxValue]
          ,[TextMinCharacters]
          ,[TextMaxCharacters]
          ,[MinDateDaysFromToday]
          ,[MaxDateDaysFromToday]
          ,[MinSelectionsRequired]
          ,[MaxSelectionsAllowed]
          ,[MaxStars]
          ,[HelpText]
    )
    VALUES
    ( 
    'OnClickValue',
    'On Click Value',
    'Default',
    N'ValueString',
    N'Add this attribute for listing to be added to the settings page',
    1,
    1,
    0, 
    NULL,
    NULL,
    NULL, 
    N'Text',
    NULL,
    N'Distinct',
    NULL, 
    NULL, 
    NULL, 
    NULL, 
    NULL, 
    NULL, 
    NULL, 
    NULL, 
    NULL, 
    NULL, 
    NULL
    );

    --Create a display container attribute for SettingsGroup, this is used to place listings in the settings page groups
    INSERT INTO [listing].[displayContainerAttribute]
    (
              [AttributeCode]
              ,[SortOrder]
              ,[IsReadOnly]
              ,[CreatedOn]
              ,[CreatedByName]
              ,[ModifiedOn]
              ,[ModifiedByName]
              ,[Deleted]
              ,[InputTypeCode]
              ,[DisplayContainerCode]
              ,[DefaultTextValue]
              ,[DefaultNumericValue]
              ,[DefaultDateAddDays]
              ,[DefaultDateAddMonths]
              ,[DefaultTimeAddMinutes]
               )
    VALUES
    ('SettingsGroup',1,0,CAST(N'2023-05-16T00:00:00.0000000+08:00' AS DateTimeOffset), N'admin',null,null,0,NULL,'Settings',NULL,NULL,NULL,NULL,NULL);

    --Provides the listing with an onClickValue 
    INSERT INTO [listing].[displayContainerAttribute]
    (
              [AttributeCode]
              ,[SortOrder]
              ,[IsReadOnly]
              ,[CreatedOn]
              ,[CreatedByName]
              ,[ModifiedOn]
              ,[ModifiedByName]
              ,[Deleted]
              ,[InputTypeCode]
              ,[DisplayContainerCode]
              ,[DefaultTextValue]
              ,[DefaultNumericValue]
              ,[DefaultDateAddDays]
              ,[DefaultDateAddMonths]
              ,[DefaultTimeAddMinutes]
               )
    VALUES
    ('OnClickValue',1,0,CAST(N'2023-05-16T00:00:00.0000000+08:00' AS DateTimeOffset), N'admin',null,null,0,NULL,'Settings',NULL,NULL,NULL,NULL,NULL);

    INSERT [listing].[Listing] (
         [Subject], [StatusCode], [VisibilityId], [ProfileListingMediaId], [ParentEntityId], [ParentEntityType], [CreatedOn], [CreatedByName], [ModifiedOn], [ModifiedByName], [Deleted], [FromDate], [ToDate], [Description], [GpsLocation], [ParentEntityId2], [ParentEntityType2], [ReferenceNo], [ListingTypeId], [TenantId], [ParentListingId], [SortOrder],[Icon]) 
    VALUES
        ( N'Users', N'Active', 1, NULL, N'c9b393d4-3701-42c4-bbb5-3552aaf70fca', N'Parent', CAST(N'2023-05-16T00:00:00.0000000+08:00' AS DateTimeOffset), N'admin', NULL, NULL, 0, CAST(N'2023-05-16T00:00:00.0000000+08:00' AS DateTimeOffset), CAST(N'2030-05-16T00:00:00.0000000+08:00' AS DateTimeOffset), N'Add/Edit individual users', NULL, NULL, NULL, N'Setting1', 0, NULL, NULL, 1,'users');


    insert  listing.ListingAttribute 
    (     [AttributeCode]
          ,[ValueString]
          ,[ValueNumeric]
          ,[ValueDateTime]
          ,[ValueStringMax]
          ,[ValueGeography]
          ,[ListingId]
    )
    VALUES
    ('SettingsGroup','Team & Company',NULL,NULL,NULL,NULL,IDENT_CURRENT( 'listing.listing' )),
    ('OnClickValue','/settings/users',NULL,NULL,NULL,NULL,IDENT_CURRENT( 'listing.listing' ));


    --Create a listing Users, give it a listing attribute for OnClickValue
    INSERT [listing].[Listing] (
         [Subject], [StatusCode], [VisibilityId], [ProfileListingMediaId], [ParentEntityId], [ParentEntityType], [CreatedOn], [CreatedByName], [ModifiedOn], [ModifiedByName], [Deleted], [FromDate], [ToDate], [Description], [GpsLocation], [ParentEntityId2], [ParentEntityType2], [ReferenceNo], [ListingTypeId], [TenantId], [ParentListingId], [SortOrder],[Icon]) 
    VALUES
        ( N'Roles & Permissions', N'Active',1, NULL, N'c9b393d4-3701-42c4-bbb5-3552aaf70fca', N'Parent', CAST(N'2023-05-16T00:00:00.0000000+08:00' AS DateTimeOffset), N'admin', NULL, NULL, 0, CAST(N'2023-05-16T00:00:00.0000000+08:00' AS DateTimeOffset), CAST(N'2030-05-16T00:00:00.0000000+08:00' AS DateTimeOffset), N'Organise users into groups and their access levels/permissions', NULL, NULL, NULL, N'Setting1', 0, NULL, NULL, 1,'user-lock');

    INSERT [listing].[Listing] (
         [Subject], [StatusCode], [VisibilityId], [ProfileListingMediaId], [ParentEntityId], [ParentEntityType], [CreatedOn], [CreatedByName], [ModifiedOn], [ModifiedByName], [Deleted], [FromDate], [ToDate], [Description], [GpsLocation], [ParentEntityId2], [ParentEntityType2], [ReferenceNo], [ListingTypeId], [TenantId], [ParentListingId], [SortOrder]) 
    VALUES
        ( N'Company', N'Active', 0, NULL, N'c9b393d4-3701-42c4-bbb5-3552aaf70fca', N'Parent', CAST(N'2023-05-16T00:00:00.0000000+08:00' AS DateTimeOffset), N'admin', NULL, NULL, 0, CAST(N'2023-05-16T00:00:00.0000000+08:00' AS DateTimeOffset), CAST(N'2030-05-16T00:00:00.0000000+08:00' AS DateTimeOffset), N'Core details about the company that effect your software environment', NULL, NULL, NULL, N'Setting3', 0, NULL, NULL, 1);

    insert  listing.ListingAttribute 
    (     [AttributeCode]
          ,[ValueString]
          ,[ValueNumeric]
          ,[ValueDateTime]
          ,[ValueStringMax]
          ,[ValueGeography]
          ,[ListingId]
    )
    VALUES
    ('SettingsGroup','Account',NULL,NULL,NULL,NULL,IDENT_CURRENT( 'listing.listing' )) ,
    ('OnClickValue','/settings/company',NULL,NULL,NULL,NULL,IDENT_CURRENT( 'listing.listing' ));

    -- Create 'Your Data as a Listing record that is DELETED. UnDelete to enable
    INSERT [listing].[Listing] (
         [Subject], [StatusCode], [VisibilityId], [ProfileListingMediaId], [ParentEntityId], [ParentEntityType], [CreatedOn], [CreatedByName], [ModifiedOn], [ModifiedByName], [Deleted], [FromDate], [ToDate], [Description], [GpsLocation], [ParentEntityId2], [ParentEntityType2], [ReferenceNo], [ListingTypeId], [TenantId], [ParentListingId], [SortOrder]) 
    VALUES
        ( N'Your Data', N'Active', 0, NULL, N'c9b393d4-3701-42c4-bbb5-3552aaf70fca', N'Parent', CAST(N'2023-05-16T00:00:00.0000000+08:00' AS DateTimeOffset), N'admin', NULL, NULL, 1, CAST(N'2023-05-16T00:00:00.0000000+08:00' AS DateTimeOffset), CAST(N'2030-05-16T00:00:00.0000000+08:00' AS DateTimeOffset), N'Your data and where it is hosted', NULL, NULL, NULL, N'Setting4', 0, NULL, NULL, 1)


    insert  listing.ListingAttribute 
    (     [AttributeCode]
          ,[ValueString]
          ,[ValueNumeric]
          ,[ValueDateTime]
          ,[ValueStringMax]
          ,[ValueGeography]
          ,[ListingId]
    )
    VALUES
    ('SettingsGroup','Account',NULL,NULL,NULL,NULL,IDENT_CURRENT( 'listing.listing' )) ,
    ('OnClickValue','/settings/yourdata',NULL,NULL,NULL,NULL,IDENT_CURRENT( 'listing.listing' ));


    -- Create 'Delete Account and Data as a Listing record that is DELETED. UnDelete to enable
    INSERT [listing].[Listing] (
         [Subject], [StatusCode], [VisibilityId], [ProfileListingMediaId], [ParentEntityId], [ParentEntityType], [CreatedOn], [CreatedByName], [ModifiedOn], [ModifiedByName], [Deleted], [FromDate], [ToDate], [Description], [GpsLocation], [ParentEntityId2], [ParentEntityType2], [ReferenceNo], [ListingTypeId], [TenantId], [ParentListingId], [SortOrder]) 
    VALUES
        ( N'Delete Account & Data', N'Active', 0, NULL, N'c9b393d4-3701-42c4-bbb5-3552aaf70fca', N'Parent', CAST(N'2023-05-16T00:00:00.0000000+08:00' AS DateTimeOffset), N'admin', NULL, NULL, 1, CAST(N'2023-05-16T00:00:00.0000000+08:00' AS DateTimeOffset), CAST(N'2030-05-16T00:00:00.0000000+08:00' AS DateTimeOffset), N'Safely remove your account and data', NULL, NULL, NULL, N'Setting4', 0, NULL, NULL, 1)


    insert  listing.ListingAttribute 
    (     [AttributeCode]
          ,[ValueString]
          ,[ValueNumeric]
          ,[ValueDateTime]
          ,[ValueStringMax]
          ,[ValueGeography]
          ,[ListingId]
    )
    VALUES
    ('SettingsGroup','Account',NULL,NULL,NULL,NULL,IDENT_CURRENT( 'listing.listing' )) ,
    ('OnClickValue','/settings/deleteaccount',NULL,NULL,NULL,NULL,IDENT_CURRENT( 'listing.listing' ));
END
GO

If Not Exists (Select * from [Listing].SortOptions where Label = 'A-Z')
BEGIN
    INSERT INTO [Listing].SortOptions 
    (SortOptionId, [Label], SqlSortStmt,SortOrder,IsEnabled)
    Values
    (300,'A-Z','[Listing].[Subject] ASC',1,1),
    (301,'Z-A','[Listing].[Subject] DESC',2,1);
END
GO

-- Add Dashboards
IF NOT EXISTS(SELECT 1 FROM [listing].[DisplayContainer] WHERE [DisplayContainerCode] = 'DashboardStruct')
    INSERT INTO [listing].[DisplayContainer]([DisplayContainerCode],	[Title],	[Description],	[Enabled],	[IsShowTitle],	[Icon],	[HelpText])
    VALUES
    ('DashboardStruct',	'Dashboard Structure',	'Dashboard Structure',	1,	1,	NULL,	NULL),
    ('ListingDetail',	'Listing Detail',	'Listing Detail',	1,	1,	NULL,	NULL)

GO

IF NOT EXISTS(SELECT 1 FROM [listing].[Attribute] WHERE [AttributeCode] = 'FolderName')
    INSERT INTO [listing].[Attribute]([AttributeCode],[Label],[AttributeGroupCode],[AttributeValueTypeCode],[Description],[SortOrder],[IsEnabled],[IsManyAllowed],[Icon],[ReadAccessClaim],[WriteAccessClaim],[InputTypeCode],[NumericDecimalPlaces],[FilterGetModeCode],[IsRequired],[NumericMinValue],[NumericMaxValue],[TextMinCharacters],[TextMaxCharacters],[MinDateDaysFromToday],[MaxDateDaysFromToday],[MinSelectionsRequired],[MaxSelectionsAllowed],[MaxStars],[HelpText])
    VALUES
    ('FolderName',	'Folder Name',	'Default',	'ValueString',	'Folder Name',	1,	1,	1,	NULL,	NULL,	NULL,	'Dropdown',	NULL,	'Distinct',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL),
    ('DashboardConfig',	'Dashboard Config',	'Default',	'ValueStringMax',	'Dashboard Config',	1,	1,	1,	NULL,	NULL,	NULL,	'TextArea',	NULL,	'None',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL),
    ('DashboardConfigVersion',	'Dashboard Config Version',	'Default',	'ValueNumeric',	'Dashboard Config Version',	1,	1,	1,	NULL,	NULL,	NULL,	'Integer',	NULL,	'None',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL),
    ('DashboardHelpText',	'Dashboard Help Text',	'Default',	'ValueString',	'Dashboard Help Text',	1,	1,	1,	NULL,	NULL,	NULL,	'Text',	NULL,	'None',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL),
    ('IsDashboardDisplayAtRootLevel',	'Is Dashboard Display At RootLevel',	'Default',	'ValueString',	'Is Dashboard Display At RootLevel',	1,	1,	1,	NULL,	NULL,	NULL,	'Checkbox',	NULL,	'None',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL),
    ('IsDashboardTemplate',	'Is Dashboard Template',	'Default',	'ValueString',	'Is Dashboard Template',	1,	1,	1,	NULL,	NULL,	NULL,	'Checkbox',	NULL,	'None',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL),
    ('InheritedFromTemplateDashboardId',	'Inherited From Template Dashboard Id',	'Default',	'ValueString',	'Inherited From Template Dashboard Id',	1,	1,	1,	NULL,	NULL,	NULL,	'Checkbox',	NULL,	'None',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL)

GO

IF NOT EXISTS(SELECT 1 FROM [listing].[DisplayContainerAttribute] WHERE [AttributeCode] = 'DashboardConfig')
    INSERT INTO [listing].[DisplayContainerAttribute]
               ([AttributeCode]
               ,[SortOrder]
               ,[IsReadOnly]
               ,[CreatedOn]
               ,[CreatedByName]
               ,[ModifiedOn]
               ,[ModifiedByName]
               ,[Deleted]
               ,[InputTypeCode]
               ,[DisplayContainerCode]
               ,[DefaultTextValue]
               ,[DefaultNumericValue]
               ,[DefaultDateAddDays]
               ,[DefaultDateAddMonths]
               ,[DefaultTimeAddMinutes])
    VALUES
    ('FolderName',	1,	0,	GETUTCDATE(),	'admin',	NULL,	NULL,	0,	NULL,	'FolderFilter',	NULL,	NULL,	NULL,	NULL,	NULL),
    ('FolderName',	1,	0,	GETUTCDATE(),	'admin',	NULL,	NULL,	0,	NULL,	'ListingDetail',	NULL,	NULL,	NULL,	NULL,	NULL),
    ('DashboardConfig',	1,	0,	GETUTCDATE(),	'admin',	NULL,	NULL,	0,	NULL,	'DashboardStruct',	NULL,	NULL,	NULL,	NULL,	NULL),
    ('DashboardConfigVersion',	1,	0,	GETUTCDATE(),	'admin',	NULL,	NULL,	0,	NULL,	'DashboardStruct',	NULL,	0,	NULL,	NULL,	NULL),
    ('DashboardHelpText',	1,	0,	GETUTCDATE(),	'admin',	NULL,	NULL,	0,	NULL,	'DashboardStruct',	NULL,	NULL,	NULL,	NULL,	NULL),
    ('IsDashboardDisplayAtRootLevel',	1,	0,	GETUTCDATE(),	'admin',	NULL,	NULL,	0,	NULL,	'DashboardStruct',	'false',	NULL,	NULL,	NULL,	NULL),
    ('IsDashboardTemplate',	1,	0,	GETUTCDATE(),	'admin',	NULL,	NULL,	0,	NULL,	'DashboardStruct',	'false',	NULL,	NULL,	NULL,	NULL),
    ('InheritedFromTemplateDashboardId',	1,	0,	GETUTCDATE(),	'admin',	NULL,	NULL,	0,	NULL,	'DashboardStruct',	NULL,	NULL,	NULL,	NULL,	NULL)
GO

IF NOT EXISTS (SELECT * from [listing].[MediaCategory] WHERE [MediaCategoryCode] = 'Listings')
    INSERT INTO [listing].[MediaCategory]
    ([MediaCategoryCode], [Label], [SortOrder], [IsEnabled])
    VALUES
    ('Listings','Listings',1,1);

GO