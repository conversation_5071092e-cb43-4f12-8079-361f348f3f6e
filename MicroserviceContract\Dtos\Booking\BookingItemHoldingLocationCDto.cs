﻿using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MicroserviceContract.Dtos.Booking
{
    [Mappable(nameof(BookingItemHoldingLocationId))]
    public class BookingItemHoldingLocationRequestCDto
    {
        public int? BookingItemHoldingLocationId { get; set; }
        public int? BookingItemId { get; set; }
        public DateTimeOffset FromDateTime { get; set; }
        public DateTimeOffset? ToDateTime { get; set; }
        public int HoldingLocationId { get; set; }
    }

    [Mappable(nameof(BookingItemHoldingLocationId))]
    public class BookingItemHoldingLocationResponseCDto : DtoBase
    {
        public int? BookingItemHoldingLocationId { get; set; }
        public int? BookingItemId { get; set; }
        public DateTimeOffset FromDateTime { get; set; }
        public DateTimeOffset? ToDateTime { get; set; }
        public int HoldingLocationId { get; set; }
        public string? LocationName { get; set; }
    }
}
