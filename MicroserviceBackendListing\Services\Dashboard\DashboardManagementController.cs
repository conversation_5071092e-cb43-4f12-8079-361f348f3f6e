﻿using MicroserviceBackendListing.BusinessLogic;
using MicroserviceBackendListing.Policies;
using MicroserviceContract.Dtos.ListingManagement;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sql;

namespace MicroserviceBackendListing.Services
{
    /// <summary>
    /// Allows Creation, Fetching and Management of Dashboards and Folders.
    /// For general searching and public facing requests use the Dashboard endpoint.
    /// </summary>
    [Route("api/DashboardManagement")]
    public class DashboardManagementController : AppController
    {
        private Func<DashboardManagementConfig> _managementFactory;

        public DashboardManagementController(IUnitOfWork unitOfWork, Func<DashboardManagementConfig> managementFactory)
        {
            _unitOfWork = unitOfWork;
            _managementFactory = managementFactory;
        }

        /// <summary>
        /// Update Dashboard's Config
        /// </summary>
        /// <param name="listingId"></param>
        /// <param name="attributes"></param>
        /// <response code="200"></response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("UpdateConfig")]
        [Authorize(Policy = PolicyType.ViewDashboardListingPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> UpdateConfig([FromQuery]Int64 listingId, [FromBody] List<ManageListingAttributeCDto> attributes)
        {
            await _managementFactory().UpdateConfigAsync(listingId, attributes);
            _unitOfWork.Commit();

            return Ok();
        }

        /// <summary>
        /// Create Dashboard (with optional Config on initial create)
        /// </summary>
        /// <param name="listingId"></param>
        /// <param name="attributes"></param>
        /// <response code="200"></response>
        /// <response code="422">Request validation error</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("CreateConfig")]
        [Authorize(Policy = PolicyType.ViewDashboardListingPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> CreateConfig([FromQuery] Int64 listingId, [FromBody] List<ManageListingAttributeCDto> attributes)
        {
            await _managementFactory().CreateConfigAsync(listingId, attributes);
            _unitOfWork.Commit();

            return Ok();
        }
    }
}
