using Redi.Prime3.MicroService.BaseLib;

namespace MicroserviceBackendListing.BusinessLogic
{
    public static class Config
    {
        public static string AzureContainer => ConfigBase.GetConfigValue("AzureContainer", defaultTo: "default");
        public static string AzureFilesBase => ConfigBase.GetConfigValue("AzureFilesBase", defaultTo: "https://rediteststorage.blob.core.windows.net/");
        public static int? BookingDefaultMinimumGapBetweenBookingsMinutes => ConfigBase.GetConfigIntValue("BookingDefaultMinimumGapBetweenBookingsMinutes");
        public static int? BookingDefaultMinimumBookingTimeInMinutes => ConfigBase.GetConfigIntValue("BookingDefaultMinimumBookingTimeInMinutes");
        public static int? BookingDefaultMaximumBookingTimeInMinutes => ConfigBase.GetConfigIntValue("BookingDefaultMaximumBookingTimeInMinutes");
        public static int? BookingDefaultMaximumBookingIntoFutureDays => ConfigBase.GetConfigIntValue("BookingDefaultMaximumBookingIntoFutureDays");
        public static bool BookingDefaultIsRecurringBookingEnabled => ConfigBase.GetConfigBoolValue("BookingDefaultIsRecurringBookingEnabled");



    }
}